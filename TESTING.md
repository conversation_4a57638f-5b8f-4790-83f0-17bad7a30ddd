# Testing Guide

This document provides comprehensive information about testing the SEO Indexer Platform.

## Table of Contents

- [Overview](#overview)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Test Types](#test-types)
- [Writing Tests](#writing-tests)
- [Code Coverage](#code-coverage)
- [Continuous Integration](#continuous-integration)
- [Performance Testing](#performance-testing)
- [Security Testing](#security-testing)

## Overview

The SEO Indexer Platform uses PHPUnit for testing with a comprehensive test suite that includes:

- **Unit Tests**: Test individual classes and methods in isolation
- **Integration Tests**: Test how different components work together
- **Feature Tests**: Test complete user workflows and features
- **Security Tests**: Test security measures and vulnerability protection

## Test Structure

```
tests/
├── bootstrap.php           # Test bootstrap and setup
├── TestCase.php           # Base test case class
├── Unit/                  # Unit tests
│   └── Services/
│       ├── AuthServiceTest.php
│       └── SecurityServiceTest.php
├── Integration/           # Integration tests
│   └── UrlSubmissionTest.php
├── Feature/              # Feature tests
│   └── UserWorkflowTest.php
├── Security/             # Security tests
│   └── SecurityMiddlewareTest.php
├── coverage/             # Coverage reports
└── results/              # Test results
```

## Running Tests

### Prerequisites

1. Install dependencies:
```bash
composer install --dev
```

2. Set up test database:
```bash
mysql -u root -p -e "CREATE DATABASE seo_indexer_test;"
```

3. Copy environment file:
```bash
cp .env.example .env
# Update .env with test database credentials
```

### Running All Tests

```bash
# Using Composer scripts
composer test

# Using PHPUnit directly
vendor/bin/phpunit

# Using the test runner script
./run-tests.sh
```

### Running Specific Test Suites

```bash
# Unit tests only
composer run-script test:unit
./run-tests.sh --unit-only

# Integration tests only
composer run-script test:integration
./run-tests.sh --integration-only

# Feature tests only
composer run-script test:feature
./run-tests.sh --feature-only

# Security tests only
composer run-script test:security
./run-tests.sh --security-only
```

### Running with Coverage

```bash
# Generate HTML coverage report
composer run-script test:coverage
./run-tests.sh --coverage

# Generate text coverage report
composer run-script test:coverage-text
```

## Test Types

### Unit Tests

Unit tests focus on testing individual classes and methods in isolation.

**Example:**
```php
public function testPasswordHashing(): void
{
    $password = 'TestPassword123!';
    $hash = $this->security->hashPassword($password);

    $this->assertNotEquals($password, $hash);
    $this->assertTrue($this->security->verifyPassword($password, $hash));
}
```

**Key Characteristics:**
- Fast execution
- No external dependencies
- Test single units of functionality
- Use mocks for dependencies

### Integration Tests

Integration tests verify that different components work together correctly.

**Example:**
```php
public function testSubmitSingleUrlToGoogle(): void
{
    $user = $this->createUser();
    $domain = $this->createDomain($user['id']);
    $this->createOAuthToken($user['id'], 'google');

    $result = $this->urlSubmissionService->submitUrl($user['id'], $domain['id'], $url, ['google']);

    $this->assertTrue($result['success']);
    $this->assertDatabaseHas('url_submissions', [
        'user_id' => $user['id'],
        'url' => $url,
        'provider' => 'google'
    ]);
}
```

**Key Characteristics:**
- Test component interactions
- Use real database connections
- Mock external API calls
- Verify data persistence

### Feature Tests

Feature tests validate complete user workflows and business processes.

**Example:**
```php
public function testCompleteUserRegistrationAndLoginWorkflow(): void
{
    // Step 1: User registration
    $registrationResult = $this->auth->register($registrationData);
    $this->assertTrue($registrationResult['success']);

    // Step 2: User login
    $loginResult = $this->auth->login('<EMAIL>', 'SecurePassword123!');
    $this->assertTrue($loginResult['success']);

    // Step 3: Verify user session
    $this->assertTrue($this->auth->isLoggedIn());
}
```

**Key Characteristics:**
- Test end-to-end workflows
- Simulate real user interactions
- Verify business logic
- Test multiple components together

### Security Tests

Security tests verify that security measures work correctly and protect against vulnerabilities.

**Example:**
```php
public function testBlocksRequestFromBlockedIp(): void
{
    $ipAddress = '*************';
    $this->security->blockIp($ipAddress, 'Test block');

    $_SERVER['REMOTE_ADDR'] = $ipAddress;
    $result = $this->middleware->handle('GET', '/dashboard');

    $this->assertEquals(403, $result['status']);
    $this->assertStringContainsString('blocked', $result['error']);
}
```

**Key Characteristics:**
- Test security controls
- Verify threat protection
- Test rate limiting
- Validate input sanitization

## Writing Tests

### Base Test Case

All tests extend the `TestCase` class which provides:

- Database setup and cleanup
- Helper methods for creating test data
- Assertion helpers
- Mock utilities

### Test Data Creation

Use the provided factory methods:

```php
// Create test user
$user = $this->createUser([
    'email' => '<EMAIL>',
    'role' => 'admin'
]);

// Create test domain
$domain = $this->createDomain($user['id'], [
    'domain' => 'example.com',
    'verified' => true
]);

// Create URL submission
$submission = $this->createUrlSubmission($user['id'], $domain['id']);
```

### Database Assertions

```php
// Assert record exists
$this->assertDatabaseHas('users', [
    'email' => '<EMAIL>',
    'status' => 'active'
]);

// Assert record doesn't exist
$this->assertDatabaseMissing('users', [
    'email' => '<EMAIL>'
]);

// Assert record count
$this->assertDatabaseCount('url_submissions', 5);
```

### Security Testing

```php
// Test rate limiting
for ($i = 0; $i < 10; $i++) {
    $this->security->recordAttempt('test-user', 'test-action');
}
$result = $this->security->checkRateLimit('test-user', 'test-action', 5, 60);
$this->assertFalse($result['allowed']);

// Test input sanitization
$maliciousInput = '<script>alert("XSS")</script>';
$sanitized = $this->security->sanitizeInput($maliciousInput, 'html');
$this->assertStringNotContainsString('<script>', $sanitized);
```

## Code Coverage

### Generating Coverage Reports

```bash
# HTML report (recommended for development)
composer run-script test:coverage

# Text report (for CI/CD)
composer run-script test:coverage-text
```

### Coverage Targets

- **Overall Coverage**: > 80%
- **Critical Components**: > 90%
  - AuthService
  - SecurityService
  - UrlSubmissionService
- **Controllers**: > 70%
- **Models**: > 85%

### Viewing Coverage Reports

HTML reports are generated in `tests/coverage/html/` and can be viewed in a browser.

## Continuous Integration

### GitHub Actions

The project uses GitHub Actions for CI/CD with the following workflow:

1. **Test Matrix**: Tests against PHP 8.0, 8.1, 8.2
2. **Database Setup**: MySQL 8.0 service
3. **Test Execution**: All test suites
4. **Coverage Reports**: Uploaded to Codecov
5. **Static Analysis**: PHPStan
6. **Code Style**: PHP CodeSniffer
7. **Security Scan**: Composer audit
8. **Performance Tests**: Critical performance tests

### Local CI Simulation

```bash
# Run the complete CI pipeline locally
./run-tests.sh --coverage
composer run-script phpstan
composer run-script phpcs
```

## Performance Testing

### Performance Assertions

```php
// Assert execution time
$this->assertExecutionTimeUnder(function() {
    $this->urlSubmissionService->submitUrlBatch($userId, $domainId, $urls, ['google']);
}, 5.0); // 5 seconds max

// Measure execution time
$executionTime = $this->measureExecutionTime(function() {
    // Code to measure
});
```

### Performance Targets

- **User Registration**: < 500ms
- **User Login**: < 200ms
- **Single URL Submission**: < 2s
- **Batch URL Submission (10 URLs)**: < 5s
- **Dashboard Load**: < 1s
- **Security Middleware**: < 50ms per request

## Security Testing

### Security Test Categories

1. **Authentication Security**
   - Brute force protection
   - Account lockout
   - Password strength validation

2. **Input Validation**
   - XSS prevention
   - SQL injection protection
   - CSRF token validation

3. **Rate Limiting**
   - API endpoint limits
   - Authentication limits
   - General request limits

4. **Access Control**
   - Authorization checks
   - Role-based access
   - IP blocking

### Security Assertions

```php
// Assert security event was logged
$this->assertSecurityEventLogged('suspicious_activity', $userId);

// Assert rate limit was recorded
$this->assertRateLimitRecorded('user:123', 'login');

// Assert security response
$this->assertSecurityBlocked($response);
```

## Best Practices

### Test Organization

1. **One test per behavior**
2. **Descriptive test names**
3. **Arrange-Act-Assert pattern**
4. **Clean up after tests**

### Test Data

1. **Use factory methods**
2. **Create minimal test data**
3. **Avoid test interdependencies**
4. **Clean database between tests**

### Assertions

1. **Use specific assertions**
2. **Test both positive and negative cases**
3. **Verify side effects**
4. **Check error conditions**

### Performance

1. **Keep tests fast**
2. **Use database transactions**
3. **Mock external services**
4. **Parallel test execution**

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check test database credentials
   - Ensure test database exists
   - Verify MySQL service is running

2. **Test Failures**
   - Check test isolation
   - Verify test data setup
   - Review assertion logic

3. **Coverage Issues**
   - Ensure Xdebug is installed
   - Check file permissions
   - Verify coverage configuration

### Debug Mode

```bash
# Run tests with verbose output
./run-tests.sh --verbose

# Run specific test with debug
vendor/bin/phpunit --debug tests/Unit/Services/AuthServiceTest.php
```

## Contributing

When contributing tests:

1. **Write tests for new features**
2. **Maintain existing test coverage**
3. **Follow naming conventions**
4. **Update documentation**
5. **Run full test suite before submitting**

For more information, see the main [README.md](README.md) and [CONTRIBUTING.md](CONTRIBUTING.md) files.
