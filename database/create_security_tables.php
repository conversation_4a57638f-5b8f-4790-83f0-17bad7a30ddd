<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

try {
    $socket = $_ENV['DB_SOCKET'] ?? '';
    $dbname = $_ENV['DB_NAME'] ?? 'indexation-tool';
    $username = $_ENV['DB_USER'] ?? 'root';
    $password = $_ENV['DB_PASS'] ?? 'root';

    if (!empty($socket) && file_exists($socket)) {
        $dsn = "mysql:unix_socket={$socket};dbname={$dbname};charset=utf8mb4";
    } else {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '8889';
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    }

    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    echo "✓ Connected to MySQL server\n";

    // Create additional security tables
    $tables = [
        'security_logs' => "
            CREATE TABLE IF NOT EXISTS security_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                event_type VARCHAR(100) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                user_id INT NULL,
                event_data JSON,
                severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_security_logs_event_type (event_type),
                INDEX idx_security_logs_ip (ip_address),
                INDEX idx_security_logs_user_id (user_id),
                INDEX idx_security_logs_severity (severity),
                INDEX idx_security_logs_created_at (created_at)
            )
        ",
        'login_attempts' => "
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INT PRIMARY KEY AUTO_INCREMENT,
                identifier VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                success BOOLEAN DEFAULT FALSE,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_login_attempts_identifier (identifier),
                INDEX idx_login_attempts_ip (ip_address),
                INDEX idx_login_attempts_created_at (created_at)
            )
        ",
        'password_reset_tokens' => "
            CREATE TABLE IF NOT EXISTS password_reset_tokens (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL UNIQUE,
                ip_address VARCHAR(45) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_password_reset_token (token),
                INDEX idx_password_reset_user_id (user_id),
                INDEX idx_password_reset_expires (expires_at)
            )
        ",
        'email_verification_tokens' => "
            CREATE TABLE IF NOT EXISTS email_verification_tokens (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_email_verification_token (token),
                INDEX idx_email_verification_user_id (user_id),
                INDEX idx_email_verification_expires (expires_at)
            )
        ",
        'session_security' => "
            CREATE TABLE IF NOT EXISTS session_security (
                id INT PRIMARY KEY AUTO_INCREMENT,
                session_id VARCHAR(255) NOT NULL,
                user_id INT NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent_hash VARCHAR(64) NOT NULL,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_session_security_session_id (session_id),
                INDEX idx_session_security_user_id (user_id),
                INDEX idx_session_security_last_activity (last_activity)
            )
        ",
        'api_key_security' => "
            CREATE TABLE IF NOT EXISTS api_key_security (
                id INT PRIMARY KEY AUTO_INCREMENT,
                api_key_hash VARCHAR(255) NOT NULL UNIQUE,
                user_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                permissions JSON,
                rate_limit_per_hour INT DEFAULT 1000,
                last_used_at TIMESTAMP NULL,
                expires_at TIMESTAMP NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                INDEX idx_api_key_hash (api_key_hash),
                INDEX idx_api_key_user_id (user_id),
                INDEX idx_api_key_active (is_active)
            )
        ",
        'user_security_settings' => "
            CREATE TABLE IF NOT EXISTS user_security_settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL UNIQUE,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret VARCHAR(255) NULL,
                backup_codes JSON NULL,
                login_notifications BOOLEAN DEFAULT TRUE,
                session_timeout_minutes INT DEFAULT 120,
                password_changed_at TIMESTAMP NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ",
        'audit_trail' => "
            CREATE TABLE IF NOT EXISTS audit_trail (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NULL,
                action VARCHAR(100) NOT NULL,
                resource_type VARCHAR(50) NOT NULL,
                resource_id INT NULL,
                old_values JSON NULL,
                new_values JSON NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_audit_trail_user_id (user_id),
                INDEX idx_audit_trail_action (action),
                INDEX idx_audit_trail_resource (resource_type, resource_id),
                INDEX idx_audit_trail_created_at (created_at)
            )
        "
    ];

    foreach ($tables as $name => $sql) {
        try {
            $pdo->exec($sql);
            echo "✓ Created table: $name\n";
        } catch (Exception $e) {
            echo "✗ Error creating $name: " . $e->getMessage() . "\n";
        }
    }

    // Add security columns to users table if they don't exist
    $userColumns = [
        'failed_login_attempts' => 'ALTER TABLE users ADD COLUMN failed_login_attempts INT DEFAULT 0',
        'locked_until' => 'ALTER TABLE users ADD COLUMN locked_until TIMESTAMP NULL',
        'last_login_at' => 'ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL',
        'last_login_ip' => 'ALTER TABLE users ADD COLUMN last_login_ip VARCHAR(45) NULL',
        'password_changed_at' => 'ALTER TABLE users ADD COLUMN password_changed_at TIMESTAMP NULL',
        'two_factor_enabled' => 'ALTER TABLE users ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE'
    ];

    echo "\nAdding security columns to users table:\n";
    foreach ($userColumns as $column => $sql) {
        try {
            $pdo->exec($sql);
            echo "✓ Added column: $column\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column') !== false) {
                echo "- Column $column already exists\n";
            } else {
                echo "✗ Error adding $column: " . $e->getMessage() . "\n";
            }
        }
    }

    echo "\n✓ Security tables creation completed!\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
