<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

try {
    $socket = $_ENV['DB_SOCKET'] ?? '';
    $dbname = $_ENV['DB_NAME'] ?? 'indexation-tool';
    $username = $_ENV['DB_USER'] ?? 'root';
    $password = $_ENV['DB_PASS'] ?? 'root';

    if (!empty($socket) && file_exists($socket)) {
        $dsn = "mysql:unix_socket={$socket};dbname={$dbname};charset=utf8mb4";
    } else {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '8889';
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    }

    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    echo "✓ Connected to MySQL server\n";

    // Create performance tables
    $tables = [
        'performance_metrics' => "
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                value DECIMAL(15,6) NOT NULL,
                type ENUM('gauge', 'counter', 'timing', 'histogram') DEFAULT 'gauge',
                timestamp INT NOT NULL,
                date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSON,
                INDEX idx_performance_metrics_name (name),
                INDEX idx_performance_metrics_date (date),
                INDEX idx_performance_metrics_type (type)
            )
        ",
        'query_performance' => "
            CREATE TABLE IF NOT EXISTS query_performance (
                id INT PRIMARY KEY AUTO_INCREMENT,
                query_hash VARCHAR(64) NOT NULL,
                query_text TEXT NOT NULL,
                execution_time DECIMAL(10,6) NOT NULL,
                rows_examined INT DEFAULT 0,
                rows_sent INT DEFAULT 0,
                memory_used INT DEFAULT 0,
                user_id INT NULL,
                endpoint VARCHAR(255) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_query_performance_hash (query_hash),
                INDEX idx_query_performance_time (execution_time),
                INDEX idx_query_performance_created_at (created_at),
                INDEX idx_query_performance_endpoint (endpoint)
            )
        ",
        'endpoint_performance' => "
            CREATE TABLE IF NOT EXISTS endpoint_performance (
                id INT PRIMARY KEY AUTO_INCREMENT,
                endpoint VARCHAR(255) NOT NULL,
                method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL,
                response_time DECIMAL(10,6) NOT NULL,
                memory_usage INT NOT NULL,
                status_code INT NOT NULL,
                user_id INT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_endpoint_performance_endpoint (endpoint),
                INDEX idx_endpoint_performance_method (method),
                INDEX idx_endpoint_performance_response_time (response_time),
                INDEX idx_endpoint_performance_created_at (created_at),
                INDEX idx_endpoint_performance_status (status_code)
            )
        ",
        'cache_performance' => "
            CREATE TABLE IF NOT EXISTS cache_performance (
                id INT PRIMARY KEY AUTO_INCREMENT,
                cache_key VARCHAR(255) NOT NULL,
                operation ENUM('get', 'set', 'delete', 'clear') NOT NULL,
                hit BOOLEAN DEFAULT FALSE,
                execution_time DECIMAL(10,6) NOT NULL,
                data_size INT DEFAULT 0,
                ttl INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_cache_performance_key (cache_key),
                INDEX idx_cache_performance_operation (operation),
                INDEX idx_cache_performance_hit (hit),
                INDEX idx_cache_performance_created_at (created_at)
            )
        ",
        'connection_pool_stats' => "
            CREATE TABLE IF NOT EXISTS connection_pool_stats (
                id INT PRIMARY KEY AUTO_INCREMENT,
                active_connections INT NOT NULL,
                idle_connections INT NOT NULL,
                total_connections INT NOT NULL,
                max_connections INT NOT NULL,
                connection_errors INT DEFAULT 0,
                avg_connection_time DECIMAL(10,6) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_connection_pool_created_at (created_at)
            )
        ",
        'system_resources' => "
            CREATE TABLE IF NOT EXISTS system_resources (
                id INT PRIMARY KEY AUTO_INCREMENT,
                cpu_usage DECIMAL(5,2) NOT NULL,
                memory_usage DECIMAL(15,0) NOT NULL,
                memory_total DECIMAL(15,0) NOT NULL,
                disk_usage DECIMAL(15,0) NOT NULL,
                disk_total DECIMAL(15,0) NOT NULL,
                load_average_1min DECIMAL(5,2) DEFAULT NULL,
                load_average_5min DECIMAL(5,2) DEFAULT NULL,
                load_average_15min DECIMAL(5,2) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_system_resources_created_at (created_at)
            )
        ",
        'performance_alerts' => "
            CREATE TABLE IF NOT EXISTS performance_alerts (
                id INT PRIMARY KEY AUTO_INCREMENT,
                alert_type VARCHAR(100) NOT NULL,
                severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
                message TEXT NOT NULL,
                metric_name VARCHAR(255) NOT NULL,
                metric_value DECIMAL(15,6) NOT NULL,
                threshold_value DECIMAL(15,6) NOT NULL,
                resolved BOOLEAN DEFAULT FALSE,
                resolved_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_performance_alerts_type (alert_type),
                INDEX idx_performance_alerts_severity (severity),
                INDEX idx_performance_alerts_resolved (resolved),
                INDEX idx_performance_alerts_created_at (created_at)
            )
        ",
        'optimization_recommendations' => "
            CREATE TABLE IF NOT EXISTS optimization_recommendations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                category VARCHAR(100) NOT NULL,
                priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
                title VARCHAR(255) NOT NULL,
                description TEXT NOT NULL,
                recommendation TEXT NOT NULL,
                estimated_impact VARCHAR(100) NULL,
                implementation_effort VARCHAR(100) NULL,
                status ENUM('pending', 'in_progress', 'completed', 'dismissed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_optimization_recommendations_category (category),
                INDEX idx_optimization_recommendations_priority (priority),
                INDEX idx_optimization_recommendations_status (status)
            )
        ",
        'slow_queries' => "
            CREATE TABLE IF NOT EXISTS slow_queries (
                id INT PRIMARY KEY AUTO_INCREMENT,
                query_hash VARCHAR(64) NOT NULL,
                query_text TEXT NOT NULL,
                execution_time DECIMAL(10,6) NOT NULL,
                lock_time DECIMAL(10,6) DEFAULT 0,
                rows_sent INT DEFAULT 0,
                rows_examined INT DEFAULT 0,
                database_name VARCHAR(64) NOT NULL,
                user_name VARCHAR(64) NULL,
                host VARCHAR(255) NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_slow_queries_hash (query_hash),
                INDEX idx_slow_queries_time (execution_time),
                INDEX idx_slow_queries_timestamp (timestamp)
            )
        ",
        'performance_baselines' => "
            CREATE TABLE IF NOT EXISTS performance_baselines (
                id INT PRIMARY KEY AUTO_INCREMENT,
                metric_name VARCHAR(255) NOT NULL,
                baseline_value DECIMAL(15,6) NOT NULL,
                measurement_period VARCHAR(50) NOT NULL,
                confidence_interval DECIMAL(5,2) DEFAULT 95.0,
                sample_size INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                INDEX idx_performance_baselines_metric (metric_name),
                INDEX idx_performance_baselines_created_at (created_at)
            )
        "
    ];

    foreach ($tables as $name => $sql) {
        try {
            $pdo->exec($sql);
            echo "✓ Created table: $name\n";
        } catch (Exception $e) {
            echo "✗ Error creating $name: " . $e->getMessage() . "\n";
        }
    }

    echo "\n✓ Performance tables creation completed!\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
