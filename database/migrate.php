<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

class DatabaseMigrator
{
    private PDO $pdo;

    public function __construct()
    {
        $this->connectDatabase();
    }

    private function connectDatabase(): void
    {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $dbname = $_ENV['DB_NAME'] ?? 'seo_indexer';
        $username = $_ENV['DB_USER'] ?? 'root';
        $password = $_ENV['DB_PASS'] ?? '';
        $socket = $_ENV['DB_SOCKET'] ?? '';

        if (!empty($socket) && file_exists($socket)) {
            $dsn = "mysql:unix_socket={$socket};charset=utf8mb4";
        } else {
            $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
        }

        try {
            $this->pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);

            echo "✓ Connected to MySQL server\n";
        } catch (PDOException $e) {
            die("✗ Database connection failed: " . $e->getMessage() . "\n");
        }
    }

    public function migrate(): void
    {
        $this->createDatabase();
        $this->runMigrations();
        echo "✓ Database migration completed successfully!\n";
    }

    private function createDatabase(): void
    {
        $dbname = $_ENV['DB_NAME'] ?? 'seo_indexer';
        
        try {
            $this->pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbname}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $this->pdo->exec("USE `{$dbname}`");
            echo "✓ Database '{$dbname}' created/selected\n";
        } catch (PDOException $e) {
            die("✗ Failed to create database: " . $e->getMessage() . "\n");
        }
    }

    private function runMigrations(): void
    {
        $migrationFiles = [
            __DIR__ . '/schema.sql',
            __DIR__ . '/security_tables.sql',
            __DIR__ . '/performance_tables.sql'
        ];

        foreach ($migrationFiles as $migrationFile) {
            if (!file_exists($migrationFile)) {
                echo "⚠ Migration file not found: {$migrationFile}\n";
                continue;
            }

            echo "Running migration: " . basename($migrationFile) . "\n";
            $this->runSqlFile($migrationFile);
        }
    }

    private function runSqlFile(string $filePath): void
    {
        $sql = file_get_contents($filePath);

        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );

        foreach ($statements as $statement) {
            try {
                $this->pdo->exec($statement);
                echo "✓ Executed SQL statement\n";
            } catch (PDOException $e) {
                // Skip if table already exists or other non-critical errors
                if (strpos($e->getMessage(), 'already exists') === false &&
                    strpos($e->getMessage(), 'Duplicate column') === false) {
                    echo "✗ SQL Error: " . $e->getMessage() . "\n";
                    echo "Statement: " . substr($statement, 0, 100) . "...\n";
                }
            }
        }
    }

    public function seed(): void
    {
        echo "Running database seeds...\n";
        
        // Check if admin user already exists
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        $stmt->execute();
        
        if ($stmt->fetchColumn() == 0) {
            // Create admin user
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("
                INSERT INTO users (username, email, password_hash, first_name, last_name, role, status, email_verified) 
                VALUES ('admin', '<EMAIL>', ?, 'Admin', 'User', 'admin', 'active', TRUE)
            ");
            $stmt->execute([$adminPassword]);
            echo "✓ Admin user created (username: admin, password: admin123)\n";
        }

        echo "✓ Database seeding completed!\n";
    }

    public function reset(): void
    {
        $dbname = $_ENV['DB_NAME'] ?? 'seo_indexer';
        
        $confirm = readline("Are you sure you want to reset the database '{$dbname}'? This will delete all data! (yes/no): ");
        
        if (strtolower($confirm) !== 'yes') {
            echo "Database reset cancelled.\n";
            return;
        }

        try {
            $this->pdo->exec("DROP DATABASE IF EXISTS `{$dbname}`");
            echo "✓ Database '{$dbname}' dropped\n";
            
            $this->migrate();
            $this->seed();
            
            echo "✓ Database reset completed!\n";
        } catch (PDOException $e) {
            die("✗ Failed to reset database: " . $e->getMessage() . "\n");
        }
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $command = $argv[1] ?? 'migrate';
    $migrator = new DatabaseMigrator();

    switch ($command) {
        case 'migrate':
            $migrator->migrate();
            break;
        case 'seed':
            $migrator->seed();
            break;
        case 'reset':
            $migrator->reset();
            break;
        default:
            echo "Usage: php migrate.php [migrate|seed|reset]\n";
            echo "  migrate - Run database migrations\n";
            echo "  seed    - Seed database with initial data\n";
            echo "  reset   - Reset database (WARNING: deletes all data)\n";
            exit(1);
    }
}
