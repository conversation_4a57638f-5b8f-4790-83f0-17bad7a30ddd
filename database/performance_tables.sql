-- Performance monitoring and optimization tables

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    value DECIMAL(15,6) NOT NULL,
    type ENUM('gauge', 'counter', 'timing', 'histogram') DEFAULT 'gauge',
    timestamp INT NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSON,
    INDEX idx_performance_metrics_name (name),
    INDEX idx_performance_metrics_date (date),
    INDEX idx_performance_metrics_type (type),
    INDEX idx_performance_metrics_name_date (name, date)
);

-- Query performance tracking
CREATE TABLE IF NOT EXISTS query_performance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    query_hash VARCHAR(64) NOT NULL,
    query_text TEXT NOT NULL,
    execution_time DECIMAL(10,6) NOT NULL,
    rows_examined INT DEFAULT 0,
    rows_sent INT DEFAULT 0,
    memory_used INT DEFAULT 0,
    user_id INT NULL,
    endpoint VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_query_performance_hash (query_hash),
    INDEX idx_query_performance_time (execution_time),
    INDEX idx_query_performance_created_at (created_at),
    INDEX idx_query_performance_endpoint (endpoint)
);

-- Endpoint performance tracking
CREATE TABLE IF NOT EXISTS endpoint_performance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    endpoint VARCHAR(255) NOT NULL,
    method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH') NOT NULL,
    response_time DECIMAL(10,6) NOT NULL,
    memory_usage INT NOT NULL,
    status_code INT NOT NULL,
    user_id INT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_endpoint_performance_endpoint (endpoint),
    INDEX idx_endpoint_performance_method (method),
    INDEX idx_endpoint_performance_response_time (response_time),
    INDEX idx_endpoint_performance_created_at (created_at),
    INDEX idx_endpoint_performance_status (status_code)
);

-- Cache performance tracking
CREATE TABLE IF NOT EXISTS cache_performance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cache_key VARCHAR(255) NOT NULL,
    operation ENUM('get', 'set', 'delete', 'clear') NOT NULL,
    hit BOOLEAN DEFAULT FALSE,
    execution_time DECIMAL(10,6) NOT NULL,
    data_size INT DEFAULT 0,
    ttl INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_cache_performance_key (cache_key),
    INDEX idx_cache_performance_operation (operation),
    INDEX idx_cache_performance_hit (hit),
    INDEX idx_cache_performance_created_at (created_at)
);

-- Database connection pool monitoring
CREATE TABLE IF NOT EXISTS connection_pool_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    active_connections INT NOT NULL,
    idle_connections INT NOT NULL,
    total_connections INT NOT NULL,
    max_connections INT NOT NULL,
    connection_errors INT DEFAULT 0,
    avg_connection_time DECIMAL(10,6) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_connection_pool_created_at (created_at)
);

-- System resource monitoring
CREATE TABLE IF NOT EXISTS system_resources (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cpu_usage DECIMAL(5,2) NOT NULL,
    memory_usage DECIMAL(15,0) NOT NULL,
    memory_total DECIMAL(15,0) NOT NULL,
    disk_usage DECIMAL(15,0) NOT NULL,
    disk_total DECIMAL(15,0) NOT NULL,
    load_average_1min DECIMAL(5,2) DEFAULT NULL,
    load_average_5min DECIMAL(5,2) DEFAULT NULL,
    load_average_15min DECIMAL(5,2) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_system_resources_created_at (created_at)
);

-- Performance alerts
CREATE TABLE IF NOT EXISTS performance_alerts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    alert_type VARCHAR(100) NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    message TEXT NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    threshold_value DECIMAL(15,6) NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_performance_alerts_type (alert_type),
    INDEX idx_performance_alerts_severity (severity),
    INDEX idx_performance_alerts_resolved (resolved),
    INDEX idx_performance_alerts_created_at (created_at)
);

-- Performance optimization recommendations
CREATE TABLE IF NOT EXISTS optimization_recommendations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(100) NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    recommendation TEXT NOT NULL,
    estimated_impact VARCHAR(100) NULL,
    implementation_effort VARCHAR(100) NULL,
    status ENUM('pending', 'in_progress', 'completed', 'dismissed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_optimization_recommendations_category (category),
    INDEX idx_optimization_recommendations_priority (priority),
    INDEX idx_optimization_recommendations_status (status)
);

-- Slow query log (custom implementation)
CREATE TABLE IF NOT EXISTS slow_queries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    query_hash VARCHAR(64) NOT NULL,
    query_text TEXT NOT NULL,
    execution_time DECIMAL(10,6) NOT NULL,
    lock_time DECIMAL(10,6) DEFAULT 0,
    rows_sent INT DEFAULT 0,
    rows_examined INT DEFAULT 0,
    database_name VARCHAR(64) NOT NULL,
    user_name VARCHAR(64) NULL,
    host VARCHAR(255) NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_slow_queries_hash (query_hash),
    INDEX idx_slow_queries_time (execution_time),
    INDEX idx_slow_queries_timestamp (timestamp)
);

-- Performance baselines for comparison
CREATE TABLE IF NOT EXISTS performance_baselines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(255) NOT NULL,
    baseline_value DECIMAL(15,6) NOT NULL,
    measurement_period VARCHAR(50) NOT NULL,
    confidence_interval DECIMAL(5,2) DEFAULT 95.0,
    sample_size INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    INDEX idx_performance_baselines_metric (metric_name),
    INDEX idx_performance_baselines_created_at (created_at)
);

-- Create views for common performance queries
CREATE OR REPLACE VIEW performance_summary AS
SELECT 
    DATE(date) as date,
    name as metric_name,
    AVG(value) as avg_value,
    MIN(value) as min_value,
    MAX(value) as max_value,
    COUNT(*) as sample_count,
    STDDEV(value) as std_deviation
FROM performance_metrics 
WHERE date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(date), name
ORDER BY date DESC, metric_name;

CREATE OR REPLACE VIEW slow_endpoints_summary AS
SELECT 
    endpoint,
    method,
    AVG(response_time) as avg_response_time,
    MAX(response_time) as max_response_time,
    MIN(response_time) as min_response_time,
    COUNT(*) as request_count,
    COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_count,
    (COUNT(CASE WHEN status_code >= 400 THEN 1 END) / COUNT(*)) * 100 as error_rate
FROM endpoint_performance 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY endpoint, method
HAVING avg_response_time > 1.0
ORDER BY avg_response_time DESC;

CREATE OR REPLACE VIEW cache_hit_rate AS
SELECT 
    DATE(created_at) as date,
    operation,
    COUNT(*) as total_operations,
    SUM(CASE WHEN hit = 1 THEN 1 ELSE 0 END) as hits,
    (SUM(CASE WHEN hit = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100 as hit_rate
FROM cache_performance 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), operation
ORDER BY date DESC, operation;

-- Stored procedures for performance analysis
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS AnalyzePerformanceTrends(
    IN metric_name VARCHAR(255),
    IN days_back INT DEFAULT 7
)
BEGIN
    SELECT 
        DATE(date) as date,
        AVG(value) as avg_value,
        MIN(value) as min_value,
        MAX(value) as max_value,
        COUNT(*) as samples,
        LAG(AVG(value)) OVER (ORDER BY DATE(date)) as previous_avg,
        ((AVG(value) - LAG(AVG(value)) OVER (ORDER BY DATE(date))) / 
         LAG(AVG(value)) OVER (ORDER BY DATE(date))) * 100 as change_percent
    FROM performance_metrics 
    WHERE name = metric_name 
    AND date >= DATE_SUB(NOW(), INTERVAL days_back DAY)
    GROUP BY DATE(date)
    ORDER BY date;
END //

CREATE PROCEDURE IF NOT EXISTS GetPerformanceAlerts(
    IN severity_filter VARCHAR(20) DEFAULT NULL
)
BEGIN
    SELECT 
        alert_type,
        severity,
        message,
        metric_name,
        metric_value,
        threshold_value,
        created_at,
        resolved,
        resolved_at
    FROM performance_alerts 
    WHERE (severity_filter IS NULL OR severity = severity_filter)
    AND resolved = FALSE
    ORDER BY 
        FIELD(severity, 'critical', 'high', 'medium', 'low'),
        created_at DESC;
END //

CREATE PROCEDURE IF NOT EXISTS CleanupPerformanceData()
BEGIN
    -- Clean up old performance metrics (older than 30 days)
    DELETE FROM performance_metrics WHERE date < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean up old query performance data (older than 7 days)
    DELETE FROM query_performance WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Clean up old endpoint performance data (older than 14 days)
    DELETE FROM endpoint_performance WHERE created_at < DATE_SUB(NOW(), INTERVAL 14 DAY);
    
    -- Clean up old cache performance data (older than 7 days)
    DELETE FROM cache_performance WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Clean up old system resource data (older than 30 days)
    DELETE FROM system_resources WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean up resolved alerts older than 30 days
    DELETE FROM performance_alerts 
    WHERE resolved = TRUE AND resolved_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean up old slow queries (older than 7 days)
    DELETE FROM slow_queries WHERE timestamp < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    SELECT ROW_COUNT() as cleaned_records;
END //

DELIMITER ;

-- Create event to run cleanup daily
CREATE EVENT IF NOT EXISTS daily_performance_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL CleanupPerformanceData();

-- Add indexes for better performance on large datasets
CREATE INDEX IF NOT EXISTS idx_performance_metrics_composite ON performance_metrics(name, date, type);
CREATE INDEX IF NOT EXISTS idx_endpoint_performance_composite ON endpoint_performance(endpoint, method, created_at);
CREATE INDEX IF NOT EXISTS idx_query_performance_composite ON query_performance(query_hash, execution_time, created_at);
