<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

try {
    $socket = $_ENV['DB_SOCKET'] ?? '';
    $dbname = $_ENV['DB_NAME'] ?? 'indexation-tool';
    $username = $_ENV['DB_USER'] ?? 'root';
    $password = $_ENV['DB_PASS'] ?? 'root';

    if (!empty($socket) && file_exists($socket)) {
        $dsn = "mysql:unix_socket={$socket};dbname={$dbname};charset=utf8mb4";
    } else {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '8889';
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    }

    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    echo "=== SEO Indexer Platform Database Information ===\n\n";
    echo "Database: {$dbname}\n";
    echo "Connection: " . (strpos($dsn, 'unix_socket') !== false ? 'Socket' : 'TCP') . "\n\n";

    // Get all tables
    $result = $pdo->query('SHOW TABLES');
    $tables = $result->fetchAll(PDO::FETCH_COLUMN);
    sort($tables);

    // Categorize tables
    $categories = [
        'Core Application' => ['users', 'domains', 'oauth_tokens', 'api_configurations', 'url_submissions', 'sitemaps', 'api_usage', 'sessions', 'system_logs', 'system_settings'],
        'Security' => ['blocked_ips', 'rate_limits', 'security_logs', 'login_attempts', 'password_reset_tokens', 'email_verification_tokens', 'session_security', 'api_key_security', 'user_security_settings', 'audit_trail'],
        'Performance' => ['performance_metrics', 'query_performance', 'endpoint_performance', 'cache_performance', 'connection_pool_stats', 'system_resources', 'performance_alerts', 'optimization_recommendations', 'slow_queries', 'performance_baselines']
    ];

    foreach ($categories as $category => $categoryTables) {
        echo "=== {$category} Tables ===\n";
        foreach ($categoryTables as $table) {
            if (in_array($table, $tables)) {
                // Get row count
                $countResult = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $countResult->fetch()['count'];
                echo sprintf("✓ %-30s (%d rows)\n", $table, $count);
            } else {
                echo sprintf("✗ %-30s (missing)\n", $table);
            }
        }
        echo "\n";
    }

    // Show admin user info
    echo "=== Admin User Information ===\n";
    $adminResult = $pdo->query("SELECT username, email, role, status, email_verified, created_at FROM users WHERE role = 'admin' LIMIT 1");
    $admin = $adminResult->fetch();
    if ($admin) {
        echo "Username: {$admin['username']}\n";
        echo "Email: {$admin['email']}\n";
        echo "Status: {$admin['status']}\n";
        echo "Email Verified: " . ($admin['email_verified'] ? 'Yes' : 'No') . "\n";
        echo "Created: {$admin['created_at']}\n";
        echo "Default Password: admin123 (please change in production!)\n";
    } else {
        echo "No admin user found!\n";
    }

    echo "\n=== System Settings ===\n";
    $settingsResult = $pdo->query("SELECT setting_key, setting_value, setting_type FROM system_settings ORDER BY setting_key");
    $settings = $settingsResult->fetchAll();
    foreach ($settings as $setting) {
        echo sprintf("%-30s = %s (%s)\n", $setting['setting_key'], $setting['setting_value'], $setting['setting_type']);
    }

    echo "\n=== Database Summary ===\n";
    echo "Total Tables: " . count($tables) . "\n";
    
    // Calculate total rows
    $totalRows = 0;
    foreach ($tables as $table) {
        $countResult = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
        $totalRows += $countResult->fetch()['count'];
    }
    echo "Total Rows: {$totalRows}\n";

    // Get database size
    $sizeResult = $pdo->query("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
    ");
    $size = $sizeResult->fetch()['size_mb'];
    echo "Database Size: {$size} MB\n";

    echo "\n✓ Database setup is complete and ready for use!\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
