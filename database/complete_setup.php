<?php

declare(strict_types=1);

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

try {
    $socket = $_ENV['DB_SOCKET'] ?? '';
    $dbname = $_ENV['DB_NAME'] ?? 'indexation-tool';
    $username = $_ENV['DB_USER'] ?? 'root';
    $password = $_ENV['DB_PASS'] ?? 'root';

    if (!empty($socket) && file_exists($socket)) {
        $dsn = "mysql:unix_socket={$socket};dbname={$dbname};charset=utf8mb4";
    } else {
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $port = $_ENV['DB_PORT'] ?? '8889';
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    }

    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    echo "✓ Connected to MySQL server\n";

    // Create system_settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_setting_key (setting_key)
        )
    ");
    echo "✓ Created system_settings table\n";

    // Insert default system settings
    $settings = [
        ['google_daily_quota', '200', 'integer', 'Daily quota for Google Search Console API'],
        ['bing_daily_quota', '10000', 'integer', 'Daily quota for Bing Webmaster API'],
        ['max_domains_per_user', '10', 'integer', 'Maximum domains per user'],
        ['max_urls_per_submission', '100', 'integer', 'Maximum URLs per batch submission'],
        ['rate_limit_requests', '100', 'integer', 'Rate limit requests per window'],
        ['rate_limit_window', '3600', 'integer', 'Rate limit window in seconds'],
        ['maintenance_mode', 'false', 'boolean', 'Enable maintenance mode'],
        ['registration_enabled', 'true', 'boolean', 'Enable user registration'],
        ['email_verification_required', 'true', 'boolean', 'Require email verification for new users']
    ];

    $stmt = $pdo->prepare('INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)');
    foreach($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "✓ Inserted default system settings\n";

    // Check if admin user exists, if not create it
    $stmt = $pdo->prepare('SELECT COUNT(*) FROM users WHERE username = ?');
    $stmt->execute(['admin']);
    if($stmt->fetchColumn() == 0) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare('
            INSERT INTO users (username, email, password_hash, first_name, last_name, role, status, email_verified) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ');
        $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'Admin', 'User', 'admin', 'active', 1]);
        echo "✓ Created admin user (username: admin, password: admin123)\n";
    } else {
        echo "- Admin user already exists\n";
    }

    echo "\n✓ Database setup completed successfully!\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
