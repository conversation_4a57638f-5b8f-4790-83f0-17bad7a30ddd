<?php
/**
 * OAuth Configuration Test Script
 * 
 * This script helps you test your OAuth configuration
 * Run this from your web browser to check if OAuth is properly configured
 */

// Load environment variables
if (file_exists('../.env')) {
    $lines = file('../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $_ENV[trim($key)] = trim($value);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth Configuration Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔧 OAuth Configuration Test</h1>
    
    <div class="section">
        <h2>📋 Current Configuration</h2>
        
        <h3>Google OAuth</h3>
        <div class="status <?php echo !empty($_ENV['GOOGLE_CLIENT_ID']) ? 'success' : 'error'; ?>">
            <strong>Client ID:</strong> <?php echo !empty($_ENV['GOOGLE_CLIENT_ID']) ? 'Configured ✓' : 'Missing ✗'; ?>
        </div>
        <div class="status <?php echo !empty($_ENV['GOOGLE_CLIENT_SECRET']) ? 'success' : 'error'; ?>">
            <strong>Client Secret:</strong> <?php echo !empty($_ENV['GOOGLE_CLIENT_SECRET']) ? 'Configured ✓' : 'Missing ✗'; ?>
        </div>
        <div class="status info">
            <strong>Redirect URI:</strong> <?php echo $_ENV['GOOGLE_REDIRECT_URI'] ?? 'Not set'; ?>
        </div>
        
        <h3>Bing/Microsoft OAuth</h3>
        <div class="status <?php echo !empty($_ENV['BING_CLIENT_ID']) ? 'success' : 'error'; ?>">
            <strong>Client ID:</strong> <?php echo !empty($_ENV['BING_CLIENT_ID']) ? 'Configured ✓' : 'Missing ✗'; ?>
        </div>
        <div class="status <?php echo !empty($_ENV['BING_CLIENT_SECRET']) ? 'success' : 'error'; ?>">
            <strong>Client Secret:</strong> <?php echo !empty($_ENV['BING_CLIENT_SECRET']) ? 'Configured ✓' : 'Missing ✗'; ?>
        </div>
        <div class="status info">
            <strong>Redirect URI:</strong> <?php echo $_ENV['BING_REDIRECT_URI'] ?? 'Not set'; ?>
        </div>
    </div>
    
    <div class="section">
        <h2>🚀 Setup Instructions</h2>
        
        <h3>1. Google OAuth Setup</h3>
        <ol>
            <li>Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>Create a new project or select existing one</li>
            <li>Enable these APIs:
                <ul>
                    <li>Google Search Console API</li>
                    <li>Web Search Indexing API</li>
                </ul>
            </li>
            <li>Go to "APIs & Services" > "Credentials"</li>
            <li>Create "OAuth 2.0 Client IDs" for "Web application"</li>
            <li>Add authorized redirect URI: <code><?php echo $_ENV['GOOGLE_REDIRECT_URI'] ?? 'http://localhost:8000/auth/google/callback'; ?></code></li>
            <li>Copy Client ID and Client Secret to your .env file</li>
        </ol>
        
        <h3>2. Microsoft/Bing OAuth Setup</h3>
        <ol>
            <li>Go to <a href="https://portal.azure.com/" target="_blank">Azure Portal</a></li>
            <li>Navigate to "Azure Active Directory" > "App registrations"</li>
            <li>Create new registration:
                <ul>
                    <li>Name: "SEO Indexer Platform"</li>
                    <li>Account types: "Accounts in any organizational directory and personal Microsoft accounts"</li>
                    <li>Redirect URI: <code><?php echo $_ENV['BING_REDIRECT_URI'] ?? 'http://localhost:8000/auth/bing/callback'; ?></code></li>
                </ul>
            </li>
            <li>Add API permissions for Bing Webmaster Tools</li>
            <li>Create client secret in "Certificates & secrets"</li>
            <li>Copy Application (client) ID and Client Secret to your .env file</li>
        </ol>
    </div>
    
    <div class="section">
        <h2>📝 .env File Template</h2>
        <p>Update your .env file with the credentials you obtain:</p>
        <pre># Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
GOOGLE_REDIRECT_URI=<?php echo $_ENV['GOOGLE_REDIRECT_URI'] ?? 'http://localhost:8000/auth/google/callback'; ?>

# Bing/Microsoft OAuth2 Configuration
BING_CLIENT_ID=your-bing-client-id-here
BING_CLIENT_SECRET=your-bing-client-secret-here
BING_REDIRECT_URI=<?php echo $_ENV['BING_REDIRECT_URI'] ?? 'http://localhost:8000/auth/bing/callback'; ?></pre>
    </div>
    
    <div class="section">
        <h2>🔍 Testing</h2>
        <?php if (!empty($_ENV['GOOGLE_CLIENT_ID']) && !empty($_ENV['GOOGLE_CLIENT_SECRET'])): ?>
            <div class="status success">
                <strong>Google OAuth:</strong> Configuration appears complete. You can test the "Connect Google Account" button.
            </div>
        <?php else: ?>
            <div class="status error">
                <strong>Google OAuth:</strong> Configuration incomplete. Please set up Google OAuth credentials first.
            </div>
        <?php endif; ?>
        
        <?php if (!empty($_ENV['BING_CLIENT_ID']) && !empty($_ENV['BING_CLIENT_SECRET'])): ?>
            <div class="status success">
                <strong>Bing OAuth:</strong> Configuration appears complete. You can test the "Connect Bing Account" button.
            </div>
        <?php else: ?>
            <div class="status error">
                <strong>Bing OAuth:</strong> Configuration incomplete. Please set up Microsoft Azure OAuth credentials first.
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>🛠️ Troubleshooting</h2>
        <h3>Common Issues:</h3>
        <ul>
            <li><strong>client_id missing:</strong> Make sure you've set the CLIENT_ID in your .env file</li>
            <li><strong>Redirect URI mismatch:</strong> Ensure the redirect URI in your OAuth app matches exactly what's in .env</li>
            <li><strong>API not enabled:</strong> Make sure you've enabled the required APIs in Google Cloud Console</li>
            <li><strong>Permissions not granted:</strong> Ensure your Azure app has the correct API permissions</li>
        </ul>
        
        <h3>Current Server Info:</h3>
        <div class="status info">
            <strong>Server:</strong> <?php echo $_SERVER['HTTP_HOST'] ?? 'localhost'; ?><br>
            <strong>Port:</strong> <?php echo $_SERVER['SERVER_PORT'] ?? '80'; ?><br>
            <strong>Protocol:</strong> <?php echo isset($_SERVER['HTTPS']) ? 'https' : 'http'; ?>
        </div>
    </div>
    
    <div class="section">
        <h2>⚠️ Security Note</h2>
        <div class="status warning">
            <strong>Important:</strong> Delete this test file (oauth-test.php) after you've completed the OAuth setup for security reasons.
        </div>
    </div>
</body>
</html>
