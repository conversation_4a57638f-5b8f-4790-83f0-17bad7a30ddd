# SEO Indexer Platform

A comprehensive platform for submitting URLs to Google Search Console and Bing Webmaster Tools for indexing.

## Features

- **Multi-Platform Support**: Submit URLs to both Google and Bing
- **OAuth2 Integration**: Secure authentication with Google and Bing accounts
- **Bulk Operations**: Submit multiple URLs or entire sitemaps
- **Real-time Monitoring**: Track indexing status and performance
- **User Management**: Role-based access with admin controls
- **API Integration**: RESTful API for programmatic access
- **Security First**: Encrypted data storage, rate limiting, and comprehensive validation

## Requirements

- PHP 8.0 or higher
- MySQL 8.0 or higher
- Composer
- Web server (Apache/Nginx)
- SSL certificate (recommended for production)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd indexation-tool
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   # Create database
   mysql -u root -p -e "CREATE DATABASE seo_indexer;"
   
   # Run migrations
   php database/migrate.php
   ```

5. **Set permissions**
   ```bash
   chmod 755 logs/ cache/ uploads/ tmp/
   ```

6. **Configure web server**
   - Point document root to `public/` directory
   - Ensure mod_rewrite is enabled (Apache)

## Configuration

### Google Search Console API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google Search Console API
4. Create OAuth2 credentials
5. Add your domain to authorized redirect URIs
6. Update `.env` with your credentials

### Bing Webmaster Tools

1. Go to [Bing Webmaster Tools](https://www.bing.com/webmasters)
2. Generate API key
3. Update `.env` with your API key

## Usage

### For Users

1. **Register/Login**: Create account or login
2. **Connect Accounts**: Link Google and Bing accounts via OAuth2
3. **Add Domains**: Add and verify your domains
4. **Submit URLs**: Submit individual URLs or bulk upload
5. **Monitor Status**: Track indexing progress and results

### For Admins

1. **User Management**: View, block, or manage users
2. **System Monitoring**: View logs, quotas, and performance
3. **Settings**: Configure platform settings and limits

## API Documentation

### Authentication
All API requests require authentication via session or API key.

### Endpoints

- `GET /api/urls/status` - Get URL indexing status
- `POST /api/urls/check` - Check multiple URLs
- `GET /api/quotas` - Get API usage quotas

## Development

### Running Tests
```bash
composer test
```

### Code Quality
```bash
composer phpstan
composer phpcs
```

### Database Migrations
```bash
php database/migrate.php
```

## Security

- All passwords are hashed using bcrypt
- Sensitive data is encrypted using AES-256
- Rate limiting prevents abuse
- Input validation and sanitization
- CSRF protection on forms
- SQL injection prevention via prepared statements

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## License

MIT License - see LICENSE file for details

## Support

For support, please contact: <EMAIL>
