name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version: [8.0, 8.1, 8.2]
        mysql-version: [8.0]
    
    services:
      mysql:
        image: mysql:${{ matrix.mysql-version }}
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: seo_indexer_test
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_pass
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: pdo, pdo_mysql, curl, json, openssl
        coverage: xdebug

    - name: Validate composer.json and composer.lock
      run: composer validate --strict

    - name: Cache Composer packages
      id: composer-cache
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-php-${{ matrix.php-version }}-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-php-${{ matrix.php-version }}-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --dev

    - name: Create environment file
      run: |
        cp .env.example .env
        echo "APP_ENV=testing" >> .env
        echo "DB_HOST=127.0.0.1" >> .env
        echo "DB_NAME=seo_indexer_test" >> .env
        echo "DB_USER=test_user" >> .env
        echo "DB_PASS=test_pass" >> .env
        echo "ENCRYPTION_KEY=test-encryption-key-32-characters" >> .env
        echo "JWT_SECRET=test-jwt-secret-key" >> .env

    - name: Wait for MySQL
      run: |
        while ! mysqladmin ping -h127.0.0.1 -P3306 -utest_user -ptest_pass --silent; do
          sleep 1
        done

    - name: Setup database
      run: |
        mysql -h127.0.0.1 -P3306 -utest_user -ptest_pass seo_indexer_test < database/schema.sql
        mysql -h127.0.0.1 -P3306 -utest_user -ptest_pass seo_indexer_test < database/security_tables.sql

    - name: Run Unit Tests
      run: composer run-script test:unit

    - name: Run Integration Tests
      run: composer run-script test:integration

    - name: Run Feature Tests
      run: composer run-script test:feature

    - name: Run Security Tests
      run: composer run-script test:security

    - name: Generate Coverage Report
      run: composer run-script test:coverage

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./tests/coverage/xml/coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Run PHPStan Static Analysis
      run: composer run-script phpstan

    - name: Run PHP CodeSniffer
      run: composer run-script phpcs

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.1
        extensions: pdo, pdo_mysql, curl, json, openssl

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-dev --optimize-autoloader

    - name: Run security audit
      run: composer audit

    - name: Check for known vulnerabilities
      run: |
        if command -v local-php-security-checker &> /dev/null; then
          local-php-security-checker
        else
          echo "Security checker not available, skipping..."
        fi

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: seo_indexer_test
          MYSQL_USER: test_user
          MYSQL_PASSWORD: test_pass
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.1
        extensions: pdo, pdo_mysql, curl, json, openssl

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-dev --optimize-autoloader

    - name: Create environment file
      run: |
        cp .env.example .env
        echo "APP_ENV=testing" >> .env
        echo "DB_HOST=127.0.0.1" >> .env
        echo "DB_NAME=seo_indexer_test" >> .env
        echo "DB_USER=test_user" >> .env
        echo "DB_PASS=test_pass" >> .env

    - name: Setup database
      run: |
        mysql -h127.0.0.1 -P3306 -utest_user -ptest_pass seo_indexer_test < database/schema.sql
        mysql -h127.0.0.1 -P3306 -utest_user -ptest_pass seo_indexer_test < database/security_tables.sql

    - name: Run performance tests
      run: |
        # Run specific performance-focused tests
        vendor/bin/phpunit tests/Feature/UserWorkflowTest.php::testPerformanceOfCompleteWorkflow
        vendor/bin/phpunit tests/Integration/UrlSubmissionTest.php::testPerformanceOfBatchSubmission
        vendor/bin/phpunit tests/Security/SecurityMiddlewareTest.php::testSecurityMiddlewarePerformance

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [test, security-scan, performance-test]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

  deploy-production:
    runs-on: ubuntu-latest
    needs: [test, security-scan, performance-test]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
