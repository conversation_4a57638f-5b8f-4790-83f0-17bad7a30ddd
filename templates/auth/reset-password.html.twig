{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-lock fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">Reset Password</h2>
                        <p class="text-muted">Enter your new password</p>
                    </div>

                    {% if errors %}
                        <div class="alert alert-danger">
                            {% for field, error in errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="POST" action="/reset-password">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <input type="hidden" name="token" value="{{ token }}">
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" 
                                   class="form-control {{ errors.password ? 'is-invalid' : '' }}" 
                                   id="password" 
                                   name="password" 
                                   required>
                            {% if errors.password %}
                                <div class="invalid-feedback">{{ errors.password }}</div>
                            {% endif %}
                            <div class="form-text">
                                Password must be at least 8 characters with uppercase, lowercase, and number.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <input type="password" 
                                   class="form-control {{ errors.confirm_password ? 'is-invalid' : '' }}" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            {% if errors.confirm_password %}
                                <div class="invalid-feedback">{{ errors.confirm_password }}</div>
                            {% endif %}
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Reset Password
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <p class="mb-0">
                            <a href="/login" class="text-decoration-none">Back to Login</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
