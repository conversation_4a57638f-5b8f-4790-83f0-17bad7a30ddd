{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">Register</h2>
                        <p class="text-muted">Create your account</p>
                    </div>

                    {% if errors %}
                        <div class="alert alert-danger">
                            {% for field, error in errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="POST" action="/register">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="first_name" 
                                       name="first_name" 
                                       value="{{ old_input.first_name ?? '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="last_name" 
                                       name="last_name" 
                                       value="{{ old_input.last_name ?? '' }}">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" 
                                   class="form-control {{ errors.username ? 'is-invalid' : '' }}" 
                                   id="username" 
                                   name="username" 
                                   value="{{ old_input.username ?? '' }}" 
                                   required>
                            {% if errors.username %}
                                <div class="invalid-feedback">{{ errors.username }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" 
                                   class="form-control {{ errors.email ? 'is-invalid' : '' }}" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old_input.email ?? '' }}" 
                                   required>
                            {% if errors.email %}
                                <div class="invalid-feedback">{{ errors.email }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" 
                                   class="form-control {{ errors.password ? 'is-invalid' : '' }}" 
                                   id="password" 
                                   name="password" 
                                   required>
                            {% if errors.password %}
                                <div class="invalid-feedback">{{ errors.password }}</div>
                            {% endif %}
                            <div class="form-text">
                                Password must be at least 8 characters with uppercase, lowercase, and number.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <input type="password" 
                                   class="form-control {{ errors.confirm_password ? 'is-invalid' : '' }}" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required>
                            {% if errors.confirm_password %}
                                <div class="invalid-feedback">{{ errors.confirm_password }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="/terms" target="_blank">Terms of Service</a> 
                                and <a href="/privacy" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <!-- OAuth Registration Options -->
                    <div class="d-grid gap-2">
                        <a href="/auth/google" class="btn btn-outline-danger">
                            <i class="fab fa-google me-2"></i>Sign up with Google
                        </a>
                        <a href="/auth/bing" class="btn btn-outline-info">
                            <i class="fab fa-microsoft me-2"></i>Sign up with Microsoft
                        </a>
                    </div>

                    <div class="text-center mt-4">
                        <p class="mb-0">
                            Already have an account? 
                            <a href="/login" class="text-decoration-none">Sign in</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
