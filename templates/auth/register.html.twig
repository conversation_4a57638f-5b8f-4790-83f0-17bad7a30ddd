{% extends "layouts/auth.html.twig" %}

{% block title %}Register - {{ parent() }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-user-plus text-primary-600 text-xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Create your account</h2>
        <p class="mt-2 text-sm text-gray-600">
            Join thousands of users optimizing their SEO
        </p>
    </div>

    <!-- Error Messages -->
    {% if errors %}
        <div class="alert-danger">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-danger-400 h-5 w-5"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-danger-800">
                        Please fix the following errors
                    </h3>
                    <div class="mt-2 text-sm text-danger-700">
                        <ul class="list-disc list-inside space-y-1">
                            {% for field, error in errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Registration Form -->
    <form method="POST" action="/register" class="space-y-6">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

        <!-- Name Fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-group">
                <label for="first_name" class="form-label">First Name</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-user text-gray-400"></i>
                    </div>
                    <input type="text"
                           id="first_name"
                           name="first_name"
                           value="{{ old_input.first_name ?? '' }}"
                           class="form-input pl-10 {{ errors.first_name ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                           placeholder="John">
                </div>
                {% if errors.first_name %}
                    <p class="form-error">{{ errors.first_name }}</p>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="last_name" class="form-label">Last Name</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-user text-gray-400"></i>
                    </div>
                    <input type="text"
                           id="last_name"
                           name="last_name"
                           value="{{ old_input.last_name ?? '' }}"
                           class="form-input pl-10 {{ errors.last_name ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                           placeholder="Doe">
                </div>
                {% if errors.last_name %}
                    <p class="form-error">{{ errors.last_name }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Username -->
        <div class="form-group">
            <label for="username" class="form-label">
                Username <span class="text-danger-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-at text-gray-400"></i>
                </div>
                <input type="text"
                       id="username"
                       name="username"
                       value="{{ old_input.username ?? '' }}"
                       class="form-input pl-10 {{ errors.username ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                       placeholder="johndoe"
                       required>
            </div>
            {% if errors.username %}
                <p class="form-error">{{ errors.username }}</p>
            {% endif %}
        </div>

        <!-- Email -->
        <div class="form-group">
            <label for="email" class="form-label">
                Email Address <span class="text-danger-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
                <input type="email"
                       id="email"
                       name="email"
                       value="{{ old_input.email ?? '' }}"
                       class="form-input pl-10 {{ errors.email ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                       placeholder="<EMAIL>"
                       required>
            </div>
            {% if errors.email %}
                <p class="form-error">{{ errors.email }}</p>
            {% endif %}
        </div>

        <!-- Password -->
        <div class="form-group">
            <label for="password" class="form-label">
                Password <span class="text-danger-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password"
                       id="password"
                       name="password"
                       class="form-input pl-10 {{ errors.password ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                       placeholder="Enter a strong password"
                       required>
            </div>
            {% if errors.password %}
                <p class="form-error">{{ errors.password }}</p>
            {% endif %}
            <p class="form-help">
                Password must be at least 8 characters long and contain uppercase, lowercase, and numbers.
            </p>
        </div>

        <!-- Confirm Password -->
        <div class="form-group">
            <label for="confirm_password" class="form-label">
                Confirm Password <span class="text-danger-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password"
                       id="confirm_password"
                       name="confirm_password"
                       class="form-input pl-10 {{ errors.confirm_password ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                       placeholder="Confirm your password"
                       required>
            </div>
            {% if errors.confirm_password %}
                <p class="form-error">{{ errors.confirm_password }}</p>
            {% endif %}
        </div>

        <!-- Terms Agreement -->
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input type="checkbox"
                       id="terms"
                       name="terms"
                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                       required>
            </div>
            <div class="ml-3 text-sm">
                <label for="terms" class="text-gray-700">
                    I agree to the
                    <a href="/terms" target="_blank" class="font-medium text-primary-600 hover:text-primary-500">Terms of Service</a>
                    and
                    <a href="/privacy" target="_blank" class="font-medium text-primary-600 hover:text-primary-500">Privacy Policy</a>
                </label>
            </div>
        </div>

        <!-- Submit Button -->
        <div>
            <button type="submit" class="btn-primary w-full">
                <i class="fas fa-user-plus mr-2"></i>
                Create Account
            </button>
        </div>
    </form>

    <!-- Social Registration -->
    <div class="relative">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">Or sign up with</span>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-3">
        <a href="/auth/google" class="btn-outline w-full justify-center">
            <i class="fab fa-google mr-2 text-red-500"></i>
            Sign up with Google
        </a>
        <a href="/auth/bing" class="btn-outline w-full justify-center">
            <i class="fab fa-microsoft mr-2 text-blue-500"></i>
            Sign up with Microsoft
        </a>
    </div>

    <!-- Login Link -->
    <div class="text-center">
        <p class="text-sm text-gray-600">
            Already have an account?
            <a href="/login" class="font-medium text-primary-600 hover:text-primary-500 transition-colors">
                Sign in here
            </a>
        </p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating account...';
    });

    // Password strength indicator
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');

    function validatePassword() {
        const value = password.value;
        const hasUpper = /[A-Z]/.test(value);
        const hasLower = /[a-z]/.test(value);
        const hasNumber = /\d/.test(value);
        const hasLength = value.length >= 8;

        // You can add visual feedback here
    }

    function validatePasswordMatch() {
        if (confirmPassword.value && password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    password.addEventListener('input', validatePassword);
    confirmPassword.addEventListener('input', validatePasswordMatch);
});
</script>
{% endblock %}
