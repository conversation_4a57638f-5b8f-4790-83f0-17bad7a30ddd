{% extends "layouts/auth.html.twig" %}

{% block title %}Login - {{ parent() }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-sign-in-alt text-primary-600 text-xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-gray-900">Welcome back</h2>
        <p class="mt-2 text-sm text-gray-600">
            Sign in to your account to continue
        </p>
    </div>

    <!-- Error Messages -->
    {% if errors %}
        <div class="alert-danger">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-danger-400 h-5 w-5"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-danger-800">
                        There were errors with your submission
                    </h3>
                    <div class="mt-2 text-sm text-danger-700">
                        <ul class="list-disc list-inside space-y-1">
                            {% for field, error in errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Login Form -->
    <form method="POST" action="/login" class="space-y-6">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

        <div class="form-group">
            <label for="identifier" class="form-label">
                Email or Username <span class="text-danger-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-user text-gray-400"></i>
                </div>
                <input type="text"
                       id="identifier"
                       name="identifier"
                       value="{{ old_input.identifier ?? '' }}"
                       class="form-input pl-10 {{ errors.identifier ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                       placeholder="Enter your email or username"
                       required
                       autofocus>
            </div>
            {% if errors.identifier %}
                <p class="form-error">{{ errors.identifier }}</p>
            {% endif %}
        </div>

        <div class="form-group">
            <label for="password" class="form-label">
                Password <span class="text-danger-500">*</span>
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input type="password"
                       id="password"
                       name="password"
                       class="form-input pl-10 {{ errors.password ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : '' }}"
                       placeholder="Enter your password"
                       required>
            </div>
            {% if errors.password %}
                <p class="form-error">{{ errors.password }}</p>
            {% endif %}
        </div>

        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input type="checkbox"
                       id="remember"
                       name="remember"
                       value="1"
                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                <label for="remember" class="ml-2 block text-sm text-gray-900">
                    Remember me
                </label>
            </div>

            <div class="text-sm">
                <a href="/forgot-password" class="font-medium text-primary-600 hover:text-primary-500 transition-colors">
                    Forgot your password?
                </a>
            </div>
        </div>

        <div>
            <button type="submit" class="btn-primary w-full">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Sign in
            </button>
        </div>
    </form>

    <!-- Social Login -->
    <div class="relative">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">Or continue with</span>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-3">
        <a href="/auth/google" class="btn-outline w-full justify-center">
            <i class="fab fa-google mr-2 text-red-500"></i>
            Continue with Google
        </a>
        <a href="/auth/bing" class="btn-outline w-full justify-center">
            <i class="fab fa-microsoft mr-2 text-blue-500"></i>
            Continue with Microsoft
        </a>
    </div>

    <!-- Sign Up Link -->
    <div class="text-center">
        <p class="text-sm text-gray-600">
            Don't have an account?
            <a href="/register" class="font-medium text-primary-600 hover:text-primary-500 transition-colors">
                Sign up for free
            </a>
        </p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input if no autofocus attribute
    const firstInput = document.querySelector('input:not([type="hidden"])');
    if (firstInput && !document.querySelector('[autofocus]')) {
        firstInput.focus();
    }

    // Add loading state to form submission
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Signing in...';
    });
});
</script>
{% endblock %}
