{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-sign-in-alt fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">Login</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>

                    {% if errors %}
                        <div class="alert alert-danger">
                            {% for field, error in errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="POST" action="/login">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="mb-3">
                            <label for="identifier" class="form-label">Email or Username</label>
                            <input type="text" 
                                   class="form-control {{ errors.identifier ? 'is-invalid' : '' }}" 
                                   id="identifier" 
                                   name="identifier" 
                                   value="{{ old_input.identifier ?? '' }}" 
                                   required>
                            {% if errors.identifier %}
                                <div class="invalid-feedback">{{ errors.identifier }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" 
                                   class="form-control {{ errors.password ? 'is-invalid' : '' }}" 
                                   id="password" 
                                   name="password" 
                                   required>
                            {% if errors.password %}
                                <div class="invalid-feedback">{{ errors.password }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <!-- OAuth Login Options -->
                    <div class="d-grid gap-2">
                        <a href="/auth/google" class="btn btn-outline-danger">
                            <i class="fab fa-google me-2"></i>Login with Google
                        </a>
                        <a href="/auth/bing" class="btn btn-outline-info">
                            <i class="fab fa-microsoft me-2"></i>Login with Microsoft
                        </a>
                    </div>

                    <div class="text-center mt-4">
                        <p class="mb-2">
                            <a href="/forgot-password" class="text-decoration-none">Forgot your password?</a>
                        </p>
                        <p class="mb-0">
                            Don't have an account? 
                            <a href="/register" class="text-decoration-none">Sign up</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
