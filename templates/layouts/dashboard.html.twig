<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title ?? 'SEO Indexer Platform' }}{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <link href="/css/app.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico">
    
    {% block head %}{% endblock %}
</head>
<body class="h-full bg-gray-50">
    <!-- Flash Messages -->
    {% include 'components/flash-messages.html.twig' %}
    
    <!-- Dashboard Layout -->
    <div class="min-h-full">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <div class="flex h-16 justify-between">
                    <div class="flex">
                        <!-- Logo -->
                        <div class="flex flex-shrink-0 items-center">
                            <a href="/dashboard" class="text-xl font-bold text-gradient">
                                <i class="fas fa-search-plus mr-2"></i>
                                SEO Indexer
                            </a>
                        </div>
                        
                        <!-- Desktop Navigation -->
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <a href="/dashboard" class="nav-link {{ current_url == '/dashboard' ? 'nav-link-active' : 'nav-link-inactive' }}">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </a>
                            <a href="/dashboard/domains" class="nav-link {{ current_url starts with '/dashboard/domains' ? 'nav-link-active' : 'nav-link-inactive' }}">
                                <i class="fas fa-globe mr-2"></i>
                                Domains
                            </a>
                            <a href="/dashboard/urls" class="nav-link {{ current_url starts with '/dashboard/urls' ? 'nav-link-active' : 'nav-link-inactive' }}">
                                <i class="fas fa-link mr-2"></i>
                                URLs
                            </a>
                            <a href="/dashboard/reports" class="nav-link {{ current_url starts with '/dashboard/reports' ? 'nav-link-active' : 'nav-link-inactive' }}">
                                <i class="fas fa-chart-line mr-2"></i>
                                Reports
                            </a>
                        </div>
                    </div>
                    
                    <!-- User Menu -->
                    <div class="hidden sm:ml-6 sm:flex sm:items-center">
                        <!-- Notifications -->
                        <button type="button" class="relative rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            <span class="sr-only">View notifications</span>
                            <i class="fas fa-bell h-6 w-6"></i>
                        </button>
                        
                        <!-- Profile dropdown -->
                        <div class="relative ml-3">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-primary-700">
                                            {{ current_user.first_name|first|upper }}{{ current_user.last_name|first|upper }}
                                        </span>
                                    </div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        {{ current_user.first_name }} {{ current_user.last_name }}
                                    </p>
                                    <p class="text-sm text-gray-500 truncate">
                                        {{ current_user.email }}
                                    </p>
                                </div>
                                <div class="flex-shrink-0">
                                    <div class="relative inline-block text-left" x-data="{ open: false }">
                                        <button @click="open = !open" type="button" class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50" id="menu-button" aria-expanded="true" aria-haspopup="true">
                                            <i class="fas fa-chevron-down h-5 w-5 text-gray-400"></i>
                                        </button>
                                        
                                        <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                                            <div class="py-1" role="none">
                                                <a href="/profile" class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem">
                                                    <i class="fas fa-user mr-2"></i>
                                                    Profile Settings
                                                </a>
                                                {% if current_user.role == 'admin' %}
                                                    <a href="/admin" class="text-gray-700 block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem">
                                                        <i class="fas fa-cog mr-2"></i>
                                                        Admin Panel
                                                    </a>
                                                {% endif %}
                                                <div class="border-t border-gray-100"></div>
                                                <form method="POST" action="/logout">
                                                    <button type="submit" class="text-gray-700 block w-full px-4 py-2 text-left text-sm hover:bg-gray-100" role="menuitem">
                                                        <i class="fas fa-sign-out-alt mr-2"></i>
                                                        Sign out
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mobile menu button -->
                    <div class="-mr-2 flex items-center sm:hidden">
                        <button type="button" class="relative inline-flex items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" aria-controls="mobile-menu" aria-expanded="false">
                            <span class="sr-only">Open main menu</span>
                            <i class="fas fa-bars h-6 w-6"></i>
                        </button>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <main class="py-6">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>
    
    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
