<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title ?? 'SEO Indexer Platform' }}{% endblock %}</title>

    <!-- Tailwind CSS -->
    <link href="/css/app.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {% block head %}{% endblock %}
    
</head>
<body class="h-full">
    <div class="min-h-full">
        {% if is_logged_in is defined and is_logged_in %}
            <!-- Dashboard Layout -->
            <div class="flex h-screen bg-gray-50">
                <!-- Sidebar -->
                <div class="hidden md:flex md:w-64 md:flex-col">
                    <div class="flex flex-col flex-grow pt-5 overflow-y-auto bg-white border-r border-gray-200">
                        <!-- Logo -->
                        <div class="flex items-center flex-shrink-0 px-4">
                            <a href="/" class="flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg">
                                    <i class="fas fa-search text-white text-sm"></i>
                                </div>
                                <span class="ml-3 text-lg font-semibold text-gray-900">SEO Indexer</span>
                            </a>
                        </div>

                        <!-- Navigation -->
                        <div class="mt-8 flex-grow flex flex-col">
                            <nav class="flex-1 px-2 space-y-1">
                                <!-- Dashboard Section -->
                                <div class="space-y-1">
                                    <a href="/dashboard" class="nav-link {% if current_url is defined and current_url == '/dashboard' %}nav-link-active{% endif %}">
                                        <i class="fas fa-tachometer-alt nav-icon"></i>
                                        Dashboard
                                    </a>
                                    <a href="/dashboard/domains" class="nav-link {% if current_url is defined and current_url starts with '/dashboard/domains' %}nav-link-active{% endif %}">
                                        <i class="fas fa-globe nav-icon"></i>
                                        Domains
                                    </a>
                                    <a href="/dashboard/urls" class="nav-link {% if current_url is defined and current_url starts with '/dashboard/urls' %}nav-link-active{% endif %}">
                                        <i class="fas fa-link nav-icon"></i>
                                        URLs
                                    </a>
                                    <a href="/dashboard/sitemaps" class="nav-link {% if current_url is defined and current_url starts with '/dashboard/sitemaps' %}nav-link-active{% endif %}">
                                        <i class="fas fa-sitemap nav-icon"></i>
                                        Sitemaps
                                    </a>
                                    <a href="/dashboard/reports" class="nav-link {% if current_url is defined and current_url starts with '/dashboard/reports' %}nav-link-active{% endif %}">
                                        <i class="fas fa-chart-bar nav-icon"></i>
                                        Reports
                                    </a>
                                    <a href="/dashboard/connections" class="nav-link {% if current_url is defined and current_url starts with '/dashboard/connections' %}nav-link-active{% endif %}">
                                        <i class="fas fa-plug nav-icon"></i>
                                        Connections
                                    </a>
                                    <a href="/dashboard/api-keys" class="nav-link {% if current_url is defined and current_url starts with '/dashboard/api-keys' %}nav-link-active{% endif %}">
                                        <i class="fas fa-key nav-icon"></i>
                                        API Keys
                                    </a>
                                </div>

                                {% if is_admin is defined and is_admin %}
                                    <!-- Admin Section -->
                                    <div class="pt-6">
                                        <div class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                                            Administration
                                        </div>
                                        <div class="mt-2 space-y-1">
                                            <a href="/admin" class="nav-link {% if current_url is defined and current_url == '/admin' %}nav-link-active{% endif %}">
                                                <i class="fas fa-cog nav-icon"></i>
                                                Overview
                                            </a>
                                            <a href="/admin/users" class="nav-link {% if current_url is defined and current_url starts with '/admin/users' %}nav-link-active{% endif %}">
                                                <i class="fas fa-users nav-icon"></i>
                                                Users
                                            </a>
                                            <a href="/admin/domains" class="nav-link {% if current_url is defined and current_url starts with '/admin/domains' %}nav-link-active{% endif %}">
                                                <i class="fas fa-globe nav-icon"></i>
                                                All Domains
                                            </a>
                                            <a href="/admin/logs" class="nav-link {% if current_url is defined and current_url starts with '/admin/logs' %}nav-link-active{% endif %}">
                                                <i class="fas fa-file-alt nav-icon"></i>
                                                System Logs
                                            </a>
                                            <a href="/admin/settings" class="nav-link {% if current_url is defined and current_url starts with '/admin/settings' %}nav-link-active{% endif %}">
                                                <i class="fas fa-sliders-h nav-icon"></i>
                                                Settings
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </nav>
                        </div>

                        <!-- User Menu -->
                        <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                            <div class="flex items-center w-full group">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600 text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-700">
                                        {% if current_user.name is defined %}
                                            {{ current_user.name }}
                                        {% elseif user.name is defined %}
                                            {{ user.name }}
                                        {% else %}
                                            User
                                        {% endif %}
                                    </p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <a href="/profile" class="hover:text-gray-700">Profile</a>
                                        <span>•</span>
                                        <a href="/logout" class="hover:text-gray-700">Sign out</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main content -->
                <div class="flex flex-col flex-1 overflow-hidden">
                    <!-- Top navigation -->
                    <header class="bg-white shadow-sm border-b border-gray-200">
                        <div class="flex items-center justify-between px-4 py-4 sm:px-6 lg:px-8">
                            <!-- Mobile menu button -->
                            <button type="button" class="md:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" id="mobile-menu-button">
                                <span class="sr-only">Open sidebar</span>
                                <i class="fas fa-bars"></i>
                            </button>

                            <!-- Page title -->
                            <div class="flex-1 min-w-0">
                                <h1 class="text-lg font-medium text-gray-900 sm:truncate">
                                    {% block page_title %}{{ title ?? 'Dashboard' }}{% endblock %}
                                </h1>
                            </div>

                            <!-- Right side actions -->
                            <div class="flex items-center space-x-4">
                                {% block header_actions %}{% endblock %}

                                <!-- User menu -->
                                <div class="relative">
                                    <button type="button" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button">
                                        <span class="sr-only">Open user menu</span>
                                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                            <i class="fas fa-user text-gray-600 text-sm"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    <!-- Page content -->
                    <main class="flex-1 overflow-y-auto focus:outline-none">
                        <div class="py-6">
                            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                                {% block content %}{% endblock %}
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        {% else %}
            <!-- Public Layout -->
            <div class="min-h-full bg-gray-50">
                <!-- Navigation -->
                <nav class="bg-white shadow-sm border-b border-gray-200">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex justify-between h-16">
                            <!-- Logo -->
                            <div class="flex items-center">
                                <a href="/" class="flex items-center">
                                    <div class="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg">
                                        <i class="fas fa-search text-white text-sm"></i>
                                    </div>
                                    <span class="ml-3 text-lg font-semibold text-gray-900">SEO Indexer</span>
                                </a>
                            </div>

                            <!-- Navigation Links -->
                            <div class="flex items-center space-x-4">
                                <a href="/login" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                                    Sign in
                                </a>
                                <a href="/register" class="btn-primary">
                                    Get Started
                                </a>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Page content -->
                <main class="flex-1">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        {% block content %}{% endblock %}
                    </div>
                </main>
            </div>
        {% endif %}

        <!-- Flash Messages -->
        <div id="flash-messages" class="fixed top-4 right-4 z-50 space-y-2" style="max-width: 400px;">
            {% for type, message in flash_messages %}
                <div class="alert-{{ type == 'error' ? 'danger' : type }} animate-slide-in">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if type == 'success' %}
                                <i class="fas fa-check-circle text-green-400 h-5 w-5"></i>
                            {% elseif type == 'error' or type == 'danger' %}
                                <i class="fas fa-exclamation-circle text-red-400 h-5 w-5"></i>
                            {% elseif type == 'warning' %}
                                <i class="fas fa-exclamation-triangle text-yellow-400 h-5 w-5"></i>
                            {% else %}
                                <i class="fas fa-info-circle text-blue-400 h-5 w-5"></i>
                            {% endif %}
                        </div>
                        <div class="ml-3 flex-1">
                            <p class="text-sm font-medium">{{ message }}</p>
                        </div>
                        <div class="ml-4 flex-shrink-0">
                            <button type="button" class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
                                <span class="sr-only">Close</span>
                                <i class="fas fa-times h-4 w-4"></i>
                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

    <!-- Scripts -->
    {% block scripts %}
    <script>
        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('#flash-messages > div');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    message.style.transform = 'translateX(100%)';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    // Add mobile menu functionality here
                    console.log('Mobile menu clicked');
                });
            }
        });
    </script>
    {% endblock %}
</body>
</html>
