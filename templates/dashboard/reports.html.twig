{% extends "layouts/base.html.twig" %}

{% block head %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Reports & Analytics</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportReport()">
                <i class="fas fa-download me-1"></i>Export
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- Overview Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total URLs Submitted</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analytics.url_stats.total }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-link fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Success Rate</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analytics.success_rate.overall }}%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Verified Domains</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analytics.domain_stats.verified }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Sitemaps Processed</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analytics.sitemap_stats.completed }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Submission Trends Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">URL Submission Trends (Last 30 Days)</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="submissionTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Provider Distribution -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Provider Distribution</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="providerChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="mr-2">
                        <i class="fas fa-circle text-danger"></i> Google
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-info"></i> Bing
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row mb-4">
    <!-- API Usage -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">API Usage by Endpoint</h6>
            </div>
            <div class="card-body">
                {% if analytics.api_usage.by_endpoint %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Provider</th>
                                    <th>Endpoint</th>
                                    <th>Requests</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for usage in analytics.api_usage.by_endpoint %}
                                    <tr>
                                        <td>
                                            {% if usage.provider == 'google' %}
                                                <span class="badge bg-danger">Google</span>
                                            {% else %}
                                                <span class="badge bg-info">Bing</span>
                                            {% endif %}
                                        </td>
                                        <td><code>{{ usage.endpoint }}</code></td>
                                        <td><strong>{{ usage.total_requests }}</strong></td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No API usage data available.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Domain Performance -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Domain Performance</h6>
            </div>
            <div class="card-body">
                {% if analytics.domain_stats.by_domain %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Domain</th>
                                    <th>URLs Submitted</th>
                                    <th>Protocol</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for domain in analytics.domain_stats.by_domain %}
                                    <tr>
                                        <td>{{ domain.domain }}</td>
                                        <td><strong>{{ domain.url_count }}</strong></td>
                                        <td>
                                            <span class="badge bg-{{ domain.protocol == 'https' ? 'success' : 'warning' }}">
                                                {{ domain.protocol|upper }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No domain data available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Success Rate Breakdown -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Success Rate Breakdown</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <h4 class="text-primary">{{ analytics.success_rate.overall }}%</h4>
                        <p class="text-muted">Overall Success Rate</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h4 class="text-danger">{{ analytics.success_rate.google }}%</h4>
                        <p class="text-muted">Google Success Rate</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h4 class="text-info">{{ analytics.success_rate.bing }}%</h4>
                        <p class="text-muted">Bing Success Rate</p>
                    </div>
                </div>
                
                <div class="progress mb-3">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: {{ analytics.success_rate.overall }}%" 
                         aria-valuenow="{{ analytics.success_rate.overall }}" 
                         aria-valuemin="0" aria-valuemax="100">
                        {{ analytics.success_rate.overall }}%
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
            </div>
            <div class="card-body">
                {% if analytics.recent_activity %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>URL</th>
                                    <th>Domain</th>
                                    <th>Provider</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in analytics.recent_activity %}
                                    <tr>
                                        <td>
                                            <a href="{{ activity.url }}" target="_blank" class="text-decoration-none">
                                                {{ activity.url|length > 50 ? activity.url|slice(0, 50) ~ '...' : activity.url }}
                                            </a>
                                        </td>
                                        <td>{{ activity.domain }}</td>
                                        <td>
                                            {% if activity.provider == 'google' %}
                                                <span class="badge bg-danger">Google</span>
                                            {% else %}
                                                <span class="badge bg-info">Bing</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% set status_class = {
                                                'submitted': 'success',
                                                'failed': 'danger',
                                                'pending': 'warning'
                                            } %}
                                            <span class="badge bg-{{ status_class[activity.status] ?? 'secondary' }}">
                                                {{ activity.status|title }}
                                            </span>
                                        </td>
                                        <td>{{ activity.submitted_at|date('M d, H:i') }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No recent activity.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }
.chart-area { position: relative; height: 320px; }
.chart-pie { position: relative; height: 245px; }
</style>

<script>
// Submission Trends Chart
const submissionData = {{ analytics.daily_submissions|json_encode|raw }};
const ctx1 = document.getElementById('submissionTrendsChart').getContext('2d');

// Process data for chart
const dates = [...new Set(submissionData.map(item => item.date))].sort();
const googleData = dates.map(date => {
    const item = submissionData.find(d => d.date === date && d.provider === 'google');
    return item ? item.count : 0;
});
const bingData = dates.map(date => {
    const item = submissionData.find(d => d.date === date && d.provider === 'bing');
    return item ? item.count : 0;
});

new Chart(ctx1, {
    type: 'line',
    data: {
        labels: dates.map(date => new Date(date).toLocaleDateString()),
        datasets: [{
            label: 'Google',
            data: googleData,
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.3
        }, {
            label: 'Bing',
            data: bingData,
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            tension: 0.3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Provider Distribution Chart
const ctx2 = document.getElementById('providerChart').getContext('2d');
new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ['Google', 'Bing'],
        datasets: [{
            data: [{{ analytics.url_stats.google }}, {{ analytics.url_stats.bing }}],
            backgroundColor: ['#dc3545', '#17a2b8'],
            hoverBackgroundColor: ['#c82333', '#138496']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

function exportReport() {
    // This would generate and download a report
    alert('Export functionality would be implemented here');
}
</script>
{% endblock %}
