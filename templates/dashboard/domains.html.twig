{% extends "layouts/dashboard.html.twig" %}

{% block title %}Domain Management - {{ parent() }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Domain Management</h1>
            <p class="mt-1 text-sm text-gray-500">
                Manage your domains and verify ownership for indexing
            </p>
        </div>
        <div class="flex space-x-3">
            <button type="button"
                    class="btn-primary"
                    onclick="openModal('addDomainModal')"
                    aria-label="Add new domain">
                <i class="fas fa-plus mr-2"></i>
                Add Domain
            </button>
        </div>
    </div>

    <!-- Domain Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Total Domains
                        </p>
                        <p class="text-3xl font-bold text-gray-900">{{ domains|length }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-globe text-primary-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Verified
                        </p>
                        <p class="text-3xl font-bold text-gray-900">
                            {{ domains|filter(d => d.verified)|length }}
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-success-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Pending
                        </p>
                        <p class="text-3xl font-bold text-gray-900">
                            {{ domains|filter(d => not d.verified)|length }}
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-warning-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Domains List -->
    {% embed 'components/data-table.html.twig' with {
        'title': 'Your Domains',
        'description': 'Manage your domains and verification status',
        'headers': [
            {'key': 'domain', 'label': 'Domain', 'sortable': true},
            {'key': 'protocol', 'label': 'Protocol', 'sortable': false},
            {'key': 'status', 'label': 'Status', 'sortable': true},
            {'key': 'connections', 'label': 'API Connections', 'sortable': false},
            {'key': 'created_at', 'label': 'Added', 'sortable': true},
            {'key': 'actions', 'label': 'Actions', 'sortable': false}
        ],
        'data': domains,
        'searchable': true,
        'pagination': true
    } %}
        {% block table_actions %}
            <div class="flex items-center space-x-2">
                {% if not item.verified %}
                    <button type="button"
                            class="btn-sm btn-success text-xs"
                            onclick="showVerificationInstructions({{ item.id }}, '{{ item.domain }}', '{{ item.protocol }}', '{{ item.verification_token }}')"
                            title="Verify Domain">
                        <i class="fas fa-check mr-1"></i>
                        Verify
                    </button>
                {% endif %}
                <button type="button"
                        class="btn-sm btn-outline text-xs"
                        onclick="showDomainDetails({{ item.id }})"
                        title="View Details">
                    <i class="fas fa-info-circle mr-1"></i>
                    Details
                </button>
                <button type="button"
                        class="btn-sm btn-danger text-xs"
                        onclick="deleteDomain({{ item.id }}, '{{ item.domain }}')"
                        title="Delete Domain">
                    <i class="fas fa-trash mr-1"></i>
                    Delete
                </button>
            </div>
        {% endblock %}
    {% endembed %}

<!-- Add Domain Modal -->
<div class="modal fade" id="addDomainModal" tabindex="-1" aria-labelledby="addDomainModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDomainModalLabel">Add New Domain</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/dashboard/domains/add">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    
                    <div class="mb-3">
                        <label for="domain" class="form-label">Domain Name</label>
                        <input type="text" class="form-control" id="domain" name="domain" 
                               placeholder="example.com" required>
                        <div class="form-text">Enter your domain without http:// or https://</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="protocol" class="form-label">Protocol</label>
                        <select class="form-select" id="protocol" name="protocol" required>
                            <option value="https">HTTPS (Recommended)</option>
                            <option value="http">HTTP</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> After adding your domain, you'll need to verify ownership by uploading a verification file to your website.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Domain
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Domain Verification Modal -->
<div class="modal fade" id="verificationModal" tabindex="-1" aria-labelledby="verificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verificationModalLabel">Domain Verification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="verificationContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="verifyDomain()">
                    <i class="fas fa-check me-2"></i>Verify Domain
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }
</style>

<script>
let currentDomainId = null;

function showVerificationInstructions(domainId, domain, protocol, token) {
    currentDomainId = domainId;
    
    const verificationUrl = `${protocol}://${domain}/seo-indexer-verification-${token}.html`;
    const fileContent = `seo-indexer-verification-${token}`;
    
    const content = `
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Verification Instructions for: <strong>${domain}</strong></h6>
        </div>
        
        <div class="mb-4">
            <h6>Step 1: Create Verification File</h6>
            <p>Create a file named <code>seo-indexer-verification-${token}.html</code> with the following content:</p>
            <div class="bg-light p-3 rounded">
                <code>${fileContent}</code>
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${fileContent}')">
                    <i class="fas fa-copy"></i> Copy
                </button>
            </div>
        </div>
        
        <div class="mb-4">
            <h6>Step 2: Upload to Your Website</h6>
            <p>Upload this file to the root directory of your website so it's accessible at:</p>
            <div class="bg-light p-3 rounded">
                <code>${verificationUrl}</code>
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${verificationUrl}')">
                    <i class="fas fa-copy"></i> Copy URL
                </button>
            </div>
        </div>
        
        <div class="mb-4">
            <h6>Step 3: Verify</h6>
            <p>Once you've uploaded the file, click the "Verify Domain" button below to complete the verification process.</p>
        </div>
        
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Important:</strong> Make sure the file is accessible via HTTP/HTTPS and contains exactly the content shown above.
        </div>
    `;
    
    document.getElementById('verificationContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('verificationModal')).show();
}

function verifyDomain() {
    if (!currentDomainId) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/dashboard/domains/verify';
    
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token }}';
    
    const domainInput = document.createElement('input');
    domainInput.type = 'hidden';
    domainInput.name = 'domain_id';
    domainInput.value = currentDomainId;
    
    form.appendChild(csrfInput);
    form.appendChild(domainInput);
    document.body.appendChild(form);
    form.submit();
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>Copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        new bootstrap.Toast(toast).show();
        setTimeout(() => toast.remove(), 3000);
    });
}

function showDomainDetails(domainId) {
    // This would show detailed domain information
    alert('Domain details for ID: ' + domainId);
}

function deleteDomain(domainId, domain) {
    if (confirm(`Are you sure you want to delete the domain "${domain}"? This action cannot be undone.`)) {
        fetch(`/dashboard/domains/${domainId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete domain');
        });
    }
}
</script>
{% endblock %}
