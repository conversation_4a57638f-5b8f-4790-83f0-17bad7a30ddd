{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Domain Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addDomainModal">
                <i class="fas fa-plus me-1"></i>Add Domain
            </button>
        </div>
    </div>
</div>

<!-- Domain Statistics -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Domains</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ domains|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-globe fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Verified</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ domains|filter(d => d.verified)|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ domains|filter(d => not d.verified)|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Domains List -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Your Domains</h6>
            </div>
            <div class="card-body">
                {% if domains %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Domain</th>
                                    <th>Protocol</th>
                                    <th>Status</th>
                                    <th>API Connections</th>
                                    <th>Added</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for domain in domains %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if domain.verified %}
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                {% else %}
                                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                                {% endif %}
                                                <strong>{{ domain.domain }}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ domain.protocol == 'https' ? 'success' : 'warning' }}">
                                                {{ domain.protocol|upper }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if domain.verified %}
                                                <span class="badge bg-success">Verified</span>
                                            {% else %}
                                                <span class="badge bg-warning">Pending Verification</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                {% if domain.google_connected %}
                                                    <span class="badge bg-danger" title="Google Connected">
                                                        <i class="fab fa-google"></i>
                                                    </span>
                                                {% endif %}
                                                {% if domain.bing_connected %}
                                                    <span class="badge bg-info" title="Bing Connected">
                                                        <i class="fab fa-microsoft"></i>
                                                    </span>
                                                {% endif %}
                                                {% if not domain.google_connected and not domain.bing_connected %}
                                                    <span class="text-muted">None</span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ domain.created_at|date('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                {% if not domain.verified %}
                                                    <button type="button" class="btn btn-outline-success btn-sm" 
                                                            onclick="showVerificationInstructions({{ domain.id }}, '{{ domain.domain }}', '{{ domain.protocol }}', '{{ domain.verification_token }}')"
                                                            title="Verify Domain">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                {% endif %}
                                                <button type="button" class="btn btn-outline-info btn-sm" 
                                                        onclick="showDomainDetails({{ domain.id }})"
                                                        title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                                        onclick="deleteDomain({{ domain.id }}, '{{ domain.domain }}')"
                                                        title="Delete Domain">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No domains added yet</h5>
                        <p class="text-muted">Add your first domain to start submitting URLs for indexing.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDomainModal">
                            <i class="fas fa-plus me-2"></i>Add Your First Domain
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Domain Modal -->
<div class="modal fade" id="addDomainModal" tabindex="-1" aria-labelledby="addDomainModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDomainModalLabel">Add New Domain</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/dashboard/domains/add">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    
                    <div class="mb-3">
                        <label for="domain" class="form-label">Domain Name</label>
                        <input type="text" class="form-control" id="domain" name="domain" 
                               placeholder="example.com" required>
                        <div class="form-text">Enter your domain without http:// or https://</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="protocol" class="form-label">Protocol</label>
                        <select class="form-select" id="protocol" name="protocol" required>
                            <option value="https">HTTPS (Recommended)</option>
                            <option value="http">HTTP</option>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> After adding your domain, you'll need to verify ownership by uploading a verification file to your website.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Domain
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Domain Verification Modal -->
<div class="modal fade" id="verificationModal" tabindex="-1" aria-labelledby="verificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verificationModalLabel">Domain Verification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="verificationContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" onclick="verifyDomain()">
                    <i class="fas fa-check me-2"></i>Verify Domain
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }
</style>

<script>
let currentDomainId = null;

function showVerificationInstructions(domainId, domain, protocol, token) {
    currentDomainId = domainId;
    
    const verificationUrl = `${protocol}://${domain}/seo-indexer-verification-${token}.html`;
    const fileContent = `seo-indexer-verification-${token}`;
    
    const content = `
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Verification Instructions for: <strong>${domain}</strong></h6>
        </div>
        
        <div class="mb-4">
            <h6>Step 1: Create Verification File</h6>
            <p>Create a file named <code>seo-indexer-verification-${token}.html</code> with the following content:</p>
            <div class="bg-light p-3 rounded">
                <code>${fileContent}</code>
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${fileContent}')">
                    <i class="fas fa-copy"></i> Copy
                </button>
            </div>
        </div>
        
        <div class="mb-4">
            <h6>Step 2: Upload to Your Website</h6>
            <p>Upload this file to the root directory of your website so it's accessible at:</p>
            <div class="bg-light p-3 rounded">
                <code>${verificationUrl}</code>
                <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${verificationUrl}')">
                    <i class="fas fa-copy"></i> Copy URL
                </button>
            </div>
        </div>
        
        <div class="mb-4">
            <h6>Step 3: Verify</h6>
            <p>Once you've uploaded the file, click the "Verify Domain" button below to complete the verification process.</p>
        </div>
        
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Important:</strong> Make sure the file is accessible via HTTP/HTTPS and contains exactly the content shown above.
        </div>
    `;
    
    document.getElementById('verificationContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('verificationModal')).show();
}

function verifyDomain() {
    if (!currentDomainId) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/dashboard/domains/verify';
    
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token }}';
    
    const domainInput = document.createElement('input');
    domainInput.type = 'hidden';
    domainInput.name = 'domain_id';
    domainInput.value = currentDomainId;
    
    form.appendChild(csrfInput);
    form.appendChild(domainInput);
    document.body.appendChild(form);
    form.submit();
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>Copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        new bootstrap.Toast(toast).show();
        setTimeout(() => toast.remove(), 3000);
    });
}

function showDomainDetails(domainId) {
    // This would show detailed domain information
    alert('Domain details for ID: ' + domainId);
}

function deleteDomain(domainId, domain) {
    if (confirm(`Are you sure you want to delete the domain "${domain}"? This action cannot be undone.`)) {
        fetch(`/dashboard/domains/${domainId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete domain');
        });
    }
}
</script>
{% endblock %}
