{% extends "layouts/base.html.twig" %}

{% block title %}Domain Management{% endblock %}

{% block page_title %}Domain Management{% endblock %}

{% block header_actions %}
    <button type="button" class="btn-primary" onclick="openModal('addDomainModal')">
        <i class="fas fa-plus mr-2"></i>
        Add Domain
    </button>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Domain Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="stat-card stat-card-primary">
            <div class="stat-card-content">
                <div class="stat-card-body">
                    <div class="stat-card-title">Total Domains</div>
                    <div class="stat-card-value">{{ domains|length }}</div>
                </div>
                <div class="stat-card-icon">
                    <i class="fas fa-globe text-primary-400"></i>
                </div>
            </div>
        </div>

        <div class="stat-card stat-card-success">
            <div class="stat-card-content">
                <div class="stat-card-body">
                    <div class="stat-card-title">Verified</div>
                    <div class="stat-card-value">{{ domains|filter(d => d.verified)|length }}</div>
                </div>
                <div class="stat-card-icon">
                    <i class="fas fa-check-circle text-success-400"></i>
                </div>
            </div>
        </div>

        <div class="stat-card stat-card-warning">
            <div class="stat-card-content">
                <div class="stat-card-body">
                    <div class="stat-card-title">Pending</div>
                    <div class="stat-card-value">{{ domains|filter(d => not d.verified)|length }}</div>
                </div>
                <div class="stat-card-icon">
                    <i class="fas fa-clock text-warning-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Domains List -->
    {% embed 'components/data-table.html.twig' with {
        'title': 'Your Domains',
        'description': 'Manage your verified domains and their settings',
        'headers': [
            {'key': 'domain', 'label': 'Domain', 'sortable': true},
            {'key': 'protocol', 'label': 'Protocol', 'sortable': false},
            {'key': 'status', 'label': 'Status', 'sortable': true},
            {'key': 'connections', 'label': 'API Connections', 'sortable': false},
            {'key': 'created_at', 'label': 'Added', 'sortable': true},
            {'key': 'actions', 'label': 'Actions', 'sortable': false}
        ],
        'data': domains ?? [],
        'searchable': true,
        'pagination': true
    } %}
        {% block table_actions %}
            <button type="button" class="btn-primary" onclick="openModal('addDomainModal')">
                <i class="fas fa-plus mr-2"></i>
                Add Domain
            </button>
        {% endblock %}
    {% endembed %}

    {% if not domains %}
        <div class="card mt-6">
            <div class="card-body text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-globe text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No domains added yet</h3>
                <p class="text-gray-500 mb-6">Add your first domain to start submitting URLs for indexing.</p>
                <button type="button" class="btn-primary" onclick="openModal('addDomainModal')">
                    <i class="fas fa-plus mr-2"></i>
                    Add Your First Domain
                </button>
            </div>
        </div>
    {% endif %}

    <!-- Add Domain Modal -->
    {% embed 'components/modal.html.twig' with {
        'id': 'addDomainModal',
        'title': 'Add New Domain',
        'size': 'md'
    } %}
        {% block modal_body %}
            <form id="addDomainForm" method="POST" action="/dashboard/domains/add">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                <div class="space-y-4">
                    <div>
                        <label for="domain" class="form-label">Domain Name</label>
                        <input type="text"
                               id="domain"
                               name="domain"
                               class="form-input"
                               placeholder="example.com"
                               required>
                        <p class="form-help">Enter your domain without protocol (http/https)</p>
                    </div>

                    <div>
                        <label for="protocol" class="form-label">Protocol</label>
                        <select id="protocol" name="protocol" class="form-select" required>
                            <option value="https">HTTPS (Recommended)</option>
                            <option value="http">HTTP</option>
                        </select>
                        <p class="form-help">Choose the protocol your website uses</p>
                    </div>
                </div>
            </form>
        {% endblock %}

        {% block modal_footer %}
            <button type="button" class="btn-outline" onclick="closeModal('addDomainModal')">
                Cancel
            </button>
            <button type="submit" form="addDomainForm" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                Add Domain
            </button>
        {% endblock %}
    {% endembed %}

    <!-- Domain Verification Modal -->
    {% embed 'components/modal.html.twig' with {
        'id': 'verificationModal',
        'title': 'Domain Verification',
        'size': 'lg'
    } %}
        {% block modal_body %}
            <div id="verificationContent">
                <!-- Content will be populated by JavaScript -->
            </div>
        {% endblock %}

        {% block modal_footer %}
            <button type="button" class="btn-outline" onclick="closeModal('verificationModal')">
                Close
            </button>
            <button type="button" class="btn-success" onclick="checkVerification()">
                <i class="fas fa-check mr-2"></i>
                Check Verification
            </button>
        {% endblock %}
    {% endembed %}

    <!-- JavaScript for domain management -->
    <script>
        // Domain management functions
        function verifyDomain(domainId) {
            fetch(`/dashboard/domains/${domainId}/verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Verification failed: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred during verification');
            });
        }

        function deleteDomain(domainId) {
            if (confirm('Are you sure you want to delete this domain? This action cannot be undone.')) {
                fetch(`/dashboard/domains/${domainId}/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Delete failed: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred during deletion');
                });
            }
        }
    </script>
</div>
{% endblock %}
