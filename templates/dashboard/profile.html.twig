{% extends 'layouts/base.html.twig' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Profile Settings</h1>
            <p class="mt-1 text-sm text-gray-500">Manage your account settings and preferences</p>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Personal Information</h3>
                    <p class="mt-1 text-sm text-gray-500">Update your personal details and account information</p>
                </div>
                <div class="card-body">
                    <form method="POST" action="/profile/update" class="space-y-6">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <!-- Name Field -->
                        <div>
                            <label for="name" class="form-label">Full Name</label>
                            <input 
                                type="text" 
                                id="name" 
                                name="name" 
                                value="{{ old_input.name ?? user.name }}"
                                class="form-input {% if errors.name %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                required
                            >
                            {% if errors.name %}
                                <p class="form-error">{{ errors.name }}</p>
                            {% endif %}
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="form-label">Email Address</label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                value="{{ old_input.email ?? user.email }}"
                                class="form-input {% if errors.email %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                required
                            >
                            {% if errors.email %}
                                <p class="form-error">{{ errors.email }}</p>
                            {% endif %}
                        </div>

                        <!-- Password Change Section -->
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="text-base font-medium text-gray-900 mb-4">Change Password</h4>
                            <p class="text-sm text-gray-500 mb-4">Leave blank to keep your current password</p>
                            
                            <div class="space-y-4">
                                <!-- Current Password -->
                                <div>
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input 
                                        type="password" 
                                        id="current_password" 
                                        name="current_password"
                                        class="form-input {% if errors.current_password %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                        autocomplete="current-password"
                                    >
                                    {% if errors.current_password %}
                                        <p class="form-error">{{ errors.current_password }}</p>
                                    {% endif %}
                                </div>

                                <!-- New Password -->
                                <div>
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input 
                                        type="password" 
                                        id="new_password" 
                                        name="new_password"
                                        class="form-input {% if errors.new_password %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                        autocomplete="new-password"
                                        minlength="8"
                                    >
                                    {% if errors.new_password %}
                                        <p class="form-error">{{ errors.new_password }}</p>
                                    {% endif %}
                                    <p class="form-help">Must be at least 8 characters long</p>
                                </div>

                                <!-- Confirm Password -->
                                <div>
                                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                                    <input 
                                        type="password" 
                                        id="confirm_password" 
                                        name="confirm_password"
                                        class="form-input {% if errors.confirm_password %}border-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                        autocomplete="new-password"
                                    >
                                    {% if errors.confirm_password %}
                                        <p class="form-error">{{ errors.confirm_password }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-6 border-t border-gray-200">
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save mr-2"></i>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Account Stats -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Account Statistics</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Total Domains</span>
                            <span class="text-sm font-medium text-gray-900">{{ stats.total_domains }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Total URLs</span>
                            <span class="text-sm font-medium text-gray-900">{{ stats.total_urls }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">Indexed URLs</span>
                            <span class="text-sm font-medium text-gray-900">{{ stats.indexed_urls }}</span>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <span class="text-sm text-gray-500">Member Since</span>
                            <span class="text-sm font-medium text-gray-900">{{ stats.member_since|date('M Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connected Services -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Connected Services</h3>
                    <p class="mt-1 text-sm text-gray-500">Manage your API connections</p>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <!-- Google Search Console -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fab fa-google text-red-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Google Search Console</p>
                                    <p class="text-xs text-gray-500">Submit URLs to Google</p>
                                </div>
                            </div>
                            {% if connected_providers.google ?? false %}
                                <span class="badge-success text-xs">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Connected
                                </span>
                            {% else %}
                                <a href="/auth/google" class="btn-sm btn-outline text-xs">
                                    Connect
                                </a>
                            {% endif %}
                        </div>

                        <!-- Bing Webmaster Tools -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fab fa-microsoft text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Bing Webmaster Tools</p>
                                    <p class="text-xs text-gray-500">Submit URLs to Bing</p>
                                </div>
                            </div>
                            {% if connected_providers.bing ?? false %}
                                <span class="badge-success text-xs">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Connected
                                </span>
                            {% else %}
                                <a href="/auth/bing" class="btn-sm btn-outline text-xs">
                                    Connect
                                </a>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <a href="/dashboard/connections" class="btn-outline w-full justify-center text-sm">
                            <i class="fas fa-cog mr-2"></i>
                            Manage Connections
                        </a>
                    </div>
                </div>
            </div>

            <!-- Account Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Account Actions</h3>
                </div>
                <div class="card-body">
                    <div class="space-y-3">
                        <a href="/dashboard" class="btn-outline w-full justify-center text-sm">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Back to Dashboard
                        </a>
                        <a href="/logout" class="btn-outline w-full justify-center text-sm text-red-600 border-red-300 hover:bg-red-50">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswordMatch() {
        if (newPassword.value && confirmPassword.value) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    newPassword.addEventListener('input', validatePasswordMatch);
    confirmPassword.addEventListener('input', validatePasswordMatch);
    
    // Show/hide password change section based on current password input
    const currentPassword = document.getElementById('current_password');
    const passwordSection = currentPassword.closest('.border-t');
    
    currentPassword.addEventListener('input', function() {
        if (this.value) {
            newPassword.setAttribute('required', 'required');
            confirmPassword.setAttribute('required', 'required');
        } else {
            newPassword.removeAttribute('required');
            confirmPassword.removeAttribute('required');
        }
    });
});
</script>
{% endblock %}
