{% extends 'layouts/dashboard.html.twig' %}

{% block title %}{{ title }} - SEO Indexer Platform{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white shadow-soft rounded-xl border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Profile Settings</h1>
                    <p class="mt-1 text-sm text-gray-500">
                        Manage your account settings and preferences
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                        <span class="text-lg font-semibold text-primary-700">
                            {{ user.first_name|first|upper }}{{ user.last_name|first|upper }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- User Stats -->
        <div class="px-6 py-4">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary-600">{{ stats.total_domains }}</div>
                    <div class="text-sm text-gray-500">Total Domains</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-success-600">{{ stats.verified_domains }}</div>
                    <div class="text-sm text-gray-500">Verified</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-warning-600">{{ stats.total_urls }}</div>
                    <div class="text-sm text-gray-500">URLs Submitted</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-success-600">{{ stats.successful_submissions }}</div>
                    <div class="text-sm text-gray-500">Indexed</div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Information -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-user mr-2 text-primary-600"></i>
                        Personal Information
                    </h3>
                    <p class="mt-1 text-sm text-gray-500">
                        Update your personal details and contact information
                    </p>
                </div>
                
                <form method="POST" action="/profile/update" class="card-body space-y-6">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="first_name" class="form-label">
                                First Name <span class="text-danger-500">*</span>
                            </label>
                            <input type="text" 
                                   id="first_name" 
                                   name="first_name" 
                                   value="{{ user.first_name }}" 
                                   class="form-input"
                                   required>
                        </div>
                        
                        <div class="form-group">
                            <label for="last_name" class="form-label">
                                Last Name <span class="text-danger-500">*</span>
                            </label>
                            <input type="text" 
                                   id="last_name" 
                                   name="last_name" 
                                   value="{{ user.last_name }}" 
                                   class="form-input"
                                   required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">
                            Email Address <span class="text-danger-500">*</span>
                        </label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="{{ user.email }}" 
                               class="form-input"
                               required>
                        {% if not user.email_verified %}
                            <p class="form-help text-warning-600">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Email not verified. Please check your inbox.
                            </p>
                        {% endif %}
                    </div>
                    
                    <div class="divider"></div>
                    
                    <!-- Password Change Section -->
                    <div x-data="{ showPasswordFields: false }">
                        <div class="flex items-center justify-between">
                            <h4 class="text-md font-medium text-gray-900">
                                <i class="fas fa-lock mr-2 text-primary-600"></i>
                                Change Password
                            </h4>
                            <button type="button" 
                                    @click="showPasswordFields = !showPasswordFields"
                                    class="btn-ghost btn-sm">
                                <span x-text="showPasswordFields ? 'Cancel' : 'Change Password'"></span>
                            </button>
                        </div>
                        
                        <div x-show="showPasswordFields" 
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="mt-4 space-y-4">
                            
                            <div class="form-group">
                                <label for="current_password" class="form-label">
                                    Current Password <span class="text-danger-500">*</span>
                                </label>
                                <input type="password" 
                                       id="current_password" 
                                       name="current_password" 
                                       class="form-input">
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label for="new_password" class="form-label">
                                        New Password <span class="text-danger-500">*</span>
                                    </label>
                                    <input type="password" 
                                           id="new_password" 
                                           name="new_password" 
                                           class="form-input"
                                           minlength="8">
                                    <p class="form-help">
                                        Minimum 8 characters required
                                    </p>
                                </div>
                                
                                <div class="form-group">
                                    <label for="confirm_password" class="form-label">
                                        Confirm Password <span class="text-danger-500">*</span>
                                    </label>
                                    <input type="password" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           class="form-input">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" 
                                onclick="window.location.reload()" 
                                class="btn-outline">
                            <i class="fas fa-undo mr-2"></i>
                            Reset
                        </button>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Account Information Sidebar -->
        <div class="space-y-6">
            <!-- Account Status -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-shield-alt mr-2 text-primary-600"></i>
                        Account Status
                    </h3>
                </div>
                <div class="card-body space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Account Type</span>
                        <span class="badge badge-primary">{{ user.role|title }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Status</span>
                        {% if user.status == 'active' %}
                            <span class="badge badge-success">
                                <i class="fas fa-check mr-1"></i>
                                Active
                            </span>
                        {% elseif user.status == 'pending' %}
                            <span class="badge badge-warning">
                                <i class="fas fa-clock mr-1"></i>
                                Pending
                            </span>
                        {% else %}
                            <span class="badge badge-danger">
                                <i class="fas fa-ban mr-1"></i>
                                {{ user.status|title }}
                            </span>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Email Verified</span>
                        {% if user.email_verified %}
                            <span class="badge badge-success">
                                <i class="fas fa-check mr-1"></i>
                                Verified
                            </span>
                        {% else %}
                            <span class="badge badge-warning">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Unverified
                            </span>
                        {% endif %}
                    </div>
                    
                    <div class="divider"></div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Member Since</span>
                            <span class="text-gray-900">{{ stats.member_since }}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Last Login</span>
                            <span class="text-gray-900">{{ stats.last_login }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-bolt mr-2 text-primary-600"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="card-body space-y-3">
                    <a href="/dashboard/domains" class="btn-outline w-full justify-start">
                        <i class="fas fa-globe mr-2"></i>
                        Manage Domains
                    </a>
                    <a href="/dashboard/urls" class="btn-outline w-full justify-start">
                        <i class="fas fa-link mr-2"></i>
                        Submit URLs
                    </a>
                    <a href="/dashboard/reports" class="btn-outline w-full justify-start">
                        <i class="fas fa-chart-line mr-2"></i>
                        View Reports
                    </a>
                    {% if not user.email_verified %}
                        <button type="button" class="btn-warning w-full justify-start">
                            <i class="fas fa-envelope mr-2"></i>
                            Resend Verification
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
