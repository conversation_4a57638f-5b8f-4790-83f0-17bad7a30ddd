{% extends 'layouts/base.html.twig' %}

{% block title %}{{ title }}{% endblock %}

{% block head %}
<style>
    /* Modern Tailwind-inspired styles for profile page */
    .profile-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
    }

    .profile-card-header {
        padding: 1.5rem 1.5rem 0 1.5rem;
        border-bottom: 1px solid #f3f4f6;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
    }

    .profile-card-body {
        padding: 0 1.5rem 1.5rem 1.5rem;
    }

    .profile-form-group {
        margin-bottom: 1.5rem;
    }

    .profile-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .profile-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: all 0.15s ease-in-out;
    }

    .profile-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .profile-input.error {
        border-color: #ef4444;
    }

    .profile-input.error:focus {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .profile-error {
        color: #ef4444;
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }

    .profile-btn {
        background-color: #3b82f6;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        border: none;
        cursor: pointer;
        transition: background-color 0.15s ease-in-out;
    }

    .profile-btn:hover {
        background-color: #2563eb;
    }

    .profile-btn-outline {
        background-color: white;
        color: #374151;
        border: 1px solid #d1d5db;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.15s ease-in-out;
    }

    .profile-btn-outline:hover {
        background-color: #f9fafb;
        color: #374151;
        text-decoration: none;
    }

    .profile-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f3f4f6;
    }

    .profile-stats:last-child {
        border-bottom: none;
    }

    .profile-stat-label {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .profile-stat-value {
        font-size: 0.875rem;
        font-weight: 500;
        color: #111827;
    }

    .profile-connection-status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .profile-connection-connected {
        background-color: #dcfce7;
        color: #166534;
    }

    .profile-connection-disconnected {
        background-color: #fef2f2;
        color: #991b1b;
    }

    .profile-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    @media (min-width: 1024px) {
        .profile-grid {
            grid-template-columns: 2fr 1fr;
        }
    }

    .profile-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-1">Profile Settings</h1>
                    <p class="text-muted mb-0">Manage your account settings and preferences</p>
                </div>
            </div>

            <div class="profile-grid">
                <!-- Profile Form -->
                <div>
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h3 style="font-size: 1.125rem; font-weight: 500; color: #111827; margin: 0;">Personal Information</h3>
                            <p style="margin: 0.25rem 0 0 0; font-size: 0.875rem; color: #6b7280;">Update your personal details and account information</p>
                        </div>
                        <div class="profile-card-body">
                            <form method="POST" action="/profile/update">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                                <!-- Name Field -->
                                <div class="profile-form-group">
                                    <label for="name" class="profile-label">Full Name</label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        value="{{ old_input.name ?? user.name }}"
                                        class="profile-input {% if errors.name %}error{% endif %}"
                                        required
                                    >
                                    {% if errors.name %}
                                        <div class="profile-error">{{ errors.name }}</div>
                                    {% endif %}
                                </div>

                                <!-- Email Field -->
                                <div class="profile-form-group">
                                    <label for="email" class="profile-label">Email Address</label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value="{{ old_input.email ?? user.email }}"
                                        class="profile-input {% if errors.email %}error{% endif %}"
                                        required
                                    >
                                    {% if errors.email %}
                                        <div class="profile-error">{{ errors.email }}</div>
                                    {% endif %}
                                </div>

                                <!-- Password Change Section -->
                                <div style="border-top: 1px solid #f3f4f6; padding-top: 1.5rem; margin-top: 1.5rem;">
                                    <h4 style="font-size: 1rem; font-weight: 500; color: #111827; margin: 0 0 1rem 0;">Change Password</h4>
                                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0 0 1rem 0;">Leave blank to keep your current password</p>

                                    <!-- Current Password -->
                                    <div class="profile-form-group">
                                        <label for="current_password" class="profile-label">Current Password</label>
                                        <input
                                            type="password"
                                            id="current_password"
                                            name="current_password"
                                            class="profile-input {% if errors.current_password %}error{% endif %}"
                                            autocomplete="current-password"
                                        >
                                        {% if errors.current_password %}
                                            <div class="profile-error">{{ errors.current_password }}</div>
                                        {% endif %}
                                    </div>

                                    <!-- New Password -->
                                    <div class="profile-form-group">
                                        <label for="new_password" class="profile-label">New Password</label>
                                        <input
                                            type="password"
                                            id="new_password"
                                            name="new_password"
                                            class="profile-input {% if errors.new_password %}error{% endif %}"
                                            autocomplete="new-password"
                                            minlength="8"
                                        >
                                        {% if errors.new_password %}
                                            <div class="profile-error">{{ errors.new_password }}</div>
                                        {% endif %}
                                        <div style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">Must be at least 8 characters long</div>
                                    </div>

                                    <!-- Confirm Password -->
                                    <div class="profile-form-group">
                                        <label for="confirm_password" class="profile-label">Confirm New Password</label>
                                        <input
                                            type="password"
                                            id="confirm_password"
                                            name="confirm_password"
                                            class="profile-input {% if errors.confirm_password %}error{% endif %}"
                                            autocomplete="new-password"
                                        >
                                        {% if errors.confirm_password %}
                                            <div class="profile-error">{{ errors.confirm_password }}</div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div style="display: flex; justify-content: flex-end; padding-top: 1.5rem; border-top: 1px solid #f3f4f6; margin-top: 1.5rem;">
                                    <button type="submit" class="profile-btn">
                                        <i class="fas fa-save me-2"></i>
                                        Update Profile
                                    </button>
                                </div>
                    </form>
                </div>
            </div>
        </div>

                <!-- Sidebar -->
                <div class="profile-sidebar">
                    <!-- Account Stats -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h3 style="font-size: 1.125rem; font-weight: 500; color: #111827; margin: 0;">Account Statistics</h3>
                        </div>
                        <div class="profile-card-body">
                            <div class="profile-stats">
                                <span class="profile-stat-label">Total Domains</span>
                                <span class="profile-stat-value">{{ stats.total_domains }}</span>
                            </div>
                            <div class="profile-stats">
                                <span class="profile-stat-label">Total URLs</span>
                                <span class="profile-stat-value">{{ stats.total_urls }}</span>
                            </div>
                            <div class="profile-stats">
                                <span class="profile-stat-label">Indexed URLs</span>
                                <span class="profile-stat-value">{{ stats.indexed_urls }}</span>
                            </div>
                            <div class="profile-stats">
                                <span class="profile-stat-label">Member Since</span>
                                <span class="profile-stat-value">{{ stats.member_since|date('M Y') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Connected Services -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h3 style="font-size: 1.125rem; font-weight: 500; color: #111827; margin: 0;">Connected Services</h3>
                            <p style="margin: 0.25rem 0 0 0; font-size: 0.875rem; color: #6b7280;">Manage your API connections</p>
                        </div>
                        <div class="profile-card-body">
                            <!-- Google Search Console -->
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #f3f4f6;">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 2rem; height: 2rem; background-color: #fef2f2; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                        <i class="fab fa-google" style="color: #dc2626; font-size: 0.875rem;"></i>
                                    </div>
                                    <div>
                                        <p style="font-size: 0.875rem; font-weight: 500; color: #111827; margin: 0;">Google Search Console</p>
                                        <p style="font-size: 0.75rem; color: #6b7280; margin: 0;">Submit URLs to Google</p>
                                    </div>
                                </div>
                                {% if connected_providers.google ?? false %}
                                    <span class="profile-connection-status profile-connection-connected">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Connected
                                    </span>
                                {% else %}
                                    <a href="/auth/google" class="profile-btn-outline" style="font-size: 0.75rem; padding: 0.25rem 0.75rem;">
                                        Connect
                                    </a>
                                {% endif %}
                            </div>

                            <!-- Bing Webmaster Tools -->
                            <div style="display: flex; align-items: center; justify-content: space-between; padding: 1rem 0;">
                                <div style="display: flex; align-items: center;">
                                    <div style="width: 2rem; height: 2rem; background-color: #dbeafe; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                                        <i class="fab fa-microsoft" style="color: #2563eb; font-size: 0.875rem;"></i>
                                    </div>
                                    <div>
                                        <p style="font-size: 0.875rem; font-weight: 500; color: #111827; margin: 0;">Bing Webmaster Tools</p>
                                        <p style="font-size: 0.75rem; color: #6b7280; margin: 0;">Submit URLs to Bing</p>
                                    </div>
                                </div>
                                {% if connected_providers.bing ?? false %}
                                    <span class="profile-connection-status profile-connection-connected">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Connected
                                    </span>
                                {% else %}
                                    <a href="/auth/bing" class="profile-btn-outline" style="font-size: 0.75rem; padding: 0.25rem 0.75rem;">
                                        Connect
                                    </a>
                                {% endif %}
                            </div>

                            <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #f3f4f6;">
                                <a href="/dashboard/connections" class="profile-btn-outline" style="width: 100%; justify-content: center;">
                                    <i class="fas fa-cog me-2"></i>
                                    Manage Connections
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Account Actions -->
                    <div class="profile-card">
                        <div class="profile-card-header">
                            <h3 style="font-size: 1.125rem; font-weight: 500; color: #111827; margin: 0;">Account Actions</h3>
                        </div>
                        <div class="profile-card-body">
                            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                                <a href="/dashboard" class="profile-btn-outline" style="width: 100%; justify-content: center;">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Back to Dashboard
                                </a>
                                <a href="/logout" class="profile-btn-outline" style="width: 100%; justify-content: center; color: #dc2626; border-color: #fca5a5;">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Sign Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswordMatch() {
        if (newPassword.value && confirmPassword.value) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    newPassword.addEventListener('input', validatePasswordMatch);
    confirmPassword.addEventListener('input', validatePasswordMatch);
    
    // Show/hide password change section based on current password input
    const currentPassword = document.getElementById('current_password');
    const passwordSection = currentPassword.closest('.border-t');
    
    currentPassword.addEventListener('input', function() {
        if (this.value) {
            newPassword.setAttribute('required', 'required');
            confirmPassword.setAttribute('required', 'required');
        } else {
            newPassword.removeAttribute('required');
            confirmPassword.removeAttribute('required');
        }
    });
});
</script>
{% endblock %}
