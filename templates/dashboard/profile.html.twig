{% extends 'layouts/base.html.twig' %}

{% block title %}{{ title }}{% endblock %}

{% block head %}
<style>
    /* Enhanced Profile Page Styles */
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .profile-avatar {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        margin-right: 1.5rem;
        border: 3px solid rgba(255,255,255,0.3);
    }

    .profile-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .profile-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.12);
    }

    .profile-card .card-header {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        border-bottom: 2px solid #dee2e6;
        padding: 1.5rem;
    }

    .profile-form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 16px;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .profile-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    .profile-form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .profile-btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .profile-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(45deg, #5a67d8, #6b46c1);
    }

    .profile-btn-outline {
        border: 2px solid #667eea;
        color: #667eea;
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .profile-btn-outline:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
        text-decoration: none;
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.3s ease;
    }

    .stats-card:hover {
        transform: scale(1.05);
    }

    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .connection-item {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }

    .connection-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .connection-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }

    .google-icon {
        background: linear-gradient(45deg, #ea4335, #fbbc05);
    }

    .bing-icon {
        background: linear-gradient(45deg, #0078d4, #106ebe);
    }

    .badge-connected {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #667eea;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Enhanced Profile Header -->
    <div class="profile-header">
        <div class="d-flex align-items-center">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div>
                <h1 class="h2 mb-2">{{ user.name ?? 'User' }}</h1>
                <p class="mb-1 opacity-75">{{ user.email }}</p>
                <small class="opacity-75">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Member since {{ stats.member_since|date('F Y') }}
                </small>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Main Profile Form -->
        <div class="col-lg-8">
            <div class="profile-card card">
                <div class="card-header">
                    <h3 class="h5 mb-1">
                        <i class="fas fa-user-edit text-primary me-2"></i>
                        Personal Information
                    </h3>
                    <p class="text-muted mb-0 small">Update your personal details and account information</p>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/profile/update">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h6 class="section-title">
                                <i class="fas fa-info-circle me-2"></i>
                                Basic Information
                            </h6>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label fw-semibold">
                                        <i class="fas fa-user me-1"></i>
                                        Full Name
                                    </label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        value="{{ old_input.name ?? user.name }}"
                                        class="form-control profile-form-control {% if errors.name %}is-invalid{% endif %}"
                                        required
                                        placeholder="Enter your full name"
                                    >
                                    {% if errors.name %}
                                        <div class="invalid-feedback">{{ errors.name }}</div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label fw-semibold">
                                        <i class="fas fa-envelope me-1"></i>
                                        Email Address
                                    </label>
                                    <input
                                        type="email"
                                        id="email"
                                        name="email"
                                        value="{{ old_input.email ?? user.email }}"
                                        class="form-control profile-form-control {% if errors.email %}is-invalid{% endif %}"
                                        required
                                        placeholder="Enter your email address"
                                    >
                                    {% if errors.email %}
                                        <div class="invalid-feedback">{{ errors.email }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Password Change Section -->
                        <div class="form-section">
                            <h6 class="section-title">
                                <i class="fas fa-lock me-2"></i>
                                Change Password
                            </h6>
                            <p class="text-muted small mb-3">Leave blank to keep your current password</p>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="current_password" class="form-label fw-semibold">
                                        <i class="fas fa-key me-1"></i>
                                        Current Password
                                    </label>
                                    <input
                                        type="password"
                                        id="current_password"
                                        name="current_password"
                                        class="form-control profile-form-control {% if errors.current_password %}is-invalid{% endif %}"
                                        autocomplete="current-password"
                                        placeholder="Enter current password"
                                    >
                                    {% if errors.current_password %}
                                        <div class="invalid-feedback">{{ errors.current_password }}</div>
                                    {% endif %}
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="new_password" class="form-label fw-semibold">
                                        <i class="fas fa-lock me-1"></i>
                                        New Password
                                    </label>
                                    <input
                                        type="password"
                                        id="new_password"
                                        name="new_password"
                                        class="form-control profile-form-control {% if errors.new_password %}is-invalid{% endif %}"
                                        autocomplete="new-password"
                                        minlength="8"
                                        placeholder="Enter new password"
                                    >
                                    {% if errors.new_password %}
                                        <div class="invalid-feedback">{{ errors.new_password }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">Must be at least 8 characters long</small>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label for="confirm_password" class="form-label fw-semibold">
                                        <i class="fas fa-check-circle me-1"></i>
                                        Confirm Password
                                    </label>
                                    <input
                                        type="password"
                                        id="confirm_password"
                                        name="confirm_password"
                                        class="form-control profile-form-control {% if errors.confirm_password %}is-invalid{% endif %}"
                                        autocomplete="new-password"
                                        placeholder="Confirm new password"
                                    >
                                    {% if errors.confirm_password %}
                                        <div class="invalid-feedback">{{ errors.confirm_password }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-end gap-3 pt-3">
                            <a href="/dashboard" class="profile-btn-outline">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Dashboard
                            </a>
                            <button type="submit" class="btn profile-btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Enhanced Sidebar -->
        <div class="col-lg-4">
            <!-- Account Statistics -->
            <div class="row g-3 mb-4">
                <div class="col-6">
                    <div class="stats-card">
                        <div class="stats-number">{{ stats.total_domains }}</div>
                        <div class="stats-label">
                            <i class="fas fa-globe me-1"></i>
                            Domains
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card">
                        <div class="stats-number">{{ stats.total_urls }}</div>
                        <div class="stats-label">
                            <i class="fas fa-link me-1"></i>
                            URLs
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card">
                        <div class="stats-number">{{ stats.indexed_urls }}</div>
                        <div class="stats-label">
                            <i class="fas fa-check-circle me-1"></i>
                            Indexed
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card">
                        <div class="stats-number">{{ stats.total_urls > 0 ? ((stats.indexed_urls / stats.total_urls * 100)|round) : 0 }}%</div>
                        <div class="stats-label">
                            <i class="fas fa-chart-line me-1"></i>
                            Success Rate
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connected Services -->
            <div class="profile-card card mb-4">
                <div class="card-header">
                    <h5 class="mb-1">
                        <i class="fas fa-plug text-primary me-2"></i>
                        Connected Services
                    </h5>
                    <p class="text-muted mb-0 small">Manage your API connections</p>
                </div>
                <div class="card-body p-3">
                    <!-- Google Search Console -->
                    <div class="connection-item">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="connection-icon google-icon">
                                    <i class="fab fa-google text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-semibold">Google Search Console</h6>
                                    <small class="text-muted">Submit URLs to Google</small>
                                </div>
                            </div>
                            {% if connected_providers.google ?? false %}
                                <span class="badge-connected">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Connected
                                </span>
                            {% else %}
                                <a href="/auth/google" class="profile-btn-outline btn-sm">
                                    <i class="fas fa-link me-1"></i>
                                    Connect
                                </a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Bing Webmaster Tools -->
                    <div class="connection-item">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="connection-icon bing-icon">
                                    <i class="fab fa-microsoft text-white"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-semibold">Bing Webmaster Tools</h6>
                                    <small class="text-muted">Submit URLs to Bing</small>
                                </div>
                            </div>
                            {% if connected_providers.bing ?? false %}
                                <span class="badge-connected">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Connected
                                </span>
                            {% else %}
                                <a href="/auth/bing" class="profile-btn-outline btn-sm">
                                    <i class="fas fa-link me-1"></i>
                                    Connect
                                </a>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mt-3 pt-3 border-top">
                        <a href="/dashboard/connections" class="profile-btn-outline d-block text-center">
                            <i class="fas fa-cog me-2"></i>
                            Manage All Connections
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="profile-card card">
                <div class="card-header">
                    <h5 class="mb-1">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="d-grid gap-2">
                        <a href="/dashboard" class="profile-btn-outline">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Back to Dashboard
                        </a>
                        <a href="/dashboard/domains" class="profile-btn-outline">
                            <i class="fas fa-globe me-2"></i>
                            Manage Domains
                        </a>
                        <a href="/dashboard/urls" class="profile-btn-outline">
                            <i class="fas fa-link me-2"></i>
                            View URLs
                        </a>
                        <hr class="my-2">
                        <a href="/logout" class="profile-btn-outline text-danger border-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswordMatch() {
        if (newPassword.value && confirmPassword.value) {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
            } else {
                confirmPassword.setCustomValidity('');
            }
        }
    }
    
    newPassword.addEventListener('input', validatePasswordMatch);
    confirmPassword.addEventListener('input', validatePasswordMatch);
    
    // Show/hide password change section based on current password input
    const currentPassword = document.getElementById('current_password');
    const passwordSection = currentPassword.closest('.border-t');
    
    currentPassword.addEventListener('input', function() {
        if (this.value) {
            newPassword.setAttribute('required', 'required');
            confirmPassword.setAttribute('required', 'required');
        } else {
            newPassword.removeAttribute('required');
            confirmPassword.removeAttribute('required');
        }
    });

    // Enhanced form interactions
    const formControls = document.querySelectorAll('.profile-form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
        });

        control.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Form submission with loading state
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnText = submitBtn.innerHTML;

    form.addEventListener('submit', function(e) {
        validatePasswordMatch();

        if (form.checkValidity()) {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
        }
    });
});
</script>
{% endblock %}
