{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">URL Submissions</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total URLs</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-link fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Submitted</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.by_status.submitted ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.by_status.pending ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Failed</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.by_status.failed ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- URL Submission Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Submit URLs for Indexing</h6>
            </div>
            <div class="card-body">
                {% if domains and (connected_providers.google or connected_providers.bing) %}
                    <form method="POST" action="/dashboard/urls/submit">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="domain_id" class="form-label">Select Domain</label>
                                <select class="form-select" id="domain_id" name="domain_id" required>
                                    <option value="">Choose a domain...</option>
                                    {% for domain in domains %}
                                        <option value="{{ domain.id }}">{{ domain.protocol }}://{{ domain.domain }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Submit to:</label>
                                <div class="form-check-container">
                                    {% if connected_providers.google %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="providers[]" value="google" id="provider_google" checked>
                                            <label class="form-check-label" for="provider_google">
                                                <i class="fab fa-google text-danger me-1"></i>Google Search Console
                                            </label>
                                        </div>
                                    {% endif %}
                                    {% if connected_providers.bing %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="providers[]" value="bing" id="provider_bing" checked>
                                            <label class="form-check-label" for="provider_bing">
                                                <i class="fab fa-microsoft text-info me-1"></i>Bing Webmaster Tools
                                            </label>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="urls" class="form-label">URLs to Submit</label>
                            <textarea class="form-control" id="urls" name="urls" rows="5" 
                                      placeholder="Enter URLs (one per line)&#10;https://example.com/page1&#10;https://example.com/page2" 
                                      required></textarea>
                            <div class="form-text">Enter one URL per line. Maximum 10 URLs per batch.</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="document.getElementById('urls').value=''">
                                <i class="fas fa-eraser me-1"></i>Clear
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Submit URLs
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Setup Required:</strong>
                        {% if not domains %}
                            You need to <a href="/dashboard/domains">add and verify a domain</a> before submitting URLs.
                        {% elseif not connected_providers.google and not connected_providers.bing %}
                            You need to <a href="/dashboard/connections">connect at least one API account</a> (Google or Bing) to submit URLs.
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- URL Submissions History -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Submission History</h6>
                {% if pagination.total > 0 %}
                    <span class="badge bg-info">{{ pagination.total }} total</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if urls %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>URL</th>
                                    <th>Domain</th>
                                    <th>Provider</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for url in urls %}
                                    <tr>
                                        <td>
                                            <a href="{{ url.url }}" target="_blank" class="text-decoration-none" title="{{ url.url }}">
                                                {{ url.url|length > 60 ? url.url|slice(0, 60) ~ '...' : url.url }}
                                                <i class="fas fa-external-link-alt fa-xs ms-1"></i>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ url.protocol }}://{{ url.domain }}</span>
                                        </td>
                                        <td>
                                            {% if url.provider == 'google' %}
                                                <span class="badge bg-danger">
                                                    <i class="fab fa-google me-1"></i>Google
                                                </span>
                                            {% elseif url.provider == 'bing' %}
                                                <span class="badge bg-info">
                                                    <i class="fab fa-microsoft me-1"></i>Bing
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% set status_class = {
                                                'pending': 'warning',
                                                'submitted': 'info',
                                                'indexed': 'success',
                                                'failed': 'danger',
                                                'rejected': 'secondary'
                                            } %}
                                            <span class="badge bg-{{ status_class[url.status] ?? 'secondary' }}">
                                                {{ url.status|title }}
                                            </span>
                                            {% if url.error_message %}
                                                <i class="fas fa-exclamation-triangle text-warning ms-1" 
                                                   title="{{ url.error_message }}" 
                                                   data-bs-toggle="tooltip"></i>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ url.submitted_at|date('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                                        onclick="checkUrlStatus('{{ url.url }}')" 
                                                        title="Check Status">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                                {% if url.response_data %}
                                                    <button type="button" class="btn btn-outline-info btn-sm" 
                                                            onclick="showDetails('{{ url.id }}')" 
                                                            title="View Details">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pagination.total_pages > 1 %}
                        <nav aria-label="URL submissions pagination">
                            <ul class="pagination justify-content-center">
                                {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ pagination.current_page - 1 }}">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for page in range(max(1, pagination.current_page - 2), min(pagination.total_pages, pagination.current_page + 2) + 1) %}
                                    <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                        <a class="page-link" href="?page={{ page }}">{{ page }}</a>
                                    </li>
                                {% endfor %}
                                
                                {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ pagination.current_page + 1 }}">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No URL submissions yet</h5>
                        <p class="text-muted">Submit your first URLs using the form above.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.border-left-danger { border-left: 0.25rem solid #e74a3b !important; }
.text-xs { font-size: 0.7rem; }
.form-check-container .form-check { margin-bottom: 0.5rem; }
</style>

<script>
// Initialize tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Check URL status function
function checkUrlStatus(url) {
    // This would make an AJAX call to check the current status
    fetch('/api/urls/status?url=' + encodeURIComponent(url))
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
            } else {
                alert('URL Status: ' + (data.status || 'Unknown'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to check URL status');
        });
}

// Show submission details
function showDetails(submissionId) {
    // This would show a modal with detailed submission information
    alert('Details for submission ID: ' + submissionId);
}

// URL validation
document.getElementById('urls').addEventListener('input', function() {
    const urls = this.value.split('\n').filter(url => url.trim());
    const maxUrls = 10;
    
    if (urls.length > maxUrls) {
        this.setCustomValidity(`Maximum ${maxUrls} URLs allowed per batch`);
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
