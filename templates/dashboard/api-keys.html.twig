{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">API Keys & Configurations</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addApiKeyModal">
                <i class="fas fa-plus me-1"></i>Add API Key
            </button>
        </div>
    </div>
</div>

<!-- API Key Statistics -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total API Keys</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ api_configs|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-key fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Keys</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set active_count = 0 %}
                            {% for config in api_configs %}
                                {% if config.is_active %}
                                    {% set active_count = active_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ active_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Providers</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {% set providers = [] %}
                            {% for config in api_configs %}
                                {% if config.provider not in providers %}
                                    {% set providers = providers|merge([config.provider]) %}
                                {% endif %}
                            {% endfor %}
                            {{ providers|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cogs fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Keys Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Your API Configurations</h6>
    </div>
    <div class="card-body">
        {% if api_configs is empty %}
            <div class="text-center py-5">
                <i class="fas fa-key fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No API Keys Configured</h5>
                <p class="text-gray-500 mb-4">Add your first API key to start using the indexing services.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addApiKeyModal">
                    <i class="fas fa-plus me-1"></i>Add API Key
                </button>
            </div>
        {% else %}
            <div class="table-responsive">
                <table class="table table-bordered" id="apiKeysTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Provider</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Last Used</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for config in api_configs %}
                        <tr>
                            <td>
                                {% if config.provider == 'google' %}
                                    <span class="badge bg-danger">
                                        <i class="fab fa-google me-1"></i>Google
                                    </span>
                                {% elseif config.provider == 'bing' %}
                                    <span class="badge bg-info">
                                        <i class="fab fa-microsoft me-1"></i>Bing
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ config.provider|title }}</span>
                                {% endif %}
                            </td>
                            <td>{{ config.name ?: 'Unnamed Configuration' }}</td>
                            <td>
                                {% if config.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>{{ config.created_at|date('M d, Y') }}</td>
                            <td>
                                {% if config.last_used_at %}
                                    {{ config.last_used_at|date('M d, Y H:i') }}
                                {% else %}
                                    <span class="text-muted">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            onclick="editApiKey({{ config.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" 
                                            onclick="testApiKey({{ config.id }})">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteApiKey({{ config.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% endif %}
    </div>
</div>

<!-- Add API Key Modal -->
<div class="modal fade" id="addApiKeyModal" tabindex="-1" aria-labelledby="addApiKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addApiKeyModalLabel">Add API Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/dashboard/api-keys/save">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    
                    <div class="mb-3">
                        <label for="provider" class="form-label">Provider</label>
                        <select class="form-select" id="provider" name="provider" required>
                            <option value="">Select Provider</option>
                            <option value="google">Google Search Console</option>
                            <option value="bing">Bing Webmaster Tools</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Configuration Name</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               placeholder="e.g., Production Google API" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key</label>
                        <input type="password" class="form-control" id="api_key" name="api_key" 
                               placeholder="Enter your API key" required>
                        <div class="form-text">Your API key will be encrypted and stored securely.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="endpoint" class="form-label">API Endpoint (Optional)</label>
                        <input type="url" class="form-control" id="endpoint" name="endpoint" 
                               placeholder="https://api.example.com">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Active
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Configuration</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editApiKey(id) {
    // TODO: Implement edit functionality
    alert('Edit functionality will be implemented soon.');
}

function testApiKey(id) {
    // TODO: Implement test functionality
    alert('Test functionality will be implemented soon.');
}

function deleteApiKey(id) {
    if (confirm('Are you sure you want to delete this API configuration?')) {
        // TODO: Implement delete functionality
        alert('Delete functionality will be implemented soon.');
    }
}

// Initialize DataTable if there are API keys
{% if api_configs is not empty %}
$(document).ready(function() {
    $('#apiKeysTable').DataTable({
        "pageLength": 25,
        "order": [[ 3, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 5 }
        ]
    });
});
{% endif %}
</script>
{% endblock %}
