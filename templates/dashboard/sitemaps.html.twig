{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Sitemap Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Sitemaps</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.completed ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Processing</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.processing ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cog fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Failed</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.failed ?? 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sitemap Submission Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Submit Sitemap for Indexing</h6>
            </div>
            <div class="card-body">
                {% if domains and (connected_providers.google or connected_providers.bing) %}
                    <form method="POST" action="/dashboard/sitemaps/submit">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="domain_id" class="form-label">Select Domain</label>
                                <select class="form-select" id="domain_id" name="domain_id" required>
                                    <option value="">Choose a domain...</option>
                                    {% for domain in domains %}
                                        <option value="{{ domain.id }}">{{ domain.protocol }}://{{ domain.domain }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Submit to:</label>
                                <div class="form-check-container">
                                    {% if connected_providers.google %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="providers[]" value="google" id="provider_google" checked>
                                            <label class="form-check-label" for="provider_google">
                                                <i class="fab fa-google text-danger me-1"></i>Google Search Console
                                            </label>
                                        </div>
                                    {% endif %}
                                    {% if connected_providers.bing %}
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="providers[]" value="bing" id="provider_bing" checked>
                                            <label class="form-check-label" for="provider_bing">
                                                <i class="fab fa-microsoft text-info me-1"></i>Bing Webmaster Tools
                                            </label>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sitemap_url" class="form-label">Sitemap URL</label>
                            <input type="url" class="form-control" id="sitemap_url" name="sitemap_url" 
                                   placeholder="https://example.com/sitemap.xml" required>
                            <div class="form-text">Enter the full URL to your XML sitemap file.</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="process_urls" name="process_urls" value="1">
                                <label class="form-check-label" for="process_urls">
                                    Also submit individual URLs from this sitemap for indexing
                                </label>
                            </div>
                            <div class="form-text">This will parse the sitemap and submit each URL individually (may take longer).</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="validateSitemap()">
                                <i class="fas fa-check me-1"></i>Validate Sitemap
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Submit Sitemap
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Setup Required:</strong>
                        {% if not domains %}
                            You need to <a href="/dashboard/domains">add and verify a domain</a> before submitting sitemaps.
                        {% elseif not connected_providers.google and not connected_providers.bing %}
                            You need to <a href="/dashboard/connections">connect at least one API account</a> (Google or Bing) to submit sitemaps.
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Sitemaps History -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Sitemap Submissions</h6>
                {% if pagination.total > 0 %}
                    <span class="badge bg-info">{{ pagination.total }} total</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if sitemaps %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Sitemap URL</th>
                                    <th>Domain</th>
                                    <th>Status</th>
                                    <th>URLs</th>
                                    <th>Providers</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sitemap in sitemaps %}
                                    <tr>
                                        <td>
                                            <a href="{{ sitemap.sitemap_url }}" target="_blank" class="text-decoration-none" title="{{ sitemap.sitemap_url }}">
                                                {{ sitemap.sitemap_url|length > 50 ? sitemap.sitemap_url|slice(0, 50) ~ '...' : sitemap.sitemap_url }}
                                                <i class="fas fa-external-link-alt fa-xs ms-1"></i>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ sitemap.protocol }}://{{ sitemap.domain }}</span>
                                        </td>
                                        <td>
                                            {% set status_class = {
                                                'pending': 'warning',
                                                'processing': 'info',
                                                'completed': 'success',
                                                'failed': 'danger'
                                            } %}
                                            <span class="badge bg-{{ status_class[sitemap.status] ?? 'secondary' }}">
                                                {{ sitemap.status|title }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if sitemap.total_urls > 0 %}
                                                <div class="small">
                                                    <div>Total: {{ sitemap.total_urls }}</div>
                                                    {% if sitemap.processed_urls > 0 %}
                                                        <div class="text-success">Success: {{ sitemap.successful_urls }}</div>
                                                        {% if sitemap.failed_urls > 0 %}
                                                            <div class="text-danger">Failed: {{ sitemap.failed_urls }}</div>
                                                        {% endif %}
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                {% if sitemap.google_submitted %}
                                                    <span class="badge bg-danger" title="Google Submitted">
                                                        <i class="fab fa-google"></i>
                                                    </span>
                                                {% endif %}
                                                {% if sitemap.bing_submitted %}
                                                    <span class="badge bg-info" title="Bing Submitted">
                                                        <i class="fab fa-microsoft"></i>
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ sitemap.created_at|date('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                                        onclick="processSitemapUrls({{ sitemap.id }})" 
                                                        title="Process URLs">
                                                    <i class="fas fa-cog"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" 
                                                        onclick="showSitemapDetails({{ sitemap.id }})"
                                                        title="View Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pagination.total_pages > 1 %}
                        <nav aria-label="Sitemaps pagination">
                            <ul class="pagination justify-content-center">
                                {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ pagination.current_page - 1 }}">Previous</a>
                                    </li>
                                {% endif %}
                                
                                {% for page in range(max(1, pagination.current_page - 2), min(pagination.total_pages, pagination.current_page + 2) + 1) %}
                                    <li class="page-item {{ page == pagination.current_page ? 'active' : '' }}">
                                        <a class="page-link" href="?page={{ page }}">{{ page }}</a>
                                    </li>
                                {% endfor %}
                                
                                {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ pagination.current_page + 1 }}">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No sitemaps submitted yet</h5>
                        <p class="text-muted">Submit your first sitemap using the form above.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.border-left-danger { border-left: 0.25rem solid #e74a3b !important; }
.text-xs { font-size: 0.7rem; }
.form-check-container .form-check { margin-bottom: 0.5rem; }
</style>

<script>
function validateSitemap() {
    const sitemapUrl = document.getElementById('sitemap_url').value;
    
    if (!sitemapUrl) {
        alert('Please enter a sitemap URL first');
        return;
    }
    
    // Basic validation
    if (!sitemapUrl.endsWith('.xml')) {
        alert('Sitemap URL should end with .xml');
        return;
    }
    
    // Try to fetch the sitemap to validate it
    fetch(sitemapUrl, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                alert('Sitemap URL appears to be valid!');
            } else {
                alert('Sitemap URL returned HTTP ' + response.status);
            }
        })
        .catch(error => {
            alert('Could not access sitemap URL. Please check the URL and try again.');
        });
}

function processSitemapUrls(sitemapId) {
    if (confirm('This will process all URLs from the sitemap and submit them for indexing. Continue?')) {
        // This would make an AJAX call to process sitemap URLs
        fetch(`/api/sitemaps/${sitemapId}/process`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Sitemap processing started. Check back later for results.');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to process sitemap');
        });
    }
}

function showSitemapDetails(sitemapId) {
    // This would show detailed sitemap information
    alert('Sitemap details for ID: ' + sitemapId);
}
</script>
{% endblock %}
