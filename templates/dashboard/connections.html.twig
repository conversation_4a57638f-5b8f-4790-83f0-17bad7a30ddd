{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">API Connections</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Google Search Console Connection -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fab fa-google text-danger me-2"></i>Google Search Console
                </h5>
                {% if connections.google.connected %}
                    <span class="badge bg-success">Connected</span>
                {% else %}
                    <span class="badge bg-secondary">Not Connected</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if connections.google.connected %}
                    <div class="mb-3">
                        <h6>Account Information</h6>
                        {% if connections.google.user_info %}
                            <p class="mb-1"><strong>Name:</strong> {{ connections.google.user_info.name ?? 'N/A' }}</p>
                            <p class="mb-1"><strong>Email:</strong> {{ connections.google.user_info.email ?? 'N/A' }}</p>
                        {% endif %}
                    </div>
                    
                    {% if connections.google.token_info %}
                        <div class="mb-3">
                            <h6>Connection Details</h6>
                            <p class="mb-1"><strong>Connected:</strong> {{ connections.google.token_info.created_at|date('M d, Y H:i') }}</p>
                            <p class="mb-1"><strong>Last Updated:</strong> {{ connections.google.token_info.updated_at|date('M d, Y H:i') }}</p>
                            {% if connections.google.token_info.expires_at %}
                                <p class="mb-1"><strong>Expires:</strong> {{ connections.google.token_info.expires_at|date('M d, Y H:i') }}</p>
                            {% endif %}
                        </div>
                    {% endif %}
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Your Google Search Console account is connected and ready to use.
                    </div>
                    
                    <form method="POST" action="/auth/google/disconnect" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <button type="submit" class="btn btn-outline-danger" 
                                onclick="return confirm('Are you sure you want to disconnect your Google account?')">
                            <i class="fas fa-unlink me-2"></i>Disconnect
                        </button>
                    </form>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Connect your Google Search Console account to submit URLs for indexing and monitor their status.
                    </div>
                    
                    <div class="mb-3">
                        <h6>Features Available:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Submit URLs for indexing</li>
                            <li><i class="fas fa-check text-success me-2"></i>Check URL indexing status</li>
                            <li><i class="fas fa-check text-success me-2"></i>Access Search Console data</li>
                            <li><i class="fas fa-check text-success me-2"></i>Manage sitemaps</li>
                        </ul>
                    </div>
                    
                    <a href="/auth/google" class="btn btn-danger">
                        <i class="fab fa-google me-2"></i>Connect Google Account
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Bing Webmaster Connection -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fab fa-microsoft text-info me-2"></i>Bing Webmaster Tools
                </h5>
                {% if connections.bing.connected %}
                    <span class="badge bg-success">Connected</span>
                {% else %}
                    <span class="badge bg-secondary">Not Connected</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if connections.bing.connected %}
                    <div class="mb-3">
                        <h6>Account Information</h6>
                        {% if connections.bing.user_info %}
                            <p class="mb-1"><strong>Name:</strong> {{ connections.bing.user_info.displayName ?? 'N/A' }}</p>
                            <p class="mb-1"><strong>Email:</strong> {{ connections.bing.user_info.mail ?? connections.bing.user_info.userPrincipalName ?? 'N/A' }}</p>
                        {% endif %}
                    </div>
                    
                    {% if connections.bing.token_info %}
                        <div class="mb-3">
                            <h6>Connection Details</h6>
                            <p class="mb-1"><strong>Connected:</strong> {{ connections.bing.token_info.created_at|date('M d, Y H:i') }}</p>
                            <p class="mb-1"><strong>Last Updated:</strong> {{ connections.bing.token_info.updated_at|date('M d, Y H:i') }}</p>
                            {% if connections.bing.token_info.expires_at %}
                                <p class="mb-1"><strong>Expires:</strong> {{ connections.bing.token_info.expires_at|date('M d, Y H:i') }}</p>
                            {% endif %}
                        </div>
                    {% endif %}
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Your Bing Webmaster Tools account is connected and ready to use.
                    </div>
                    
                    <form method="POST" action="/auth/bing/disconnect" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <button type="submit" class="btn btn-outline-danger" 
                                onclick="return confirm('Are you sure you want to disconnect your Bing account?')">
                            <i class="fas fa-unlink me-2"></i>Disconnect
                        </button>
                    </form>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Connect your Bing Webmaster Tools account to submit URLs for indexing and access Bing search data.
                    </div>
                    
                    <div class="mb-3">
                        <h6>Features Available:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Submit URLs for indexing</li>
                            <li><i class="fas fa-check text-success me-2"></i>Batch URL submissions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Access Bing Webmaster data</li>
                            <li><i class="fas fa-check text-success me-2"></i>Monitor crawl status</li>
                        </ul>
                    </div>
                    
                    <a href="/auth/bing" class="btn btn-info">
                        <i class="fab fa-microsoft me-2"></i>Connect Bing Account
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Connection Status Summary -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Connection Status Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Google Search Console</h6>
                        {% if connections.google.connected %}
                            <div class="d-flex align-items-center text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>Connected and active</span>
                            </div>
                        {% else %}
                            <div class="d-flex align-items-center text-muted">
                                <i class="fas fa-times-circle me-2"></i>
                                <span>Not connected</span>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6>Bing Webmaster Tools</h6>
                        {% if connections.bing.connected %}
                            <div class="d-flex align-items-center text-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>Connected and active</span>
                            </div>
                        {% else %}
                            <div class="d-flex align-items-center text-muted">
                                <i class="fas fa-times-circle me-2"></i>
                                <span>Not connected</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                {% if not connections.google.connected and not connections.bing.connected %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>No connections found.</strong> You need to connect at least one account to start submitting URLs for indexing.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh connection status every 30 seconds
setInterval(function() {
    // Only refresh if user is still on the page
    if (document.visibilityState === 'visible') {
        fetch('/api/connection-status')
            .then(response => response.json())
            .then(data => {
                // Update connection status badges
                // This would be implemented with more detailed AJAX updates
            })
            .catch(error => console.log('Connection status check failed'));
    }
}, 30000);
</script>
{% endblock %}
