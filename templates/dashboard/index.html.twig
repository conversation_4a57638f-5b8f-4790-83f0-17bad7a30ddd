{% extends "layouts/dashboard.html.twig" %}

{% block content %}
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p class="mt-1 text-sm text-gray-500">
                Welcome back! Here's what's happening with your SEO indexing.
            </p>
        </div>
        <div class="flex space-x-3">
            <button type="button" class="btn-outline">
                <i class="fas fa-share-alt mr-2"></i>
                Share
            </button>
            <button type="button" class="btn-outline">
                <i class="fas fa-download mr-2"></i>
                Export
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Total Domains
                        </p>
                        <p class="text-3xl font-bold text-gray-900">{{ stats.total_domains }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-globe text-primary-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Total URLs
                        </p>
                        <p class="text-3xl font-bold text-gray-900">{{ stats.total_urls }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-link text-success-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Indexed URLs
                        </p>
                        <p class="text-3xl font-bold text-gray-900">{{ stats.indexed_urls }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-primary-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                            Pending URLs
                        </p>
                        <p class="text-3xl font-bold text-gray-900">{{ stats.pending_urls }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-warning-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- Connection Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">API Connection Status</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fab fa-google fa-2x text-danger me-3"></i>
                            <div>
                                <h6 class="mb-1">Google Search Console</h6>
                                {% if connected_providers.google ?? false %}
                                    <span class="badge bg-success">Connected</span>
                                {% else %}
                                    <span class="badge bg-secondary">Not Connected</span>
                                    <a href="/auth/google" class="btn btn-sm btn-outline-danger ms-2">Connect</a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fab fa-microsoft fa-2x text-info me-3"></i>
                            <div>
                                <h6 class="mb-1">Bing Webmaster Tools</h6>
                                {% if connected_providers.bing ?? false %}
                                    <span class="badge bg-success">Connected</span>
                                {% else %}
                                    <span class="badge bg-secondary">Not Connected</span>
                                    <a href="/auth/bing" class="btn btn-sm btn-outline-info ms-2">Connect</a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% if not (connected_providers.google ?? false) and not (connected_providers.bing ?? false) %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>No API connections found.</strong>
                        <a href="/dashboard/connections">Connect your accounts</a> to start submitting URLs for indexing.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/connections" class="btn btn-primary btn-block">
                            <i class="fas fa-plug me-2"></i>Manage Connections
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/domains" class="btn btn-success btn-block">
                            <i class="fas fa-plus me-2"></i>Add Domain
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/urls" class="btn btn-info btn-block">
                            <i class="fas fa-upload me-2"></i>Submit URLs
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/dashboard/reports" class="btn btn-warning btn-block">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent URL Submissions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Recent URL Submissions</h6>
                <a href="/dashboard/urls" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_urls %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>URL</th>
                                    <th>Domain</th>
                                    <th>Provider</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for url in recent_urls %}
                                    <tr>
                                        <td>
                                            <a href="{{ url.url }}" target="_blank" class="text-decoration-none">
                                                {{ url.url|length > 50 ? url.url|slice(0, 50) ~ '...' : url.url }}
                                            </a>
                                        </td>
                                        <td>{{ url.domain }}</td>
                                        <td>
                                            <span class="badge bg-{{ url.provider == 'google' ? 'danger' : 'info' }}">
                                                {{ url.provider|title }}
                                            </span>
                                        </td>
                                        <td>
                                            {% set status_class = {
                                                'pending': 'warning',
                                                'submitted': 'info',
                                                'indexed': 'success',
                                                'failed': 'danger',
                                                'rejected': 'secondary'
                                            } %}
                                            <span class="badge bg-{{ status_class[url.status] ?? 'secondary' }}">
                                                {{ url.status|title }}
                                            </span>
                                        </td>
                                        <td>{{ url.created_at|date('M d, Y H:i') }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No URL submissions yet</h5>
                        <p class="text-muted">Start by adding a domain and submitting your first URL.</p>
                        <a href="/dashboard/domains" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Domain
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.btn-block {
    width: 100%;
}
</style>
{% endblock %}
