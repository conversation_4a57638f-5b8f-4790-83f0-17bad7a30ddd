{% if flash_messages %}
    <div class="fixed top-4 right-4 z-50 space-y-2 max-w-sm" x-data="{ messages: [] }" x-init="
        {% for type, messages in flash_messages %}
            {% for message in messages %}
                messages.push({
                    id: Date.now() + Math.random(),
                    type: '{{ type }}',
                    message: '{{ message|e('js') }}',
                    show: true
                });
            {% endfor %}
        {% endfor %}
        
        // Auto-hide messages after 5 seconds
        messages.forEach((msg, index) => {
            setTimeout(() => {
                msg.show = false;
                setTimeout(() => {
                    messages.splice(index, 1);
                }, 300);
            }, 5000);
        });
    ">
        <template x-for="msg in messages" :key="msg.id">
            <div x-show="msg.show" 
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform translate-x-full"
                 x-transition:enter-end="opacity-100 transform translate-x-0"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100 transform translate-x-0"
                 x-transition:leave-end="opacity-0 transform translate-x-full"
                 class="rounded-lg p-4 shadow-lg border"
                 :class="{
                     'alert-success': msg.type === 'success',
                     'alert-danger': msg.type === 'error',
                     'alert-warning': msg.type === 'warning',
                     'alert-info': msg.type === 'info'
                 }">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i :class="{
                            'fas fa-check-circle text-success-400': msg.type === 'success',
                            'fas fa-exclamation-circle text-danger-400': msg.type === 'error',
                            'fas fa-exclamation-triangle text-warning-400': msg.type === 'warning',
                            'fas fa-info-circle text-primary-400': msg.type === 'info'
                        }" class="h-5 w-5"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium" x-text="msg.message"></p>
                    </div>
                    <div class="ml-4 flex-shrink-0">
                        <button @click="msg.show = false" class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
                                :class="{
                                    'text-success-500 hover:bg-success-100 focus:ring-success-600': msg.type === 'success',
                                    'text-danger-500 hover:bg-danger-100 focus:ring-danger-600': msg.type === 'error',
                                    'text-warning-500 hover:bg-warning-100 focus:ring-warning-600': msg.type === 'warning',
                                    'text-primary-500 hover:bg-primary-100 focus:ring-primary-600': msg.type === 'info'
                                }">
                            <span class="sr-only">Dismiss</span>
                            <i class="fas fa-times h-4 w-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>
{% endif %}
