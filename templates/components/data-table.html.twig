{#
    Reusable Data Table Component
    
    Usage:
    {% include 'components/data-table.html.twig' with {
        'title': 'Domain List',
        'description': 'Manage your domains and verification status',
        'headers': [
            {'key': 'domain', 'label': 'Domain', 'sortable': true},
            {'key': 'status', 'label': 'Status', 'sortable': false},
            {'key': 'created', 'label': 'Created', 'sortable': true},
            {'key': 'actions', 'label': 'Actions', 'sortable': false}
        ],
        'data': domains,
        'actions': true,
        'searchable': true,
        'pagination': true
    } %}
#}

<div class="card">
    {% if title or description %}
        <div class="card-header">
            {% if title %}
                <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
            {% endif %}
            {% if description %}
                <p class="mt-1 text-sm text-gray-500">{{ description }}</p>
            {% endif %}
        </div>
    {% endif %}
    
    <div class="card-body p-0">
        <!-- Search and Filters -->
        {% if searchable %}
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                    <div class="flex-1 max-w-lg">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" 
                                   id="table-search"
                                   class="form-input pl-10" 
                                   placeholder="Search {{ title|lower ?? 'items' }}..."
                                   onkeyup="filterTable()">
                        </div>
                    </div>
                    
                    {% if filters is defined %}
                        <div class="flex space-x-3">
                            {% for filter in filters %}
                                <select class="form-select" onchange="filterTable()">
                                    <option value="">{{ filter.label }}</option>
                                    {% for option in filter.options %}
                                        <option value="{{ option.value }}">{{ option.label }}</option>
                                    {% endfor %}
                                </select>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
        
        <!-- Table -->
        <div class="overflow-x-auto">
            <table class="table" id="data-table">
                <thead class="table-header">
                    <tr>
                        {% for header in headers %}
                            <th class="table-cell {{ header.sortable ? 'cursor-pointer hover:bg-gray-100' : '' }}"
                                {% if header.sortable %}onclick="sortTable('{{ header.key }}')"{% endif %}>
                                <div class="flex items-center space-x-1">
                                    <span>{{ header.label }}</span>
                                    {% if header.sortable %}
                                        <i class="fas fa-sort text-gray-400 text-xs"></i>
                                    {% endif %}
                                </div>
                            </th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody class="table-body">
                    {% if data is empty %}
                        <tr>
                            <td colspan="{{ headers|length }}" class="table-cell text-center py-12">
                                <div class="text-gray-500">
                                    <i class="fas fa-inbox text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">No {{ title|lower ?? 'items' }} found</p>
                                    <p class="text-sm">Get started by adding your first item.</p>
                                </div>
                            </td>
                        </tr>
                    {% else %}
                        {% for item in data %}
                            <tr class="hover:bg-gray-50 transition-colors">
                                {% for header in headers %}
                                    <td class="table-cell">
                                        {% if header.key == 'actions' %}
                                            <div class="flex items-center space-x-2">
                                                {% block table_actions %}
                                                    <button class="btn-sm btn-outline text-xs">
                                                        <i class="fas fa-edit mr-1"></i>
                                                        Edit
                                                    </button>
                                                    <button class="btn-sm btn-danger text-xs">
                                                        <i class="fas fa-trash mr-1"></i>
                                                        Delete
                                                    </button>
                                                {% endblock %}
                                            </div>
                                        {% elseif header.key == 'status' %}
                                            {% if item.verified is defined %}
                                                {% if item.verified %}
                                                    <span class="badge-success">
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                        Verified
                                                    </span>
                                                {% else %}
                                                    <span class="badge-warning">
                                                        <i class="fas fa-clock mr-1"></i>
                                                        Pending
                                                    </span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge-primary">{{ item[header.key] }}</span>
                                            {% endif %}
                                        {% elseif header.key == 'protocol' %}
                                            <span class="badge-{{ item.protocol == 'https' ? 'success' : 'warning' }}">
                                                {{ item.protocol|upper }}
                                            </span>
                                        {% elseif header.key == 'domain' %}
                                            <div class="flex items-center">
                                                {% if item.verified %}
                                                    <i class="fas fa-check-circle text-success-500 mr-2"></i>
                                                {% else %}
                                                    <i class="fas fa-exclamation-triangle text-warning-500 mr-2"></i>
                                                {% endif %}
                                                <span class="font-medium">{{ item.domain }}</span>
                                            </div>
                                        {% elseif header.key == 'connections' %}
                                            <div class="flex items-center space-x-1">
                                                {% if item.google_connected %}
                                                    <span class="badge-danger" title="Google Connected">
                                                        <i class="fab fa-google"></i>
                                                    </span>
                                                {% endif %}
                                                {% if item.bing_connected %}
                                                    <span class="badge-primary" title="Bing Connected">
                                                        <i class="fab fa-microsoft"></i>
                                                    </span>
                                                {% endif %}
                                                {% if not item.google_connected and not item.bing_connected %}
                                                    <span class="text-gray-500 text-sm">None</span>
                                                {% endif %}
                                            </div>
                                        {% elseif header.key ends with '_date' or header.key == 'created' or header.key == 'updated' or header.key == 'created_at' %}
                                            <span class="text-sm text-gray-500">
                                                {{ item[header.key]|date('M j, Y g:i A') }}
                                            </span>
                                        {% else %}
                                            {{ item[header.key] }}
                                        {% endif %}
                                    </td>
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if pagination and data is not empty %}
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">{{ data|length }}</span> of <span class="font-medium">{{ data|length }}</span> results
                    </div>
                    <div class="flex space-x-2">
                        <button class="btn-outline btn-sm" disabled>
                            <i class="fas fa-chevron-left mr-1"></i>
                            Previous
                        </button>
                        <button class="btn-outline btn-sm" disabled>
                            Next
                            <i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
function filterTable() {
    const searchInput = document.getElementById('table-search');
    const table = document.getElementById('data-table');
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    
    if (!searchInput) return;
    
    const searchTerm = searchInput.value.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

function sortTable(column) {
    // Basic sorting implementation
    const table = document.getElementById('data-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Toggle sort direction
    const currentSort = table.dataset.sortColumn;
    const currentDirection = table.dataset.sortDirection || 'asc';
    const newDirection = (currentSort === column && currentDirection === 'asc') ? 'desc' : 'asc';
    
    table.dataset.sortColumn = column;
    table.dataset.sortDirection = newDirection;
    
    // Update sort icons
    const headers = table.querySelectorAll('th i.fa-sort, th i.fa-sort-up, th i.fa-sort-down');
    headers.forEach(icon => {
        icon.className = 'fas fa-sort text-gray-400 text-xs';
    });
    
    const activeHeader = table.querySelector(`th[onclick="sortTable('${column}')"] i`);
    if (activeHeader) {
        activeHeader.className = `fas fa-sort-${newDirection === 'asc' ? 'up' : 'down'} text-primary-600 text-xs`;
    }
    
    // Sort rows (basic implementation)
    rows.sort((a, b) => {
        const aText = a.textContent.trim();
        const bText = b.textContent.trim();
        
        if (newDirection === 'asc') {
            return aText.localeCompare(bText);
        } else {
            return bText.localeCompare(aText);
        }
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}
</script>
