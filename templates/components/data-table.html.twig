{#
    Data Table Component
    
    Usage:
    {% embed 'components/data-table.html.twig' with {
        'title': 'Table Title',
        'description': 'Table description',
        'headers': [
            {'key': 'name', 'label': 'Name', 'sortable': true},
            {'key': 'email', 'label': 'Email', 'sortable': false}
        ],
        'data': items,
        'searchable': true,
        'pagination': true
    } %}
        {% block table_actions %}
            <button class="btn-primary">Add Item</button>
        {% endblock %}
    {% endembed %}
#}

<div class="card">
    <div class="card-header">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
                {% if description %}
                    <p class="mt-1 text-sm text-gray-500">{{ description }}</p>
                {% endif %}
            </div>
            <div class="flex items-center space-x-3">
                {% if searchable %}
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" 
                               id="table-search" 
                               class="form-input pl-10 pr-4 py-2 w-64" 
                               placeholder="Search..."
                               onkeyup="filterTable()">
                    </div>
                {% endif %}
                {% block table_actions %}{% endblock %}
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        {% if data and data|length > 0 %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="data-table">
                    <thead class="bg-gray-50">
                        <tr>
                            {% for header in headers %}
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider {{ header.sortable ? 'cursor-pointer hover:bg-gray-100' : '' }}" 
                                    {% if header.sortable %}onclick="sortTable('{{ header.key }}')"{% endif %}>
                                    <div class="flex items-center space-x-1">
                                        <span>{{ header.label }}</span>
                                        {% if header.sortable %}
                                            <i class="fas fa-sort text-gray-400 sort-icon" data-column="{{ header.key }}"></i>
                                        {% endif %}
                                    </div>
                                </th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in data %}
                            <tr class="hover:bg-gray-50 table-row">
                                {% for header in headers %}
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        {% if header.key == 'status' %}
                                            {% set status_class = {
                                                'active': 'success',
                                                'verified': 'success',
                                                'pending': 'warning',
                                                'inactive': 'secondary',
                                                'failed': 'danger',
                                                'rejected': 'danger'
                                            } %}
                                            <span class="badge-{{ status_class[item.status] ?? 'secondary' }}">
                                                {{ item.status|title }}
                                            </span>
                                        {% elseif header.key == 'url' %}
                                            <a href="{{ item.url }}" target="_blank" class="text-primary-600 hover:text-primary-800 font-medium">
                                                {{ item.url|length > 50 ? item.url|slice(0, 50) ~ '...' : item.url }}
                                                <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                            </a>
                                        {% elseif header.key == 'provider' %}
                                            <span class="badge-{{ item.provider == 'google' ? 'danger' : 'primary' }}">
                                                <i class="fab fa-{{ item.provider == 'google' ? 'google' : 'microsoft' }} mr-1"></i>
                                                {{ item.provider|title }}
                                            </span>
                                        {% elseif header.key == 'domain' %}
                                            <div class="flex items-center">
                                                {% if item.verified %}
                                                    <i class="fas fa-check-circle text-success-500 mr-2"></i>
                                                {% else %}
                                                    <i class="fas fa-exclamation-triangle text-warning-500 mr-2"></i>
                                                {% endif %}
                                                <span class="font-medium text-gray-900">{{ item.domain }}</span>
                                            </div>
                                        {% elseif header.key == 'protocol' %}
                                            <span class="badge-{{ item.protocol == 'https' ? 'success' : 'secondary' }}">
                                                <i class="fas fa-{{ item.protocol == 'https' ? 'lock' : 'unlock' }} mr-1"></i>
                                                {{ item.protocol|upper }}
                                            </span>
                                        {% elseif header.key == 'connections' %}
                                            <div class="flex space-x-1">
                                                {% if item.google_connected %}
                                                    <span class="badge-danger text-xs">
                                                        <i class="fab fa-google mr-1"></i>
                                                        Google
                                                    </span>
                                                {% endif %}
                                                {% if item.bing_connected %}
                                                    <span class="badge-primary text-xs">
                                                        <i class="fab fa-microsoft mr-1"></i>
                                                        Bing
                                                    </span>
                                                {% endif %}
                                                {% if not item.google_connected and not item.bing_connected %}
                                                    <span class="text-gray-400 text-sm">None</span>
                                                {% endif %}
                                            </div>
                                        {% elseif header.key == 'actions' %}
                                            <div class="flex items-center space-x-2">
                                                {% if not item.verified %}
                                                    <button onclick="verifyDomain({{ item.id }})" class="btn-sm btn-success" title="Verify Domain">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                {% endif %}
                                                <button onclick="editDomain({{ item.id }})" class="btn-sm btn-outline" title="Edit Domain">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button onclick="deleteDomain({{ item.id }})" class="btn-sm btn-danger" title="Delete Domain">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        {% elseif header.key ends with '_date' or header.key == 'created' or header.key == 'updated' or header.key == 'created_at' %}
                                            <span class="text-gray-900">{{ attribute(item, header.key)|date('M d, Y') }}</span>
                                        {% else %}
                                            <span class="text-gray-900">{{ attribute(item, header.key) }}</span>
                                        {% endif %}
                                    </td>
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if pagination %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </button>
                        <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">1</span> to <span class="font-medium">{{ data|length }}</span> of <span class="font-medium">{{ data|length }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left h-5 w-5"></i>
                                </button>
                                <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    1
                                </button>
                                <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right h-5 w-5"></i>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-inbox text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No data available</h3>
                <p class="text-gray-500">There are no items to display at the moment.</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
function filterTable() {
    const input = document.getElementById('table-search');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('data-table');
    const rows = table.getElementsByClassName('table-row');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const text = row.textContent || row.innerText;
        if (text.toLowerCase().indexOf(filter) > -1) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

let sortDirection = {};

function sortTable(column) {
    const table = document.getElementById('data-table');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByClassName('table-row'));
    
    // Toggle sort direction
    sortDirection[column] = sortDirection[column] === 'asc' ? 'desc' : 'asc';
    
    // Update sort icons
    document.querySelectorAll('.sort-icon').forEach(icon => {
        icon.className = 'fas fa-sort text-gray-400 sort-icon';
    });
    
    const currentIcon = document.querySelector(`[data-column="${column}"]`);
    if (currentIcon) {
        currentIcon.className = `fas fa-sort-${sortDirection[column] === 'asc' ? 'up' : 'down'} text-gray-600 sort-icon`;
    }
    
    // Sort rows
    rows.sort((a, b) => {
        const aText = a.cells[getColumnIndex(column)].textContent.trim();
        const bText = b.cells[getColumnIndex(column)].textContent.trim();
        
        if (sortDirection[column] === 'asc') {
            return aText.localeCompare(bText);
        } else {
            return bText.localeCompare(aText);
        }
    });
    
    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}

function getColumnIndex(column) {
    const headers = document.querySelectorAll('#data-table thead th');
    for (let i = 0; i < headers.length; i++) {
        if (headers[i].querySelector('.sort-icon')?.dataset.column === column) {
            return i;
        }
    }
    return 0;
}
</script>
