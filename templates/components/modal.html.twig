{#
    Reusable Modal Component
    
    Usage:
    {% include 'components/modal.html.twig' with {
        'id': 'addDomainModal',
        'title': 'Add New Domain',
        'size': 'md', // sm, md, lg, xl
        'closable': true,
        'footer': true
    } %}
#}

{% set modal_id = id ?? 'modal' %}
{% set modal_size = size ?? 'md' %}
{% set modal_closable = closable ?? true %}
{% set modal_footer = footer ?? false %}

{% set size_classes = {
    'sm': 'max-w-md',
    'md': 'max-w-lg',
    'lg': 'max-w-2xl',
    'xl': 'max-w-4xl',
    'full': 'max-w-full mx-4'
} %}

<!-- Modal Overlay -->
<div id="{{ modal_id }}" 
     class="fixed inset-0 z-50 overflow-y-auto hidden" 
     aria-labelledby="{{ modal_id }}-title" 
     role="dialog" 
     aria-modal="true">
    
    <!-- Background overlay -->
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
             aria-hidden="true"
             onclick="closeModal('{{ modal_id }}')"></div>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle {{ size_classes[modal_size] }} sm:w-full">
            
            <!-- Modal Header -->
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        {% if icon is defined %}
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full {{ icon_bg ?? 'bg-primary-100' }} sm:mx-0 sm:h-10 sm:w-10">
                                <i class="{{ icon }} {{ icon_color ?? 'text-primary-600' }}"></i>
                            </div>
                        {% endif %}
                        <div class="{{ icon is defined ? 'ml-4' : '' }}">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="{{ modal_id }}-title">
                                {{ title ?? 'Modal Title' }}
                            </h3>
                            {% if subtitle is defined %}
                                <p class="text-sm text-gray-500">{{ subtitle }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if modal_closable %}
                        <button type="button" 
                                class="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                onclick="closeModal('{{ modal_id }}')"
                                aria-label="Close modal">
                            <span class="sr-only">Close</span>
                            <i class="fas fa-times h-6 w-6"></i>
                        </button>
                    {% endif %}
                </div>
            </div>

            <!-- Modal Body -->
            <div class="bg-white px-4 pb-4 sm:p-6 sm:pt-0">
                {% block modal_content %}
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">
                            {{ content ?? 'Modal content goes here.' }}
                        </p>
                    </div>
                {% endblock %}
            </div>

            <!-- Modal Footer -->
            {% if modal_footer %}
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    {% block modal_footer %}
                        <button type="button" 
                                class="btn-primary w-full sm:w-auto sm:ml-3">
                            {{ primary_button ?? 'Save' }}
                        </button>
                        <button type="button" 
                                class="btn-outline w-full sm:w-auto mt-3 sm:mt-0"
                                onclick="closeModal('{{ modal_id }}')">
                            {{ secondary_button ?? 'Cancel' }}
                        </button>
                    {% endblock %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Modal functionality
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
        
        // Focus management
        const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
        
        // Trap focus within modal
        modal.addEventListener('keydown', trapFocus);
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
        modal.removeEventListener('keydown', trapFocus);
    }
}

function trapFocus(e) {
    if (e.key === 'Escape') {
        const modal = e.currentTarget;
        closeModal(modal.id);
        return;
    }
    
    if (e.key === 'Tab') {
        const modal = e.currentTarget;
        const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey) {
            if (document.activeElement === firstFocusable) {
                lastFocusable.focus();
                e.preventDefault();
            }
        } else {
            if (document.activeElement === lastFocusable) {
                firstFocusable.focus();
                e.preventDefault();
            }
        }
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('bg-opacity-75')) {
        const modal = e.target.closest('[role="dialog"]');
        if (modal) {
            closeModal(modal.id);
        }
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const openModals = document.querySelectorAll('[role="dialog"]:not(.hidden)');
        openModals.forEach(modal => {
            closeModal(modal.id);
        });
    }
});
</script>
