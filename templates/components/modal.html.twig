{#
    Modal Component
    
    Usage:
    {% embed 'components/modal.html.twig' with {
        'id': 'myModal',
        'title': 'Modal Title',
        'size': 'md'  // sm, md, lg, xl
    } %}
        {% block modal_body %}
            <p>Modal content goes here</p>
        {% endblock %}
        
        {% block modal_footer %}
            <button class="btn-outline" onclick="closeModal('myModal')">Cancel</button>
            <button class="btn-primary">Save</button>
        {% endblock %}
    {% endembed %}
#}

<div id="{{ id }}" class="modal-overlay hidden" onclick="closeModal('{{ id }}')">
    <div class="modal-container modal-{{ size ?? 'md' }}" onclick="event.stopPropagation()">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h3 class="modal-title">{{ title }}</h3>
                <button type="button" class="modal-close" onclick="closeModal('{{ id }}')">
                    <span class="sr-only">Close</span>
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Modal Body -->
            <div class="modal-body">
                {% block modal_body %}{% endblock %}
            </div>
            
            <!-- Modal Footer -->
            {% if block('modal_footer') is defined %}
                <div class="modal-footer">
                    {% block modal_footer %}{% endblock %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
        
        // Focus first input if available
        const firstInput = modal.querySelector('input, select, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
        
        // Reset form if present
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// Close modal on Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const visibleModal = document.querySelector('.modal-overlay:not(.hidden)');
        if (visibleModal) {
            closeModal(visibleModal.id);
        }
    }
});

// Domain-specific functions
function verifyDomain(domainId) {
    // Implementation for domain verification
    console.log('Verify domain:', domainId);
}

function editDomain(domainId) {
    // Implementation for domain editing
    console.log('Edit domain:', domainId);
}

function deleteDomain(domainId) {
    if (confirm('Are you sure you want to delete this domain? This action cannot be undone.')) {
        // Implementation for domain deletion
        console.log('Delete domain:', domainId);
    }
}

function showVerificationInstructions(domainId, domain, protocol, token) {
    const content = `
        <div class="space-y-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400 h-5 w-5"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Domain Verification Required
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p>To verify ownership of <strong>${domain}</strong>, you need to add a verification file to your website.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div>
                <h4 class="text-base font-medium text-gray-900 mb-3">Step 1: Download Verification File</h4>
                <p class="text-sm text-gray-600 mb-3">Download the verification file and upload it to your website's root directory.</p>
                <a href="/dashboard/domains/${domainId}/verification-file" 
                   class="btn-primary inline-flex items-center" 
                   download="seo-indexer-verification.txt">
                    <i class="fas fa-download mr-2"></i>
                    Download Verification File
                </a>
            </div>
            
            <div>
                <h4 class="text-base font-medium text-gray-900 mb-3">Step 2: Upload to Your Website</h4>
                <p class="text-sm text-gray-600 mb-3">Upload the file to your website so it's accessible at:</p>
                <div class="bg-gray-100 rounded-lg p-3 font-mono text-sm">
                    ${protocol}://${domain}/seo-indexer-verification.txt
                </div>
            </div>
            
            <div>
                <h4 class="text-base font-medium text-gray-900 mb-3">Step 3: Verify</h4>
                <p class="text-sm text-gray-600 mb-3">Once you've uploaded the file, click the "Check Verification" button below.</p>
            </div>
        </div>
    `;
    
    document.getElementById('verificationContent').innerHTML = content;
    openModal('verificationModal');
}

function checkVerification() {
    // Implementation for checking domain verification
    console.log('Checking verification...');
}
</script>
