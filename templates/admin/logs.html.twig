{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">System Logs</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearLogs()">
                <i class="fas fa-trash me-1"></i>Clear Logs
            </button>
        </div>
    </div>
</div>

<!-- Filter Controls -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Filter Logs</h6>
    </div>
    <div class="card-body">
        <form method="GET" action="/admin/logs" class="row g-3">
            <div class="col-md-3">
                <label for="level" class="form-label">Log Level</label>
                <select class="form-select" id="level" name="level">
                    <option value="">All Levels</option>
                    <option value="emergency" {{ app.request.get('level') == 'emergency' ? 'selected' : '' }}>Emergency</option>
                    <option value="alert" {{ app.request.get('level') == 'alert' ? 'selected' : '' }}>Alert</option>
                    <option value="critical" {{ app.request.get('level') == 'critical' ? 'selected' : '' }}>Critical</option>
                    <option value="error" {{ app.request.get('level') == 'error' ? 'selected' : '' }}>Error</option>
                    <option value="warning" {{ app.request.get('level') == 'warning' ? 'selected' : '' }}>Warning</option>
                    <option value="notice" {{ app.request.get('level') == 'notice' ? 'selected' : '' }}>Notice</option>
                    <option value="info" {{ app.request.get('level') == 'info' ? 'selected' : '' }}>Info</option>
                    <option value="debug" {{ app.request.get('level') == 'debug' ? 'selected' : '' }}>Debug</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    <option value="auth" {{ app.request.get('category') == 'auth' ? 'selected' : '' }}>Authentication</option>
                    <option value="security" {{ app.request.get('category') == 'security' ? 'selected' : '' }}>Security</option>
                    <option value="api" {{ app.request.get('category') == 'api' ? 'selected' : '' }}>API</option>
                    <option value="database" {{ app.request.get('category') == 'database' ? 'selected' : '' }}>Database</option>
                    <option value="system" {{ app.request.get('category') == 'system' ? 'selected' : '' }}>System</option>
                    <option value="indexing" {{ app.request.get('category') == 'indexing' ? 'selected' : '' }}>Indexing</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ app.request.get('date_from') }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ app.request.get('date_to') }}">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter me-1"></i>Apply Filters
                </button>
                <a href="/admin/logs" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Logs Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">System Log Entries</h6>
    </div>
    <div class="card-body">
        {% if logs is empty %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No Log Entries Found</h5>
                <p class="text-gray-500">No log entries match the current filters.</p>
            </div>
        {% else %}
            <div class="table-responsive">
                <table class="table table-bordered" id="logsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Level</th>
                            <th>Category</th>
                            <th>Message</th>
                            <th>User</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                            <tr class="log-row log-{{ log.level }}">
                                <td>{{ log.id }}</td>
                                <td>
                                    {% if log.level == 'emergency' %}
                                        <span class="badge bg-dark">Emergency</span>
                                    {% elseif log.level == 'alert' %}
                                        <span class="badge bg-danger">Alert</span>
                                    {% elseif log.level == 'critical' %}
                                        <span class="badge bg-danger">Critical</span>
                                    {% elseif log.level == 'error' %}
                                        <span class="badge bg-danger">Error</span>
                                    {% elseif log.level == 'warning' %}
                                        <span class="badge bg-warning">Warning</span>
                                    {% elseif log.level == 'notice' %}
                                        <span class="badge bg-info">Notice</span>
                                    {% elseif log.level == 'info' %}
                                        <span class="badge bg-primary">Info</span>
                                    {% elseif log.level == 'debug' %}
                                        <span class="badge bg-secondary">Debug</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">{{ log.level|title }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ log.category|title }}</span>
                                </td>
                                <td>
                                    <div class="log-message" style="max-width: 300px;">
                                        {{ log.message|length > 100 ? log.message|slice(0, 100) ~ '...' : log.message }}
                                        {% if log.message|length > 100 %}
                                            <br><small>
                                                <a href="#" onclick="showFullMessage({{ log.id }})" class="text-primary">Show full message</a>
                                            </small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if log.user_id %}
                                        <a href="/admin/users?search={{ log.username }}" class="text-decoration-none">
                                            {{ log.username ?? 'User #' ~ log.user_id }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Guest</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="font-monospace">{{ log.ip_address ?? '-' }}</small>
                                </td>
                                <td>
                                    <small style="max-width: 200px; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ log.user_agent }}">
                                        {{ log.user_agent ?? '-' }}
                                    </small>
                                </td>
                                <td>
                                    <small>{{ log.created_at|date('M j, Y g:i:s A') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" 
                                                class="btn btn-sm btn-primary" 
                                                onclick="viewLogDetails({{ log.id }})"
                                                title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-danger" 
                                                onclick="deleteLog({{ log.id }})"
                                                title="Delete Log Entry">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination.total_pages > 1 %}
                <nav aria-label="Logs pagination">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.current_page - 1 }}{{ app.request.query_string ? '&' ~ app.request.query_string : '' }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            </li>
                        {% endif %}

                        <!-- Page Numbers -->
                        {% for page in 1..pagination.total_pages %}
                            {% if page == pagination.current_page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                            {% elseif page == 1 or page == pagination.total_pages or (page >= pagination.current_page - 2 and page <= pagination.current_page + 2) %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page }}{{ app.request.query_string ? '&' ~ app.request.query_string : '' }}">{{ page }}</a>
                                </li>
                            {% elseif page == pagination.current_page - 3 or page == pagination.current_page + 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        <!-- Next Page -->
                        {% if pagination.current_page < pagination.total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.current_page + 1 }}{{ app.request.query_string ? '&' ~ app.request.query_string : '' }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>

                <!-- Pagination Info -->
                <div class="text-center text-muted">
                    Showing {{ pagination.offset + 1 }} to {{ pagination.offset + logs|length }} of {{ pagination.total_records }} log entries
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logDetailsModalLabel">Log Entry Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.log-row.log-error {
    background-color: #fff5f5;
}
.log-row.log-warning {
    background-color: #fffbf0;
}
.log-row.log-critical, .log-row.log-alert, .log-row.log-emergency {
    background-color: #fef2f2;
}
.log-message {
    word-break: break-word;
}
</style>

<script>
function refreshLogs() {
    location.reload();
}

function clearLogs() {
    if (confirm('Are you sure you want to clear all log entries? This action cannot be undone.')) {
        fetch('/admin/logs/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to clear logs'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing logs');
        });
    }
}

function showFullMessage(logId) {
    viewLogDetails(logId);
}

function viewLogDetails(logId) {
    const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
    const content = document.getElementById('logDetailsContent');
    
    // Show loading spinner
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    fetch('/admin/logs/' + logId + '/details')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = '<div class="alert alert-danger">Failed to load log details</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">An error occurred while loading log details</div>';
        });
}

function deleteLog(logId) {
    if (confirm('Are you sure you want to delete this log entry?')) {
        fetch('/admin/logs/' + logId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete log entry'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the log entry');
        });
    }
}
</script>
{% endblock %}
