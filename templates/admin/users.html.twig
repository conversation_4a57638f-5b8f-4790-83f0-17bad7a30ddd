{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">User Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshUsers()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">All Users</h6>
    </div>
    <div class="card-body">
        {% if users is empty %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No Users Found</h5>
                <p class="text-gray-500">No users have been registered yet.</p>
            </div>
        {% else %}
            <div class="table-responsive">
                <table class="table table-bordered" id="usersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Name</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Email Verified</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>
                                    <strong>{{ user.username }}</strong>
                                </td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.first_name or user.last_name %}
                                        {{ user.first_name }} {{ user.last_name }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.role == 'admin' %}
                                        <span class="badge bg-danger">Admin</span>
                                    {% else %}
                                        <span class="badge bg-primary">User</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.status == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% elseif user.status == 'blocked' %}
                                        <span class="badge bg-danger">Blocked</span>
                                    {% elseif user.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ user.status|title }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.email_verified %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Verified
                                        </span>
                                    {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.last_login %}
                                        <small>{{ user.last_login|date('M j, Y g:i A') }}</small>
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ user.created_at|date('M j, Y') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if user.status == 'active' %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-warning" 
                                                    onclick="blockUser({{ user.id }})"
                                                    title="Block User">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        {% elseif user.status == 'blocked' %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-success" 
                                                    onclick="unblockUser({{ user.id }})"
                                                    title="Unblock User">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        {% endif %}
                                        
                                        {% if user.role != 'admin' %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-danger" 
                                                    onclick="deleteUser({{ user.id }})"
                                                    title="Delete User">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination.total_pages > 1 %}
                <nav aria-label="Users pagination">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.current_page - 1 }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            </li>
                        {% endif %}

                        <!-- Page Numbers -->
                        {% for page in 1..pagination.total_pages %}
                            {% if page == pagination.current_page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                            {% elseif page == 1 or page == pagination.total_pages or (page >= pagination.current_page - 2 and page <= pagination.current_page + 2) %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page }}">{{ page }}</a>
                                </li>
                            {% elseif page == pagination.current_page - 3 or page == pagination.current_page + 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        <!-- Next Page -->
                        {% if pagination.current_page < pagination.total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.current_page + 1 }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>

                <!-- Pagination Info -->
                <div class="text-center text-muted">
                    Showing {{ pagination.offset + 1 }} to {{ pagination.offset + users|length }} of {{ pagination.total_records }} users
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>

<script>
function refreshUsers() {
    location.reload();
}

function blockUser(userId) {
    if (confirm('Are you sure you want to block this user?')) {
        fetch('/admin/users/block', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                user_id: userId,
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to block user'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while blocking the user');
        });
    }
}

function unblockUser(userId) {
    if (confirm('Are you sure you want to unblock this user?')) {
        fetch('/admin/users/unblock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                user_id: userId,
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to unblock user'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while unblocking the user');
        });
    }
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch('/admin/users/' + userId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete user'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the user');
        });
    }
}
</script>
{% endblock %}
