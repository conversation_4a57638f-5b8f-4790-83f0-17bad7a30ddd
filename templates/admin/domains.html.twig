{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Domain Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDomains()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- Domains Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">All Domains</h6>
    </div>
    <div class="card-body">
        {% if domains is empty %}
            <div class="text-center py-5">
                <i class="fas fa-globe fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No Domains Found</h5>
                <p class="text-gray-500">No domains have been registered yet.</p>
            </div>
        {% else %}
            <div class="table-responsive">
                <table class="table table-bordered" id="domainsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Domain</th>
                            <th>Owner</th>
                            <th>Status</th>
                            <th>Verification</th>
                            <th>URLs Count</th>
                            <th>Last Submission</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for domain in domains %}
                            <tr>
                                <td>{{ domain.id }}</td>
                                <td>
                                    <strong>{{ domain.domain_name }}</strong>
                                    {% if domain.is_https %}
                                        <i class="fas fa-lock text-success ms-1" title="HTTPS Enabled"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.username %}
                                        <a href="/admin/users?search={{ domain.username }}" class="text-decoration-none">
                                            {{ domain.username }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">Unknown</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.status == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                    {% elseif domain.status == 'suspended' %}
                                        <span class="badge bg-danger">Suspended</span>
                                    {% elseif domain.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ domain.status|title }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.is_verified %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Verified
                                        </span>
                                        {% if domain.verified_at %}
                                            <br><small class="text-muted">{{ domain.verified_at|date('M j, Y') }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ domain.url_count ?? 0 }}</span>
                                    {% if domain.url_count > 0 %}
                                        <br><small class="text-muted">URLs submitted</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if domain.last_submission %}
                                        <small>{{ domain.last_submission|date('M j, Y g:i A') }}</small>
                                    {% else %}
                                        <span class="text-muted">Never</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ domain.created_at|date('M j, Y') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if domain.status == 'active' %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-warning" 
                                                    onclick="suspendDomain({{ domain.id }})"
                                                    title="Suspend Domain">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        {% elseif domain.status == 'suspended' %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-success" 
                                                    onclick="activateDomain({{ domain.id }})"
                                                    title="Activate Domain">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        {% endif %}
                                        
                                        {% if not domain.is_verified %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-info" 
                                                    onclick="verifyDomain({{ domain.id }})"
                                                    title="Force Verify Domain">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                        {% endif %}
                                        
                                        <button type="button" 
                                                class="btn btn-sm btn-primary" 
                                                onclick="viewDomainDetails({{ domain.id }})"
                                                title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <button type="button" 
                                                class="btn btn-sm btn-danger" 
                                                onclick="deleteDomain({{ domain.id }})"
                                                title="Delete Domain">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination.total_pages > 1 %}
                <nav aria-label="Domains pagination">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        {% if pagination.current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.current_page - 1 }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            </li>
                        {% endif %}

                        <!-- Page Numbers -->
                        {% for page in 1..pagination.total_pages %}
                            {% if page == pagination.current_page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                            {% elseif page == 1 or page == pagination.total_pages or (page >= pagination.current_page - 2 and page <= pagination.current_page + 2) %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page }}">{{ page }}</a>
                                </li>
                            {% elseif page == pagination.current_page - 3 or page == pagination.current_page + 3 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}

                        <!-- Next Page -->
                        {% if pagination.current_page < pagination.total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.current_page + 1 }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            </li>
                        {% endif %}
                    </ul>
                </nav>

                <!-- Pagination Info -->
                <div class="text-center text-muted">
                    Showing {{ pagination.offset + 1 }} to {{ pagination.offset + domains|length }} of {{ pagination.total_records }} domains
                </div>
            {% endif %}
        {% endif %}
    </div>
</div>

<!-- Domain Details Modal -->
<div class="modal fade" id="domainDetailsModal" tabindex="-1" aria-labelledby="domainDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="domainDetailsModalLabel">Domain Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="domainDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDomains() {
    location.reload();
}

function suspendDomain(domainId) {
    if (confirm('Are you sure you want to suspend this domain?')) {
        fetch('/admin/domains/suspend', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                domain_id: domainId,
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to suspend domain'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while suspending the domain');
        });
    }
}

function activateDomain(domainId) {
    if (confirm('Are you sure you want to activate this domain?')) {
        fetch('/admin/domains/activate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                domain_id: domainId,
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to activate domain'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while activating the domain');
        });
    }
}

function verifyDomain(domainId) {
    if (confirm('Are you sure you want to force verify this domain?')) {
        fetch('/admin/domains/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                domain_id: domainId,
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to verify domain'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while verifying the domain');
        });
    }
}

function viewDomainDetails(domainId) {
    const modal = new bootstrap.Modal(document.getElementById('domainDetailsModal'));
    const content = document.getElementById('domainDetailsContent');
    
    // Show loading spinner
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    fetch('/admin/domains/' + domainId + '/details')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = '<div class="alert alert-danger">Failed to load domain details</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="alert alert-danger">An error occurred while loading domain details</div>';
        });
}

function deleteDomain(domainId) {
    if (confirm('Are you sure you want to delete this domain? This action cannot be undone and will also delete all associated URLs.')) {
        fetch('/admin/domains/' + domainId, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete domain'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the domain');
        });
    }
}
</script>
{% endblock %}
