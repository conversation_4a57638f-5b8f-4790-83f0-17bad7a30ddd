{% extends "layouts/base.html.twig" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">System Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-success" onclick="saveSettings()">
                <i class="fas fa-save me-1"></i>Save Changes
            </button>
        </div>
    </div>
</div>

<!-- Settings Form -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Platform Configuration</h6>
    </div>
    <div class="card-body">
        <form id="settingsForm" method="POST" action="/admin/settings/save">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            
            {% if settings is empty %}
                <div class="text-center py-5">
                    <i class="fas fa-cogs fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No Settings Found</h5>
                    <p class="text-gray-500">System settings have not been initialized.</p>
                </div>
            {% else %}
                <div class="row">
                    <!-- API Quotas Section -->
                    <div class="col-md-6 mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-chart-line me-2"></i>API Quotas
                        </h5>
                        
                        {% for setting in settings %}
                            {% if setting.setting_key starts with 'google_' or setting.setting_key starts with 'bing_' %}
                                <div class="mb-3">
                                    <label for="{{ setting.setting_key }}" class="form-label">
                                        {{ setting.setting_key|replace({'_': ' '})|title }}
                                        {% if setting.description %}
                                            <i class="fas fa-info-circle text-muted" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ setting.description }}"></i>
                                        {% endif %}
                                    </label>
                                    
                                    {% if setting.setting_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="{{ setting.setting_key }}" 
                                                   name="settings[{{ setting.setting_key }}]" 
                                                   value="true"
                                                   {{ setting.setting_value == 'true' ? 'checked' : '' }}>
                                        </div>
                                    {% elseif setting.setting_type == 'integer' %}
                                        <input type="number" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}"
                                               min="0">
                                    {% else %}
                                        <input type="text" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}">
                                    {% endif %}
                                    
                                    {% if setting.description %}
                                        <div class="form-text">{{ setting.description }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- User Limits Section -->
                    <div class="col-md-6 mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-users me-2"></i>User Limits
                        </h5>
                        
                        {% for setting in settings %}
                            {% if setting.setting_key starts with 'max_' %}
                                <div class="mb-3">
                                    <label for="{{ setting.setting_key }}" class="form-label">
                                        {{ setting.setting_key|replace({'_': ' '})|title }}
                                        {% if setting.description %}
                                            <i class="fas fa-info-circle text-muted" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ setting.description }}"></i>
                                        {% endif %}
                                    </label>
                                    
                                    {% if setting.setting_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="{{ setting.setting_key }}" 
                                                   name="settings[{{ setting.setting_key }}]" 
                                                   value="true"
                                                   {{ setting.setting_value == 'true' ? 'checked' : '' }}>
                                        </div>
                                    {% elseif setting.setting_type == 'integer' %}
                                        <input type="number" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}"
                                               min="1">
                                    {% else %}
                                        <input type="text" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}">
                                    {% endif %}
                                    
                                    {% if setting.description %}
                                        <div class="form-text">{{ setting.description }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <div class="row">
                    <!-- Rate Limiting Section -->
                    <div class="col-md-6 mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-shield-alt me-2"></i>Rate Limiting
                        </h5>
                        
                        {% for setting in settings %}
                            {% if setting.setting_key starts with 'rate_limit_' %}
                                <div class="mb-3">
                                    <label for="{{ setting.setting_key }}" class="form-label">
                                        {{ setting.setting_key|replace({'_': ' '})|title }}
                                        {% if setting.description %}
                                            <i class="fas fa-info-circle text-muted" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ setting.description }}"></i>
                                        {% endif %}
                                    </label>
                                    
                                    {% if setting.setting_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="{{ setting.setting_key }}" 
                                                   name="settings[{{ setting.setting_key }}]" 
                                                   value="true"
                                                   {{ setting.setting_value == 'true' ? 'checked' : '' }}>
                                        </div>
                                    {% elseif setting.setting_type == 'integer' %}
                                        <input type="number" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}"
                                               min="1">
                                    {% else %}
                                        <input type="text" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}">
                                    {% endif %}
                                    
                                    {% if setting.description %}
                                        <div class="form-text">{{ setting.description }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                    
                    <!-- System Features Section -->
                    <div class="col-md-6 mb-4">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-toggle-on me-2"></i>System Features
                        </h5>
                        
                        {% for setting in settings %}
                            {% if setting.setting_key in ['maintenance_mode', 'registration_enabled', 'email_verification_required'] %}
                                <div class="mb-3">
                                    <label for="{{ setting.setting_key }}" class="form-label">
                                        {{ setting.setting_key|replace({'_': ' '})|title }}
                                        {% if setting.description %}
                                            <i class="fas fa-info-circle text-muted" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ setting.description }}"></i>
                                        {% endif %}
                                    </label>
                                    
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="true"
                                               {{ setting.setting_value == 'true' ? 'checked' : '' }}>
                                    </div>
                                    
                                    {% if setting.description %}
                                        <div class="form-text">{{ setting.description }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Other Settings Section -->
                <div class="row">
                    <div class="col-12">
                        <h5 class="text-primary mb-3">
                            <i class="fas fa-cog me-2"></i>Other Settings
                        </h5>
                        
                        {% for setting in settings %}
                            {% if not (setting.setting_key starts with 'google_' or 
                                      setting.setting_key starts with 'bing_' or 
                                      setting.setting_key starts with 'max_' or 
                                      setting.setting_key starts with 'rate_limit_' or 
                                      setting.setting_key in ['maintenance_mode', 'registration_enabled', 'email_verification_required']) %}
                                <div class="mb-3 col-md-6">
                                    <label for="{{ setting.setting_key }}" class="form-label">
                                        {{ setting.setting_key|replace({'_': ' '})|title }}
                                        {% if setting.description %}
                                            <i class="fas fa-info-circle text-muted" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ setting.description }}"></i>
                                        {% endif %}
                                    </label>
                                    
                                    {% if setting.setting_type == 'boolean' %}
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="{{ setting.setting_key }}" 
                                                   name="settings[{{ setting.setting_key }}]" 
                                                   value="true"
                                                   {{ setting.setting_value == 'true' ? 'checked' : '' }}>
                                        </div>
                                    {% elseif setting.setting_type == 'integer' %}
                                        <input type="number" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}">
                                    {% else %}
                                        <input type="text" 
                                               class="form-control" 
                                               id="{{ setting.setting_key }}" 
                                               name="settings[{{ setting.setting_key }}]" 
                                               value="{{ setting.setting_value }}">
                                    {% endif %}
                                    
                                    {% if setting.description %}
                                        <div class="form-text">{{ setting.description }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </form>
    </div>
</div>

<script>
function saveSettings() {
    document.getElementById('settingsForm').submit();
}

// Initialize tooltips
$(document).ready(function() {
    $('[data-bs-toggle="tooltip"]').tooltip();
});
</script>
{% endblock %}
