<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.0/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         colors="true"
         cacheDirectory=".phpunit.cache"
         executionOrder="depends,defects"
         requireCoverageMetadata="true"
         beStrictAboutCoverageMetadata="true"
         beStrictAboutOutputDuringTests="true"
         failOnRisky="true"
         failOnWarning="true">

    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
        <testsuite name="Security">
            <directory>tests/Security</directory>
        </testsuite>
    </testsuites>

    <source>
        <include>
            <directory>src</directory>
        </include>
        <exclude>
            <directory>src/Views</directory>
            <file>src/App.php</file>
        </exclude>
    </source>

    <coverage>
        <report>
            <html outputDirectory="tests/coverage/html"/>
            <text outputFile="tests/coverage/coverage.txt"/>
            <xml outputDirectory="tests/coverage/xml"/>
        </report>
    </coverage>

    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_HOST" value="localhost"/>
        <env name="DB_NAME" value="seo_indexer_test"/>
        <env name="DB_USER" value="test_user"/>
        <env name="DB_PASS" value="test_pass"/>
        <env name="ENCRYPTION_KEY" value="test-encryption-key-32-characters"/>
        <env name="JWT_SECRET" value="test-jwt-secret-key"/>
        <env name="GOOGLE_CLIENT_ID" value="test-google-client-id"/>
        <env name="GOOGLE_CLIENT_SECRET" value="test-google-client-secret"/>
        <env name="BING_CLIENT_ID" value="test-bing-client-id"/>
        <env name="BING_CLIENT_SECRET" value="test-bing-client-secret"/>
    </php>

    <logging>
        <junit outputFile="tests/results/junit.xml"/>
    </logging>
</phpunit>
