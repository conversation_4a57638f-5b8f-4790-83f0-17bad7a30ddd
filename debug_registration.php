<?php

require_once 'vendor/autoload.php';

use App\Services\DatabaseService;
use App\Services\AuthService;
use App\Services\SessionService;
use App\Models\User;
use App\Utils\Config;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Initialize database
$config = new Config();
$logger = new Logger('debug');
$logger->pushHandler(new StreamHandler('php://stdout'));

echo "Database config:\n";
echo "Host: " . $config->get('DB_HOST') . "\n";
echo "Port: " . $config->get('DB_PORT') . "\n";
echo "Database: " . $config->get('DB_NAME') . "\n";
echo "User: " . $config->get('DB_USER') . "\n";
echo "Socket: " . $config->get('DB_SOCKET') . "\n\n";

try {
    $db = new DatabaseService($config, $logger);
    $userModel = new User($db);
    $authService = new AuthService($db, $userModel, null, null);

    // Test registration data
    $testData = [
        'username' => 'testuser' . time(),
        'email' => 'test' . time() . '@example.com',
        'password' => 'TestPass123',
        'confirm_password' => 'TestPass123',
        'first_name' => 'Test',
        'last_name' => 'User'
    ];

    echo "Testing registration with data:\n";
    print_r($testData);

    $result = $authService->register($testData);

    echo "\nRegistration result:\n";
    print_r($result);

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
