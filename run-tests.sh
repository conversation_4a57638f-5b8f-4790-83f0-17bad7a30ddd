#!/bin/bash

# SEO Indexer Platform - Test Runner Script
# This script runs the complete test suite with various options

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
RUN_UNIT=true
RUN_INTEGRATION=true
RUN_FEATURE=true
RUN_SECURITY=true
RUN_COVERAGE=false
VERBOSE=false
STOP_ON_FAILURE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -u, --unit-only       Run only unit tests"
    echo "  -i, --integration-only Run only integration tests"
    echo "  -f, --feature-only    Run only feature tests"
    echo "  -s, --security-only   Run only security tests"
    echo "  -c, --coverage        Generate code coverage report"
    echo "  -v, --verbose         Verbose output"
    echo "  --stop-on-failure     Stop on first failure"
    echo "  -h, --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    Run all tests"
    echo "  $0 -u                 Run only unit tests"
    echo "  $0 -c                 Run all tests with coverage"
    echo "  $0 -u -c              Run unit tests with coverage"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--unit-only)
            RUN_UNIT=true
            RUN_INTEGRATION=false
            RUN_FEATURE=false
            RUN_SECURITY=false
            shift
            ;;
        -i|--integration-only)
            RUN_UNIT=false
            RUN_INTEGRATION=true
            RUN_FEATURE=false
            RUN_SECURITY=false
            shift
            ;;
        -f|--feature-only)
            RUN_UNIT=false
            RUN_INTEGRATION=false
            RUN_FEATURE=true
            RUN_SECURITY=false
            shift
            ;;
        -s|--security-only)
            RUN_UNIT=false
            RUN_INTEGRATION=false
            RUN_FEATURE=false
            RUN_SECURITY=true
            shift
            ;;
        -c|--coverage)
            RUN_COVERAGE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --stop-on-failure)
            STOP_ON_FAILURE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if PHP and Composer are available
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed or not in PATH"
    exit 1
fi

if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed or not in PATH"
    exit 1
fi

# Check if vendor directory exists
if [ ! -d "vendor" ]; then
    print_warning "Vendor directory not found. Running composer install..."
    composer install --dev
fi

# Create necessary directories
mkdir -p tests/coverage/html
mkdir -p tests/coverage/xml
mkdir -p tests/results

print_status "Starting SEO Indexer Platform Test Suite"
echo "=========================================="

# Set up test environment
export APP_ENV=testing
export DB_NAME=seo_indexer_test

# Build PHPUnit command
PHPUNIT_CMD="vendor/bin/phpunit"

if [ "$VERBOSE" = true ]; then
    PHPUNIT_CMD="$PHPUNIT_CMD --verbose"
fi

if [ "$STOP_ON_FAILURE" = true ]; then
    PHPUNIT_CMD="$PHPUNIT_CMD --stop-on-failure"
fi

# Run tests based on options
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

run_test_suite() {
    local suite_name=$1
    local suite_flag=$2
    
    print_status "Running $suite_name tests..."
    
    if [ "$RUN_COVERAGE" = true ]; then
        COVERAGE_CMD="--coverage-html tests/coverage/html/$suite_name --coverage-text"
    else
        COVERAGE_CMD=""
    fi
    
    if $PHPUNIT_CMD $suite_flag $COVERAGE_CMD; then
        print_success "$suite_name tests passed"
        return 0
    else
        print_error "$suite_name tests failed"
        return 1
    fi
}

# Run selected test suites
if [ "$RUN_UNIT" = true ]; then
    if run_test_suite "Unit" "--testsuite=Unit"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
fi

if [ "$RUN_INTEGRATION" = true ]; then
    if run_test_suite "Integration" "--testsuite=Integration"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
fi

if [ "$RUN_FEATURE" = true ]; then
    if run_test_suite "Feature" "--testsuite=Feature"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
fi

if [ "$RUN_SECURITY" = true ]; then
    if run_test_suite "Security" "--testsuite=Security"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
fi

# Generate combined coverage report if requested
if [ "$RUN_COVERAGE" = true ]; then
    print_status "Generating combined coverage report..."
    $PHPUNIT_CMD --coverage-html tests/coverage/html/combined --coverage-text > tests/coverage/coverage.txt
    print_success "Coverage report generated in tests/coverage/"
fi

# Run code quality checks
print_status "Running code quality checks..."

# PHPStan static analysis
if command -v vendor/bin/phpstan &> /dev/null; then
    print_status "Running PHPStan static analysis..."
    if vendor/bin/phpstan analyse src --level=5; then
        print_success "PHPStan analysis passed"
    else
        print_warning "PHPStan analysis found issues"
    fi
else
    print_warning "PHPStan not found, skipping static analysis"
fi

# PHP CodeSniffer
if command -v vendor/bin/phpcs &> /dev/null; then
    print_status "Running PHP CodeSniffer..."
    if vendor/bin/phpcs src --standard=PSR12; then
        print_success "Code style check passed"
    else
        print_warning "Code style issues found"
    fi
else
    print_warning "PHP CodeSniffer not found, skipping code style check"
fi

# Print summary
echo ""
echo "=========================================="
print_status "Test Summary"
echo "=========================================="
echo "Total test suites: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    print_success "All tests passed! 🎉"
    exit 0
else
    print_error "$FAILED_TESTS test suite(s) failed"
    exit 1
fi
