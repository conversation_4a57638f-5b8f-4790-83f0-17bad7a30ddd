@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-100 text-secondary-700 hover:bg-secondary-200 focus:ring-secondary-500 border border-secondary-200;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm;
  }
  
  .btn-warning {
    @apply btn bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-400 shadow-sm;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500 shadow-sm;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:ring-primary-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-200 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }
  
  /* Form Components */
  .form-group {
    @apply space-y-2;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-select {
    @apply form-input pr-10 bg-white;
  }
  
  .form-textarea {
    @apply form-input resize-none;
  }
  
  .form-error {
    @apply text-sm text-danger-600;
  }
  
  .form-help {
    @apply text-sm text-gray-500;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  /* Alert Components */
  .alert {
    @apply p-4 rounded-lg border;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-danger {
    @apply alert bg-danger-50 border-danger-200 text-danger-800;
  }
  
  .alert-info {
    @apply alert bg-primary-50 border-primary-200 text-primary-800;
  }
  
  /* Navigation Components */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .nav-link-active {
    @apply nav-link bg-primary-100 text-primary-700 border-r-2 border-primary-600;
  }
  
  .nav-link-inactive {
    @apply nav-link text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
  
  /* Table Components */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* Loading Components */
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-primary-600;
  }
  
  .loading-dots {
    @apply flex space-x-1;
  }
  
  .loading-dot {
    @apply h-2 w-2 bg-primary-600 rounded-full animate-pulse;
  }
  
  /* Utility Components */
  .divider {
    @apply border-t border-gray-200 my-6;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
  
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
}
