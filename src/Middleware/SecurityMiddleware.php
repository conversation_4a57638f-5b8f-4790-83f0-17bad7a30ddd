<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Services\SecurityService;
use App\Services\SessionService;
use App\Utils\Config;
use Monolog\Logger;

class SecurityMiddleware
{
    private SecurityService $security;
    private SessionService $session;
    private Config $config;
    private Logger $logger;

    public function __construct(SecurityService $security, SessionService $session, Config $config, Logger $logger)
    {
        $this->security = $security;
        $this->session = $session;
        $this->config = $config;
        $this->logger = $logger;
    }

    public function handle(string $method, string $path, array $params = []): ?array
    {
        // Get client IP
        $clientIp = $this->getClientIp();
        
        // Check if IP is blocked
        if ($this->security->isIpBlocked($clientIp)) {
            $this->security->logSecurityEvent('blocked_ip_access_attempt', [
                'ip_address' => $clientIp,
                'path' => $path,
                'method' => $method,
                'severity' => 'warning'
            ]);
            
            return [
                'status' => 403,
                'error' => 'Access denied. Your IP address has been temporarily blocked.'
            ];
        }

        // Apply security headers
        $this->setSecurityHeaders();

        // Rate limiting based on endpoint type
        $rateLimitResult = $this->applyRateLimit($method, $path, $clientIp);
        if (!$rateLimitResult['allowed']) {
            return [
                'status' => 429,
                'error' => 'Too many requests. Please try again later.',
                'headers' => [
                    'X-RateLimit-Limit' => $rateLimitResult['max_attempts'],
                    'X-RateLimit-Remaining' => $rateLimitResult['remaining'],
                    'X-RateLimit-Reset' => $rateLimitResult['reset_time'],
                    'Retry-After' => $rateLimitResult['reset_time'] - time()
                ]
            ];
        }

        // CSRF protection for state-changing requests
        if (in_array($method, ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            if (!$this->validateCsrfToken()) {
                $this->security->logSecurityEvent('csrf_token_mismatch', [
                    'ip_address' => $clientIp,
                    'path' => $path,
                    'method' => $method,
                    'user_id' => $this->session->getUserId(),
                    'severity' => 'warning'
                ]);
                
                return [
                    'status' => 403,
                    'error' => 'Invalid security token. Please refresh the page and try again.'
                ];
            }
        }

        // Input validation and sanitization
        $this->sanitizeGlobalInputs();

        // Log suspicious activity
        $this->detectSuspiciousActivity($method, $path, $clientIp);

        // Record the attempt for rate limiting
        $this->security->recordAttempt($clientIp, $this->getRateLimitKey($method, $path));

        return null; // Continue processing
    }

    private function applyRateLimit(string $method, string $path, string $clientIp): array
    {
        $rateLimitKey = $this->getRateLimitKey($method, $path);
        $userId = $this->session->getUserId();
        $identifier = $userId ? "user:{$userId}" : "ip:{$clientIp}";

        // Different limits for different endpoint types
        if (strpos($path, '/api/') === 0) {
            // API endpoints - stricter limits
            return $this->security->checkRateLimit($identifier, $rateLimitKey, 100, 60); // 100 requests per hour
        } elseif (strpos($path, '/auth/') === 0) {
            // Authentication endpoints - very strict
            return $this->security->checkRateLimit($identifier, $rateLimitKey, 10, 15); // 10 attempts per 15 minutes
        } elseif (in_array($method, ['POST', 'PUT', 'DELETE'])) {
            // State-changing operations - moderate limits
            return $this->security->checkRateLimit($identifier, $rateLimitKey, 30, 60); // 30 requests per hour
        } else {
            // General requests - lenient limits
            return $this->security->checkRateLimit($identifier, $rateLimitKey, 200, 60); // 200 requests per hour
        }
    }

    private function getRateLimitKey(string $method, string $path): string
    {
        // Normalize path for rate limiting
        $normalizedPath = preg_replace('/\/\d+/', '/{id}', $path);
        return strtolower($method) . ':' . $normalizedPath;
    }

    private function setSecurityHeaders(): void
    {
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // XSS protection
        header('X-XSS-Protection: 1; mode=block');
        
        // Referrer policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "font-src 'self' https://cdnjs.cloudflare.com; " .
               "connect-src 'self'; " .
               "frame-ancestors 'none';";
        header("Content-Security-Policy: {$csp}");
        
        // HTTPS enforcement in production
        if ($this->config->get('APP_ENV') === 'production') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
    }

    private function validateCsrfToken(): bool
    {
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        return $this->session->validateCsrfToken($token);
    }

    private function sanitizeGlobalInputs(): void
    {
        // Sanitize GET parameters
        if (!empty($_GET)) {
            foreach ($_GET as $key => $value) {
                if (is_string($value)) {
                    $_GET[$key] = $this->security->sanitizeInput($value);
                }
            }
        }

        // Sanitize POST parameters (except for specific fields that need raw data)
        if (!empty($_POST)) {
            $skipSanitization = ['password', 'confirm_password', 'csrf_token'];
            foreach ($_POST as $key => $value) {
                if (is_string($value) && !in_array($key, $skipSanitization)) {
                    $_POST[$key] = $this->security->sanitizeInput($value);
                }
            }
        }
    }

    private function detectSuspiciousActivity(string $method, string $path, string $clientIp): void
    {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $suspicious = false;
        $reasons = [];

        // Check for common attack patterns in path
        $attackPatterns = [
            '/\.\./i' => 'directory_traversal',
            '/<script/i' => 'xss_attempt',
            '/union.*select/i' => 'sql_injection',
            '/exec\(/i' => 'code_injection',
            '/eval\(/i' => 'code_injection',
            '/base64_decode/i' => 'code_injection'
        ];

        foreach ($attackPatterns as $pattern => $type) {
            if (preg_match($pattern, $path) || preg_match($pattern, http_build_query($_GET))) {
                $suspicious = true;
                $reasons[] = $type;
            }
        }

        // Check for suspicious user agents
        $suspiciousUserAgents = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'nessus', 'openvas',
            'burpsuite', 'w3af', 'acunetix', 'appscan'
        ];

        foreach ($suspiciousUserAgents as $suspiciousAgent) {
            if (stripos($userAgent, $suspiciousAgent) !== false) {
                $suspicious = true;
                $reasons[] = 'suspicious_user_agent';
                break;
            }
        }

        // Check for empty or very short user agent
        if (empty($userAgent) || strlen($userAgent) < 10) {
            $suspicious = true;
            $reasons[] = 'missing_user_agent';
        }

        // Check for rapid requests from same IP
        $recentRequests = $this->security->checkRateLimit("ip:{$clientIp}", 'rapid_requests', 50, 5);
        if (!$recentRequests['allowed']) {
            $suspicious = true;
            $reasons[] = 'rapid_requests';
        }

        if ($suspicious) {
            $this->security->logSecurityEvent('suspicious_activity', [
                'ip_address' => $clientIp,
                'path' => $path,
                'method' => $method,
                'user_agent' => $userAgent,
                'reasons' => $reasons,
                'user_id' => $this->session->getUserId(),
                'severity' => 'warning'
            ]);

            // Auto-block IP if multiple suspicious activities
            if (count($reasons) >= 3) {
                $this->security->blockIp($clientIp, 'Multiple suspicious activities: ' . implode(', ', $reasons), 60);
            }
        }
    }

    private function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    public function handleSecurityViolation(string $type, array $data = []): void
    {
        $clientIp = $this->getClientIp();
        
        $this->security->logSecurityEvent($type, array_merge($data, [
            'ip_address' => $clientIp,
            'user_id' => $this->session->getUserId(),
            'severity' => 'high'
        ]));

        // Auto-block for severe violations
        $severeViolations = ['sql_injection', 'xss_attempt', 'code_injection', 'brute_force'];
        if (in_array($type, $severeViolations)) {
            $this->security->blockIp($clientIp, "Security violation: {$type}", 120);
        }
    }
}
