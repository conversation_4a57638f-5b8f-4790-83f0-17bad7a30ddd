<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Services\PerformanceMonitoringService;
use App\Services\CacheService;
use App\Services\DatabaseService;
use App\Utils\Config;
use Monolog\Logger;

class PerformanceMiddleware
{
    private PerformanceMonitoringService $performanceMonitor;
    private CacheService $cache;
    private DatabaseService $db;
    private Config $config;
    private Logger $logger;
    private float $requestStartTime;
    private int $requestStartMemory;

    public function __construct(
        PerformanceMonitoringService $performanceMonitor,
        CacheService $cache,
        DatabaseService $db,
        Config $config,
        Logger $logger
    ) {
        $this->performanceMonitor = $performanceMonitor;
        $this->cache = $cache;
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
    }

    public function before(string $method, string $path): void
    {
        $this->requestStartTime = microtime(true);
        $this->requestStartMemory = memory_get_usage(true);

        // Start performance monitoring
        $this->performanceMonitor->startTimer('request_total');
        $this->performanceMonitor->startTimer('request_processing');

        // Record request start metrics
        $this->performanceMonitor->recordMetric('request_memory_start', $this->requestStartMemory, 'gauge');
        $this->performanceMonitor->recordMetric('concurrent_requests', $this->getConcurrentRequests(), 'gauge');

        // Set performance monitoring on database service
        $this->db->setPerformanceMonitor($this->performanceMonitor);
    }

    public function after(string $method, string $path, int $statusCode, array $headers = []): void
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        // Calculate metrics
        $totalTime = $endTime - $this->requestStartTime;
        $memoryUsed = $endMemory - $this->requestStartMemory;
        $peakMemory = memory_get_peak_usage(true);

        // End performance timers
        $this->performanceMonitor->endTimer('request_processing');
        $this->performanceMonitor->endTimer('request_total');

        // Record endpoint performance
        $this->performanceMonitor->recordEndpointPerformance($path, $totalTime);

        // Record detailed metrics
        $this->recordDetailedMetrics($method, $path, $statusCode, $totalTime, $memoryUsed, $peakMemory);

        // Store endpoint performance in database
        $this->storeEndpointPerformance($method, $path, $statusCode, $totalTime, $memoryUsed);

        // Check for performance alerts
        $this->checkPerformanceAlerts($path, $totalTime, $memoryUsed);

        // Add performance headers if enabled
        if ($this->config->get('PERFORMANCE_HEADERS_ENABLED', false)) {
            $this->addPerformanceHeaders($totalTime, $memoryUsed, $peakMemory);
        }

        // Log slow requests
        $slowThreshold = (float)$this->config->get('SLOW_REQUEST_THRESHOLD', 2.0);
        if ($totalTime > $slowThreshold) {
            $this->logger->warning('Slow request detected', [
                'method' => $method,
                'path' => $path,
                'execution_time' => $totalTime,
                'memory_used' => $memoryUsed,
                'status_code' => $statusCode
            ]);
        }
    }

    private function recordDetailedMetrics(
        string $method,
        string $path,
        int $statusCode,
        float $totalTime,
        int $memoryUsed,
        int $peakMemory
    ): void {
        // Record general metrics
        $this->performanceMonitor->recordMetric('request_duration', $totalTime, 'timing');
        $this->performanceMonitor->recordMetric('request_memory_used', $memoryUsed, 'gauge');
        $this->performanceMonitor->recordMetric('request_memory_peak', $peakMemory, 'gauge');

        // Record HTTP status metrics
        $this->performanceMonitor->recordMetric("http_status_{$statusCode}", 1, 'counter');
        
        if ($statusCode >= 400) {
            $this->performanceMonitor->recordMetric('http_errors', 1, 'counter');
        }

        // Record method-specific metrics
        $this->performanceMonitor->recordMetric("http_method_{$method}", 1, 'counter');

        // Record endpoint-specific metrics
        $normalizedPath = $this->normalizePath($path);
        $this->performanceMonitor->recordMetric("endpoint_requests:{$normalizedPath}", 1, 'counter');
        $this->performanceMonitor->recordMetric("endpoint_duration:{$normalizedPath}", $totalTime, 'timing');

        // Record system metrics
        $this->recordSystemMetrics();
    }

    private function recordSystemMetrics(): void
    {
        $systemMetrics = $this->performanceMonitor->getSystemMetrics();

        // Memory metrics
        $this->performanceMonitor->recordMetric('system_memory_usage', $systemMetrics['memory']['current_usage'], 'gauge');
        $this->performanceMonitor->recordMetric('system_memory_peak', $systemMetrics['memory']['peak_usage'], 'gauge');

        // CPU metrics
        if (isset($systemMetrics['cpu']['load_1min'])) {
            $this->performanceMonitor->recordMetric('system_cpu_load_1min', $systemMetrics['cpu']['load_1min'], 'gauge');
        }

        // Database metrics
        if (isset($systemMetrics['database']['threads_connected'])) {
            $this->performanceMonitor->recordMetric('db_connections_active', $systemMetrics['database']['threads_connected'], 'gauge');
        }

        // Cache metrics
        $cacheStats = $systemMetrics['cache'];
        $this->performanceMonitor->recordMetric('cache_entries', $cacheStats['memory_cache_entries'], 'gauge');
        $this->performanceMonitor->recordMetric('cache_size_mb', $cacheStats['total_size_mb'], 'gauge');
    }

    private function storeEndpointPerformance(
        string $method,
        string $path,
        int $statusCode,
        float $responseTime,
        int $memoryUsage
    ): void {
        try {
            $this->db->insert('endpoint_performance', [
                'endpoint' => $path,
                'method' => $method,
                'response_time' => $responseTime,
                'memory_usage' => $memoryUsage,
                'status_code' => $statusCode,
                'user_id' => $this->getCurrentUserId(),
                'ip_address' => $this->getClientIp(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Failed to store endpoint performance', [
                'error' => $e->getMessage(),
                'endpoint' => $path
            ]);
        }
    }

    private function checkPerformanceAlerts(string $path, float $totalTime, int $memoryUsed): void
    {
        $alerts = [];

        // Check response time alerts
        $slowThreshold = (float)$this->config->get('SLOW_REQUEST_THRESHOLD', 2.0);
        $criticalThreshold = (float)$this->config->get('CRITICAL_REQUEST_THRESHOLD', 5.0);

        if ($totalTime > $criticalThreshold) {
            $alerts[] = [
                'alert_type' => 'critical_response_time',
                'severity' => 'critical',
                'message' => "Critical response time detected on {$path}",
                'metric_name' => 'response_time',
                'metric_value' => $totalTime,
                'threshold_value' => $criticalThreshold
            ];
        } elseif ($totalTime > $slowThreshold) {
            $alerts[] = [
                'alert_type' => 'slow_response_time',
                'severity' => 'high',
                'message' => "Slow response time detected on {$path}",
                'metric_name' => 'response_time',
                'metric_value' => $totalTime,
                'threshold_value' => $slowThreshold
            ];
        }

        // Check memory usage alerts
        $memoryThreshold = $this->parseMemorySize($this->config->get('HIGH_MEMORY_THRESHOLD', '50MB'));
        if ($memoryUsed > $memoryThreshold) {
            $alerts[] = [
                'alert_type' => 'high_memory_usage',
                'severity' => 'medium',
                'message' => "High memory usage detected on {$path}",
                'metric_name' => 'memory_usage',
                'metric_value' => $memoryUsed,
                'threshold_value' => $memoryThreshold
            ];
        }

        // Store alerts in database
        foreach ($alerts as $alert) {
            try {
                $this->db->insert('performance_alerts', $alert);
            } catch (\Exception $e) {
                $this->logger->error('Failed to store performance alert', [
                    'error' => $e->getMessage(),
                    'alert' => $alert
                ]);
            }
        }
    }

    private function addPerformanceHeaders(float $totalTime, int $memoryUsed, int $peakMemory): void
    {
        header('X-Response-Time: ' . round($totalTime * 1000, 2) . 'ms');
        header('X-Memory-Used: ' . round($memoryUsed / 1024 / 1024, 2) . 'MB');
        header('X-Memory-Peak: ' . round($peakMemory / 1024 / 1024, 2) . 'MB');
        header('X-Query-Count: ' . count($this->db->getQueryLog()));
    }

    private function getConcurrentRequests(): int
    {
        // Simple implementation using cache
        $key = 'concurrent_requests';
        $current = $this->cache->get($key, 0);
        $this->cache->set($key, $current + 1, 60);
        
        // Decrement after request (would need proper cleanup in production)
        register_shutdown_function(function() use ($key) {
            $current = $this->cache->get($key, 1);
            $this->cache->set($key, max(0, $current - 1), 60);
        });
        
        return $current + 1;
    }

    private function normalizePath(string $path): string
    {
        // Replace dynamic segments with placeholders
        $path = preg_replace('/\/\d+/', '/{id}', $path);
        $path = preg_replace('/\/[a-f0-9-]{36}/', '/{uuid}', $path);
        return $path;
    }

    private function getCurrentUserId(): ?int
    {
        // This would typically come from session or authentication service
        return $_SESSION['user_id'] ?? null;
    }

    private function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    private function parseMemorySize(string $size): int
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $value = (int)$size;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
