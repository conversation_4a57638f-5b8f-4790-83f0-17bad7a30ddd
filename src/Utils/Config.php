<?php

declare(strict_types=1);

namespace App\Utils;

class Config
{
    private array $config = [];

    public function __construct()
    {
        $this->loadConfig();
    }

    private function loadConfig(): void
    {
        // Load from environment variables
        $this->config = [
            'DB_HOST' => $_ENV['DB_HOST'] ?? 'localhost',
            'DB_PORT' => (int)($_ENV['DB_PORT'] ?? 3306),
            'DB_NAME' => $_ENV['DB_NAME'] ?? 'seo_indexer',
            'DB_USER' => $_ENV['DB_USER'] ?? 'root',
            'DB_PASS' => $_ENV['DB_PASS'] ?? '',
            'DB_SOCKET' => $_ENV['DB_SOCKET'] ?? '',
            
            'APP_ENV' => $_ENV['APP_ENV'] ?? 'development',
            'APP_DEBUG' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'APP_URL' => $_ENV['APP_URL'] ?? 'http://localhost',
            'APP_NAME' => $_ENV['APP_NAME'] ?? 'SEO Indexer Platform',
            'APP_SECRET_KEY' => $_ENV['APP_SECRET_KEY'] ?? '',
            
            'SESSION_LIFETIME' => (int)($_ENV['SESSION_LIFETIME'] ?? 7200),
            'SESSION_SECURE' => filter_var($_ENV['SESSION_SECURE'] ?? false, FILTER_VALIDATE_BOOLEAN),
            'SESSION_HTTPONLY' => filter_var($_ENV['SESSION_HTTPONLY'] ?? true, FILTER_VALIDATE_BOOLEAN),
            
            'GOOGLE_CLIENT_ID' => $_ENV['GOOGLE_CLIENT_ID'] ?? '',
            'GOOGLE_CLIENT_SECRET' => $_ENV['GOOGLE_CLIENT_SECRET'] ?? '',
            'GOOGLE_REDIRECT_URI' => $_ENV['GOOGLE_REDIRECT_URI'] ?? '',

            'BING_CLIENT_ID' => $_ENV['BING_CLIENT_ID'] ?? '',
            'BING_CLIENT_SECRET' => $_ENV['BING_CLIENT_SECRET'] ?? '',
            'BING_REDIRECT_URI' => $_ENV['BING_REDIRECT_URI'] ?? '',
            'BING_API_KEY' => $_ENV['BING_API_KEY'] ?? '',
            
            'RATE_LIMIT_REQUESTS' => (int)($_ENV['RATE_LIMIT_REQUESTS'] ?? 100),
            'RATE_LIMIT_WINDOW' => (int)($_ENV['RATE_LIMIT_WINDOW'] ?? 3600),
            
            'ENCRYPTION_KEY' => $_ENV['ENCRYPTION_KEY'] ?? '',
            
            'LOG_LEVEL' => $_ENV['LOG_LEVEL'] ?? 'info',
            'LOG_FILE' => $_ENV['LOG_FILE'] ?? 'logs/app.log',
            
            'GOOGLE_DAILY_QUOTA' => (int)($_ENV['GOOGLE_DAILY_QUOTA'] ?? 200),
            'BING_DAILY_QUOTA' => (int)($_ENV['BING_DAILY_QUOTA'] ?? 10000),
        ];
    }

    public function get(string $key, mixed $default = null): mixed
    {
        return $this->config[$key] ?? $default;
    }

    public function set(string $key, mixed $value): void
    {
        $this->config[$key] = $value;
    }

    public function has(string $key): bool
    {
        return isset($this->config[$key]);
    }

    public function all(): array
    {
        return $this->config;
    }
}
