<?php

declare(strict_types=1);

namespace App;

use Dotenv\Dotenv;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use Twig\Environment;
use Twig\Loader\FilesystemLoader;
use App\Services\DatabaseService;
use App\Services\AuthService;
use App\Services\SessionService;
use App\Services\SecurityService;
use App\Utils\Router;
use App\Utils\Config;

class App
{
    private static ?App $instance = null;
    private Logger $logger;
    private Environment $twig;
    private DatabaseService $database;
    private AuthService $auth;
    private SessionService $session;
    private Router $router;
    private Config $config;
    private SecurityService $security;
    private \App\Middleware\SecurityMiddleware $securityMiddleware;

    private function __construct()
    {
        $this->loadEnvironment();
        $this->initializeConfig();
        $this->initializeLogger();
        $this->initializeTemplating();
        $this->initializeDatabase();
        $this->initializeSession();
        $this->initializeAuth();
        $this->initializeSecurity();
        $this->initializeRouter();
    }

    public static function getInstance(): App
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function loadEnvironment(): void
    {
        $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
        $dotenv->load();
    }

    private function initializeConfig(): void
    {
        $this->config = new Config();
    }

    private function initializeLogger(): void
    {
        $this->logger = new Logger('seo_indexer');
        $logFile = $this->config->get('LOG_FILE', 'logs/app.log');
        $logLevel = $this->config->get('LOG_LEVEL', 'info');
        
        $this->logger->pushHandler(new StreamHandler($logFile, $logLevel));
    }

    private function initializeTemplating(): void
    {
        $loader = new FilesystemLoader(__DIR__ . '/../templates');
        $this->twig = new Environment($loader, [
            'cache' => $this->config->get('APP_ENV') === 'production' ? __DIR__ . '/../cache' : false,
            'debug' => $this->config->get('APP_DEBUG', false),
        ]);

        // Add global variables
        $this->twig->addGlobal('app_name', $this->config->get('APP_NAME'));
        $this->twig->addGlobal('app_url', $this->config->get('APP_URL'));

        // Add app context for templates
        $this->twig->addGlobal('app', [
            'user' => null, // Will be set dynamically in BaseController
            'request' => [
                'uri' => $_SERVER['REQUEST_URI'] ?? '/',
                'method' => $_SERVER['REQUEST_METHOD'] ?? 'GET'
            ]
        ]);
    }

    private function initializeDatabase(): void
    {
        $this->database = new DatabaseService($this->config, $this->logger);
    }

    private function initializeSession(): void
    {
        $this->session = new SessionService($this->config);
    }

    private function initializeAuth(): void
    {
        $this->auth = new AuthService($this->database, $this->session, $this->config);
    }

    private function initializeSecurity(): void
    {
        $this->security = new SecurityService($this->database, $this->config, $this->logger);
        $this->securityMiddleware = new \App\Middleware\SecurityMiddleware($this->security, $this->session, $this->config, $this->logger);
    }

    private function initializeRouter(): void
    {
        $this->router = new Router($this);
    }

    public function run(): void
    {
        try {
            // Apply security middleware
            $securityResult = $this->securityMiddleware->handle($_SERVER['REQUEST_METHOD'], $_SERVER['REQUEST_URI']);
            if ($securityResult !== null) {
                http_response_code($securityResult['status']);
                if (isset($securityResult['headers'])) {
                    foreach ($securityResult['headers'] as $header => $value) {
                        header("{$header}: {$value}");
                    }
                }
                echo json_encode(['error' => $securityResult['error']]);
                return;
            }

            $this->router->dispatch();
        } catch (\Exception $e) {
            $this->logger->error('Application error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            if ($this->config->get('APP_DEBUG')) {
                throw $e;
            }
            
            http_response_code(500);
            echo $this->twig->render('errors/500.html.twig', ['error' => $e->getMessage()]);
        }
    }

    // Getters
    public function getLogger(): Logger
    {
        return $this->logger;
    }

    public function getTwig(): Environment
    {
        return $this->twig;
    }

    public function getDatabase(): DatabaseService
    {
        return $this->database;
    }

    public function getAuth(): AuthService
    {
        return $this->auth;
    }

    public function getSession(): SessionService
    {
        return $this->session;
    }

    public function getConfig(): Config
    {
        return $this->config;
    }
}
