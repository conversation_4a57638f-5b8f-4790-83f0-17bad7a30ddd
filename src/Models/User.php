<?php

declare(strict_types=1);

namespace App\Models;

use App\Services\DatabaseService;

class User
{
    private DatabaseService $db;

    public function __construct(DatabaseService $db)
    {
        $this->db = $db;
    }

    public function create(array $data): int
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }

        // Generate email verification token
        if (!isset($data['email_verification_token'])) {
            $data['email_verification_token'] = bin2hex(random_bytes(32));
        }

        // Set default values
        $data['role'] = $data['role'] ?? 'user';
        $data['status'] = $data['status'] ?? 'pending';
        $data['email_verified'] = $data['email_verified'] ?? false;

        return $this->db->insert('users', $data);
    }

    public function findById(int $id): ?array
    {
        return $this->db->fetch('SELECT * FROM users WHERE id = ?', [$id]);
    }

    public function findByEmail(string $email): ?array
    {
        return $this->db->fetch('SELECT * FROM users WHERE email = ?', [$email]);
    }

    public function findByUsername(string $username): ?array
    {
        return $this->db->fetch('SELECT * FROM users WHERE username = ?', [$username]);
    }

    public function findByEmailOrUsername(string $identifier): ?array
    {
        return $this->db->fetch(
            'SELECT * FROM users WHERE email = ? OR username = ?',
            [$identifier, $identifier]
        );
    }

    public function update(int $id, array $data): bool
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }

        return $this->db->update('users', $data, ['id' => $id]) > 0;
    }

    public function delete(int $id): bool
    {
        return $this->db->delete('users', ['id' => $id]) > 0;
    }

    public function verifyPassword(array $user, string $password): bool
    {
        return password_verify($password, $user['password_hash']);
    }

    public function updateLastLogin(int $id): void
    {
        $this->db->update('users', ['last_login' => date('Y-m-d H:i:s')], ['id' => $id]);
    }

    public function verifyEmail(string $token): bool
    {
        $user = $this->db->fetch(
            'SELECT id FROM users WHERE email_verification_token = ? AND email_verified = FALSE',
            [$token]
        );

        if (!$user) {
            return false;
        }

        return $this->db->update('users', [
            'email_verified' => true,
            'email_verification_token' => null,
            'status' => 'active'
        ], ['id' => $user['id']]) > 0;
    }

    public function generatePasswordResetToken(string $email): ?string
    {
        $user = $this->findByEmail($email);
        if (!$user) {
            return null;
        }

        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

        $this->db->update('users', [
            'password_reset_token' => $token,
            'password_reset_expires' => $expires
        ], ['id' => $user['id']]);

        return $token;
    }

    public function resetPassword(string $token, string $newPassword): bool
    {
        $user = $this->db->fetch(
            'SELECT id FROM users WHERE password_reset_token = ? AND password_reset_expires > NOW()',
            [$token]
        );

        if (!$user) {
            return false;
        }

        return $this->db->update('users', [
            'password_hash' => password_hash($newPassword, PASSWORD_DEFAULT),
            'password_reset_token' => null,
            'password_reset_expires' => null
        ], ['id' => $user['id']]) > 0;
    }

    public function getAllUsers(int $page = 1, int $perPage = 20): array
    {
        $sql = 'SELECT id, username, email, first_name, last_name, role, status, email_verified, last_login, created_at FROM users ORDER BY created_at DESC';
        return $this->db->paginate($sql, [], $page, $perPage);
    }

    public function getUserStats(): array
    {
        $stats = [];
        
        $stats['total'] = $this->db->count('users');
        $stats['active'] = $this->db->count('users', ['status' => 'active']);
        $stats['blocked'] = $this->db->count('users', ['status' => 'blocked']);
        $stats['pending'] = $this->db->count('users', ['status' => 'pending']);
        
        // Users registered in last 30 days
        $stats['recent'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
        )['count'] ?? 0;

        return $stats;
    }

    public function blockUser(int $id): bool
    {
        return $this->db->update('users', ['status' => 'blocked'], ['id' => $id]) > 0;
    }

    public function unblockUser(int $id): bool
    {
        return $this->db->update('users', ['status' => 'active'], ['id' => $id]) > 0;
    }

    public function isEmailTaken(string $email, ?int $excludeId = null): bool
    {
        $sql = 'SELECT 1 FROM users WHERE email = ?';
        $params = [$email];

        if ($excludeId) {
            $sql .= ' AND id != ?';
            $params[] = $excludeId;
        }

        return $this->db->fetch($sql, $params) !== null;
    }

    public function isUsernameTaken(string $username, ?int $excludeId = null): bool
    {
        $sql = 'SELECT 1 FROM users WHERE username = ?';
        $params = [$username];

        if ($excludeId) {
            $sql .= ' AND id != ?';
            $params[] = $excludeId;
        }

        return $this->db->fetch($sql, $params) !== null;
    }
}
