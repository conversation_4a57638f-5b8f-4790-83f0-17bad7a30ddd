<?php

declare(strict_types=1);

namespace App\Models;

use App\Services\DatabaseService;

class OAuthToken
{
    private DatabaseService $db;

    public function __construct(DatabaseService $db)
    {
        $this->db = $db;
    }

    public function saveToken(int $userId, string $provider, array $tokenData): bool
    {
        try {
            // Check if token already exists
            $existing = $this->getToken($userId, $provider);
            
            $data = [
                'user_id' => $userId,
                'provider' => $provider,
                'access_token' => $this->encryptToken($tokenData['access_token']),
                'refresh_token' => isset($tokenData['refresh_token']) ? $this->encryptToken($tokenData['refresh_token']) : null,
                'token_type' => 'Bearer',
                'expires_at' => $tokenData['expires_at'] ?? null,
                'scope' => $tokenData['scope'] ?? null
            ];

            // Store user info as JSON if provided
            if (isset($tokenData['user_info'])) {
                $data['user_info'] = json_encode($tokenData['user_info']);
            }

            if ($existing) {
                // Update existing token
                return $this->db->update('oauth_tokens', $data, [
                    'user_id' => $userId,
                    'provider' => $provider
                ]) > 0;
            } else {
                // Insert new token
                return $this->db->insert('oauth_tokens', $data) > 0;
            }

        } catch (\Exception $e) {
            error_log("Failed to save OAuth token: " . $e->getMessage());
            return false;
        }
    }

    public function getToken(int $userId, string $provider): ?array
    {
        $token = $this->db->fetch(
            'SELECT * FROM oauth_tokens WHERE user_id = ? AND provider = ?',
            [$userId, $provider]
        );

        if (!$token) {
            return null;
        }

        // Decrypt tokens
        $token['access_token'] = $this->decryptToken($token['access_token']);
        if ($token['refresh_token']) {
            $token['refresh_token'] = $this->decryptToken($token['refresh_token']);
        }

        return $token;
    }

    public function updateToken(int $userId, string $provider, array $updates): bool
    {
        try {
            // Encrypt tokens if they're being updated
            if (isset($updates['access_token'])) {
                $updates['access_token'] = $this->encryptToken($updates['access_token']);
            }
            if (isset($updates['refresh_token'])) {
                $updates['refresh_token'] = $this->encryptToken($updates['refresh_token']);
            }

            return $this->db->update('oauth_tokens', $updates, [
                'user_id' => $userId,
                'provider' => $provider
            ]) > 0;

        } catch (\Exception $e) {
            error_log("Failed to update OAuth token: " . $e->getMessage());
            return false;
        }
    }

    public function deleteToken(int $userId, string $provider): bool
    {
        return $this->db->delete('oauth_tokens', [
            'user_id' => $userId,
            'provider' => $provider
        ]) > 0;
    }

    public function getUserTokens(int $userId): array
    {
        $tokens = $this->db->fetchAll(
            'SELECT provider, expires_at, scope, created_at, updated_at FROM oauth_tokens WHERE user_id = ?',
            [$userId]
        );

        return $tokens;
    }

    public function isTokenExpired(int $userId, string $provider): bool
    {
        $token = $this->db->fetch(
            'SELECT expires_at FROM oauth_tokens WHERE user_id = ? AND provider = ?',
            [$userId, $provider]
        );

        if (!$token || !$token['expires_at']) {
            return false; // No expiration set
        }

        return strtotime($token['expires_at']) <= time();
    }

    public function getExpiredTokens(): array
    {
        return $this->db->fetchAll(
            'SELECT user_id, provider FROM oauth_tokens WHERE expires_at IS NOT NULL AND expires_at <= NOW()'
        );
    }

    public function cleanupExpiredTokens(): int
    {
        // Only cleanup tokens that are expired and have no refresh token
        return $this->db->query(
            'DELETE FROM oauth_tokens WHERE expires_at IS NOT NULL AND expires_at <= NOW() AND refresh_token IS NULL'
        )->rowCount();
    }

    public function getTokenStats(): array
    {
        $stats = [];

        // Total tokens by provider
        $providerStats = $this->db->fetchAll(
            'SELECT provider, COUNT(*) as count FROM oauth_tokens GROUP BY provider'
        );

        foreach ($providerStats as $stat) {
            $stats['by_provider'][$stat['provider']] = (int)$stat['count'];
        }

        // Total active tokens (not expired)
        $stats['active'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM oauth_tokens WHERE expires_at IS NULL OR expires_at > NOW()'
        )['count'] ?? 0;

        // Total expired tokens
        $stats['expired'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM oauth_tokens WHERE expires_at IS NOT NULL AND expires_at <= NOW()'
        )['count'] ?? 0;

        // Tokens created in last 30 days
        $stats['recent'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM oauth_tokens WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
        )['count'] ?? 0;

        return $stats;
    }

    private function encryptToken(string $token): string
    {
        $key = $_ENV['ENCRYPTION_KEY'] ?? 'default-key-change-in-production';
        
        // Use a simple encryption method (in production, use proper encryption)
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($token, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }

    private function decryptToken(string $encryptedToken): string
    {
        $key = $_ENV['ENCRYPTION_KEY'] ?? 'default-key-change-in-production';
        
        $data = base64_decode($encryptedToken);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }

    public function hasValidToken(int $userId, string $provider): bool
    {
        $token = $this->db->fetch(
            'SELECT expires_at FROM oauth_tokens WHERE user_id = ? AND provider = ?',
            [$userId, $provider]
        );

        if (!$token) {
            return false;
        }

        // If no expiration date, consider it valid
        if (!$token['expires_at']) {
            return true;
        }

        // Check if token is not expired
        return strtotime($token['expires_at']) > time();
    }

    public function getConnectedProviders(int $userId): array
    {
        $tokens = $this->db->fetchAll(
            'SELECT provider, expires_at FROM oauth_tokens WHERE user_id = ?',
            [$userId]
        );

        $connected = [];
        foreach ($tokens as $token) {
            $isValid = !$token['expires_at'] || strtotime($token['expires_at']) > time();
            $connected[$token['provider']] = $isValid;
        }

        return $connected;
    }
}
