<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\DatabaseService;
use App\Utils\Config;
use Monolog\Logger;

class SecurityService
{
    private DatabaseService $db;
    private Config $config;
    private Logger $logger;

    public function __construct(DatabaseService $db, Config $config, Logger $logger)
    {
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
    }

    public function checkRateLimit(string $identifier, string $action, int $maxAttempts = 60, int $windowMinutes = 60): array
    {
        $windowStart = date('Y-m-d H:i:s', time() - ($windowMinutes * 60));
        
        // Clean up old rate limit entries
        $this->cleanupOldRateLimits($windowStart);
        
        // Count current attempts
        $result = $this->db->fetch(
            'SELECT COUNT(*) as count FROM rate_limits WHERE identifier = ? AND action = ? AND created_at >= ?',
            [$identifier, $action, $windowStart]
        );
        $currentAttempts = (int)($result['count'] ?? 0);
        
        $isAllowed = $currentAttempts < $maxAttempts;
        $remainingAttempts = max(0, $maxAttempts - $currentAttempts);
        $resetTime = time() + ($windowMinutes * 60);
        
        if (!$isAllowed) {
            $this->logger->warning('Rate limit exceeded', [
                'identifier' => $identifier,
                'action' => $action,
                'attempts' => $currentAttempts,
                'max_attempts' => $maxAttempts
            ]);
        }
        
        return [
            'allowed' => $isAllowed,
            'remaining' => $remainingAttempts,
            'reset_time' => $resetTime,
            'current_attempts' => $currentAttempts,
            'max_attempts' => $maxAttempts
        ];
    }

    public function recordAttempt(string $identifier, string $action, array $metadata = []): void
    {
        $this->db->insert('rate_limits', [
            'identifier' => $identifier,
            'action' => $action,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'metadata' => json_encode($metadata)
        ]);
    }

    public function checkBruteForce(string $identifier, string $action, int $maxAttempts = 5, int $windowMinutes = 15): array
    {
        return $this->checkRateLimit($identifier, $action, $maxAttempts, $windowMinutes);
    }

    public function blockIp(string $ipAddress, string $reason, int $durationMinutes = 60): void
    {
        $expiresAt = date('Y-m-d H:i:s', time() + ($durationMinutes * 60));
        
        $this->db->insert('blocked_ips', [
            'ip_address' => $ipAddress,
            'reason' => $reason,
            'expires_at' => $expiresAt
        ]);
        
        $this->logger->warning('IP address blocked', [
            'ip_address' => $ipAddress,
            'reason' => $reason,
            'expires_at' => $expiresAt
        ]);
    }

    public function isIpBlocked(string $ipAddress): bool
    {
        $blocked = $this->db->fetch(
            'SELECT id FROM blocked_ips WHERE ip_address = ? AND (expires_at IS NULL OR expires_at > NOW())',
            [$ipAddress]
        );
        
        return $blocked !== null;
    }

    public function validateInput(array $data, array $rules): array
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            
            // Required validation
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = $rule['message'] ?? "{$field} is required";
                continue;
            }
            
            // Skip further validation if field is empty and not required
            if (empty($value) && !($rule['required'] ?? false)) {
                continue;
            }
            
            // Type validation
            if (isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field] = $rule['message'] ?? "Invalid {$field} format";
                        }
                        break;
                    case 'url':
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $errors[$field] = $rule['message'] ?? "Invalid {$field} format";
                        }
                        break;
                    case 'int':
                        if (!filter_var($value, FILTER_VALIDATE_INT)) {
                            $errors[$field] = $rule['message'] ?? "{$field} must be a number";
                        }
                        break;
                    case 'float':
                        if (!filter_var($value, FILTER_VALIDATE_FLOAT)) {
                            $errors[$field] = $rule['message'] ?? "{$field} must be a decimal number";
                        }
                        break;
                    case 'boolean':
                        if (!filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)) {
                            $errors[$field] = $rule['message'] ?? "{$field} must be true or false";
                        }
                        break;
                }
            }
            
            // Length validation
            if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                $errors[$field] = $rule['message'] ?? "{$field} must be at least {$rule['min_length']} characters";
            }
            
            if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                $errors[$field] = $rule['message'] ?? "{$field} must be no more than {$rule['max_length']} characters";
            }
            
            // Pattern validation
            if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
                $errors[$field] = $rule['message'] ?? "{$field} format is invalid";
            }
            
            // Custom validation
            if (isset($rule['custom']) && is_callable($rule['custom'])) {
                $customResult = $rule['custom']($value);
                if ($customResult !== true) {
                    $errors[$field] = is_string($customResult) ? $customResult : ($rule['message'] ?? "{$field} is invalid");
                }
            }
        }
        
        return $errors;
    }

    public function sanitizeInput(string $input, string $type = 'string'): string
    {
        switch ($type) {
            case 'html':
                return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            case 'url':
                return filter_var($input, FILTER_SANITIZE_URL);
            case 'email':
                return filter_var($input, FILTER_SANITIZE_EMAIL);
            case 'int':
                return (string)filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            case 'float':
                return (string)filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            case 'string':
            default:
                return trim(strip_tags($input));
        }
    }

    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);
    }

    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    public function checkPasswordStrength(string $password): array
    {
        $score = 0;
        $feedback = [];
        
        // Length check
        if (strlen($password) >= 8) {
            $score += 1;
        } else {
            $feedback[] = 'Password should be at least 8 characters long';
        }
        
        // Uppercase check
        if (preg_match('/[A-Z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one uppercase letter';
        }
        
        // Lowercase check
        if (preg_match('/[a-z]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one lowercase letter';
        }
        
        // Number check
        if (preg_match('/[0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one number';
        }
        
        // Special character check
        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 1;
        } else {
            $feedback[] = 'Password should contain at least one special character';
        }
        
        // Common password check
        if ($this->isCommonPassword($password)) {
            $score -= 2;
            $feedback[] = 'Password is too common, please choose a more unique password';
        }
        
        $strength = 'weak';
        if ($score >= 4) {
            $strength = 'strong';
        } elseif ($score >= 3) {
            $strength = 'medium';
        }
        
        return [
            'score' => max(0, $score),
            'strength' => $strength,
            'feedback' => $feedback
        ];
    }

    public function logSecurityEvent(string $event, array $data = []): void
    {
        $this->db->insert('security_logs', [
            'event_type' => $event,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'user_id' => $data['user_id'] ?? null,
            'event_data' => json_encode($data),
            'severity' => $data['severity'] ?? 'info'
        ]);
        
        $this->logger->info('Security event logged', [
            'event' => $event,
            'data' => $data
        ]);
    }

    private function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    private function cleanupOldRateLimits(string $cutoffTime): void
    {
        $this->db->query('DELETE FROM rate_limits WHERE created_at < ?', [$cutoffTime]);
        $this->db->query('DELETE FROM blocked_ips WHERE expires_at IS NOT NULL AND expires_at < NOW()');
    }

    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
            'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',
            'qwerty123', 'admin123', 'root', 'toor', 'pass', 'test', 'guest'
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }
}
