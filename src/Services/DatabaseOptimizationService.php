<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\DatabaseService;
use App\Services\CacheService;
use App\Utils\Config;
use Monolog\Logger;

class DatabaseOptimizationService
{
    private DatabaseService $db;
    private CacheService $cache;
    private Config $config;
    private Logger $logger;
    private array $queryCache = [];
    private array $preparedStatements = [];

    public function __construct(DatabaseService $db, CacheService $cache, Config $config, Logger $logger)
    {
        $this->db = $db;
        $this->cache = $cache;
        $this->config = $config;
        $this->logger = $logger;
    }

    public function optimizeDatabase(): array
    {
        $results = [];
        
        // Analyze table performance
        $results['table_analysis'] = $this->analyzeTablePerformance();
        
        // Optimize tables
        $results['table_optimization'] = $this->optimizeTables();
        
        // Update table statistics
        $results['statistics_update'] = $this->updateTableStatistics();
        
        // Check and suggest indexes
        $results['index_suggestions'] = $this->suggestIndexes();
        
        // Clean up old data
        $results['data_cleanup'] = $this->cleanupOldData();
        
        $this->logger->info('Database optimization completed', $results);
        
        return $results;
    }

    public function getCachedQuery(string $sql, array $params = [], int $ttl = 300)
    {
        $cacheKey = 'query:' . md5($sql . serialize($params));
        
        return $this->cache->remember($cacheKey, function() use ($sql, $params) {
            return $this->db->fetchAll($sql, $params);
        }, $ttl);
    }

    public function getCachedCount(string $table, array $conditions = [], int $ttl = 300): int
    {
        $cacheKey = 'count:' . $table . ':' . md5(serialize($conditions));
        
        return $this->cache->remember($cacheKey, function() use ($table, $conditions) {
            return $this->db->count($table, $conditions);
        }, $ttl);
    }

    public function invalidateQueryCache(string $table): void
    {
        $this->cache->invalidateTag("table:{$table}");
    }

    public function batchInsert(string $table, array $data, int $batchSize = 1000): int
    {
        if (empty($data)) {
            return 0;
        }

        $totalInserted = 0;
        $chunks = array_chunk($data, $batchSize);
        
        foreach ($chunks as $chunk) {
            $columns = array_keys($chunk[0]);
            $placeholders = '(' . implode(',', array_fill(0, count($columns), '?')) . ')';
            $values = implode(',', array_fill(0, count($chunk), $placeholders));
            
            $sql = "INSERT INTO {$table} (" . implode(',', $columns) . ") VALUES {$values}";
            
            $params = [];
            foreach ($chunk as $row) {
                foreach ($columns as $column) {
                    $params[] = $row[$column];
                }
            }
            
            $stmt = $this->db->prepare($sql);
            if ($stmt->execute($params)) {
                $totalInserted += count($chunk);
            }
        }
        
        // Invalidate related cache
        $this->invalidateQueryCache($table);
        
        return $totalInserted;
    }

    public function batchUpdate(string $table, array $updates, string $keyColumn = 'id'): int
    {
        if (empty($updates)) {
            return 0;
        }

        $updated = 0;
        
        foreach ($updates as $data) {
            if (!isset($data[$keyColumn])) {
                continue;
            }
            
            $keyValue = $data[$keyColumn];
            unset($data[$keyColumn]);
            
            if ($this->db->update($table, $data, [$keyColumn => $keyValue])) {
                $updated++;
            }
        }
        
        // Invalidate related cache
        $this->invalidateQueryCache($table);
        
        return $updated;
    }

    public function getSlowQueries(int $limit = 10): array
    {
        $sql = "
            SELECT 
                query_time,
                lock_time,
                rows_sent,
                rows_examined,
                sql_text
            FROM mysql.slow_log 
            ORDER BY query_time DESC 
            LIMIT ?
        ";
        
        try {
            return $this->db->fetchAll($sql, [$limit]);
        } catch (\Exception $e) {
            $this->logger->warning('Could not fetch slow queries', ['error' => $e->getMessage()]);
            return [];
        }
    }

    public function getTableSizes(): array
    {
        $sql = "
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                table_rows,
                ROUND((data_length / 1024 / 1024), 2) AS data_mb,
                ROUND((index_length / 1024 / 1024), 2) AS index_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
        ";
        
        return $this->getCachedQuery($sql, [], 3600); // Cache for 1 hour
    }

    public function getIndexUsage(): array
    {
        $sql = "
            SELECT 
                t.table_name,
                s.index_name,
                s.column_name,
                s.cardinality,
                IFNULL(u.count_read, 0) as reads,
                IFNULL(u.count_write, 0) as writes
            FROM information_schema.statistics s
            LEFT JOIN performance_schema.table_io_waits_summary_by_index_usage u 
                ON s.table_schema = u.object_schema 
                AND s.table_name = u.object_name 
                AND s.index_name = u.index_name
            JOIN information_schema.tables t 
                ON s.table_schema = t.table_schema 
                AND s.table_name = t.table_name
            WHERE s.table_schema = DATABASE()
            ORDER BY t.table_name, s.index_name, s.seq_in_index
        ";
        
        return $this->getCachedQuery($sql, [], 3600);
    }

    private function analyzeTablePerformance(): array
    {
        $tables = ['users', 'domains', 'url_submissions', 'oauth_tokens', 'security_logs'];
        $analysis = [];
        
        foreach ($tables as $table) {
            $sql = "ANALYZE TABLE {$table}";
            try {
                $result = $this->db->query($sql);
                $analysis[$table] = 'analyzed';
            } catch (\Exception $e) {
                $analysis[$table] = 'error: ' . $e->getMessage();
            }
        }
        
        return $analysis;
    }

    private function optimizeTables(): array
    {
        $tables = ['users', 'domains', 'url_submissions', 'oauth_tokens', 'security_logs'];
        $optimization = [];
        
        foreach ($tables as $table) {
            $sql = "OPTIMIZE TABLE {$table}";
            try {
                $result = $this->db->query($sql);
                $optimization[$table] = 'optimized';
            } catch (\Exception $e) {
                $optimization[$table] = 'error: ' . $e->getMessage();
            }
        }
        
        return $optimization;
    }

    private function updateTableStatistics(): array
    {
        $sql = "ANALYZE TABLE " . implode(', ', [
            'users', 'domains', 'url_submissions', 'oauth_tokens', 
            'security_logs', 'rate_limits', 'blocked_ips'
        ]);
        
        try {
            $this->db->query($sql);
            return ['status' => 'updated'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    private function suggestIndexes(): array
    {
        $suggestions = [];
        
        // Check for missing indexes on foreign keys
        $foreignKeys = [
            'domains' => ['user_id'],
            'url_submissions' => ['user_id', 'domain_id'],
            'oauth_tokens' => ['user_id'],
            'security_logs' => ['user_id'],
            'rate_limits' => ['identifier'],
            'login_attempts' => ['identifier', 'ip_address']
        ];
        
        foreach ($foreignKeys as $table => $columns) {
            foreach ($columns as $column) {
                if (!$this->hasIndex($table, $column)) {
                    $suggestions[] = "CREATE INDEX idx_{$table}_{$column} ON {$table}({$column})";
                }
            }
        }
        
        // Check for composite indexes
        $compositeIndexes = [
            'url_submissions' => ['user_id', 'status', 'created_at'],
            'security_logs' => ['event_type', 'created_at'],
            'rate_limits' => ['identifier', 'action', 'created_at']
        ];
        
        foreach ($compositeIndexes as $table => $columns) {
            $indexName = 'idx_' . $table . '_' . implode('_', $columns);
            if (!$this->hasIndex($table, $indexName)) {
                $columnList = implode(', ', $columns);
                $suggestions[] = "CREATE INDEX {$indexName} ON {$table}({$columnList})";
            }
        }
        
        return $suggestions;
    }

    private function hasIndex(string $table, string $columnOrIndexName): bool
    {
        $sql = "
            SELECT COUNT(*) as count
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = ? 
            AND (column_name = ? OR index_name = ?)
        ";
        
        $result = $this->db->fetch($sql, [$table, $columnOrIndexName, $columnOrIndexName]);
        return $result['count'] > 0;
    }

    private function cleanupOldData(): array
    {
        $cleaned = [];
        
        // Clean old rate limits (older than 24 hours)
        $sql = "DELETE FROM rate_limits WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        $cleaned['rate_limits'] = $this->db->query($sql)->rowCount();
        
        // Clean expired blocked IPs
        $sql = "DELETE FROM blocked_ips WHERE expires_at IS NOT NULL AND expires_at < NOW()";
        $cleaned['blocked_ips'] = $this->db->query($sql)->rowCount();
        
        // Clean old security logs (older than 90 days)
        $sql = "DELETE FROM security_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)";
        $cleaned['security_logs'] = $this->db->query($sql)->rowCount();
        
        // Clean old login attempts (older than 30 days)
        $sql = "DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $cleaned['login_attempts'] = $this->db->query($sql)->rowCount();
        
        // Clean expired password reset tokens
        $sql = "DELETE FROM password_reset_tokens WHERE expires_at < NOW()";
        $cleaned['password_reset_tokens'] = $this->db->query($sql)->rowCount();
        
        return $cleaned;
    }

    public function getConnectionPoolStats(): array
    {
        $sql = "SHOW STATUS LIKE 'Threads_%'";
        $threads = $this->db->fetchAll($sql);
        
        $sql = "SHOW STATUS LIKE 'Connections'";
        $connections = $this->db->fetch($sql);
        
        $sql = "SHOW STATUS LIKE 'Max_used_connections'";
        $maxConnections = $this->db->fetch($sql);
        
        return [
            'threads' => $threads,
            'total_connections' => $connections['Value'] ?? 0,
            'max_used_connections' => $maxConnections['Value'] ?? 0
        ];
    }

    public function enableQueryCache(): bool
    {
        try {
            $this->db->query("SET GLOBAL query_cache_type = ON");
            $this->db->query("SET GLOBAL query_cache_size = 67108864"); // 64MB
            return true;
        } catch (\Exception $e) {
            $this->logger->error('Failed to enable query cache', ['error' => $e->getMessage()]);
            return false;
        }
    }
}
