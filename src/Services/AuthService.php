<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Utils\Config;
use Respect\Validation\Validator as v;

class AuthService
{
    private DatabaseService $db;
    private SessionService $session;
    private Config $config;
    private User $userModel;

    public function __construct(DatabaseService $db, SessionService $session, Config $config)
    {
        $this->db = $db;
        $this->session = $session;
        $this->config = $config;
        $this->userModel = new User($db);
    }

    public function register(array $data): array
    {
        $errors = $this->validateRegistration($data);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        try {
            $userId = $this->userModel->create([
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => $data['password'],
                'first_name' => $data['first_name'] ?? '',
                'last_name' => $data['last_name'] ?? ''
            ]);

            // TODO: Send email verification email

            return [
                'success' => true,
                'user_id' => $userId,
                'message' => 'Registration successful. Please check your email to verify your account.'
            ];
        } catch (\PDOException $e) {
            // Handle database constraint violations (duplicate username/email)
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                if (strpos($e->getMessage(), 'username') !== false) {
                    return [
                        'success' => false,
                        'errors' => ['username' => 'Username is already taken']
                    ];
                } elseif (strpos($e->getMessage(), 'email') !== false) {
                    return [
                        'success' => false,
                        'errors' => ['email' => 'Email is already registered']
                    ];
                }
            }

            // Log the actual error for debugging
            error_log('Registration database error: ' . $e->getMessage());

            return [
                'success' => false,
                'errors' => ['general' => 'Registration failed due to a database error. Please try again.']
            ];
        } catch (\Exception $e) {
            // Log the actual error for debugging
            error_log('Registration error: ' . $e->getMessage());

            return [
                'success' => false,
                'errors' => ['general' => 'Registration failed. Please try again.']
            ];
        }
    }

    public function login(string $identifier, string $password, bool $remember = false): array
    {
        $clientIp = $this->getClientIp();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $errors = [];

        // Validate input
        if (empty($identifier)) {
            $errors['identifier'] = 'Email or username is required';
        }
        if (empty($password)) {
            $errors['password'] = 'Password is required';
        }

        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        // Check for account lockout
        $user = $this->userModel->findByEmailOrUsername($identifier);
        if ($user && $this->isAccountLocked($user)) {
            $this->logLoginAttempt($identifier, $clientIp, false, 'account_locked');
            return [
                'success' => false,
                'errors' => ['general' => 'Account is temporarily locked due to too many failed login attempts']
            ];
        }

        // Check brute force protection
        $bruteForceCheck = $this->checkBruteForce($identifier, $clientIp);
        if (!$bruteForceCheck['allowed']) {
            $this->logLoginAttempt($identifier, $clientIp, false, 'brute_force_blocked');
            return [
                'success' => false,
                'errors' => ['general' => 'Too many failed login attempts. Please try again later.']
            ];
        }

        if (!$user || !$this->userModel->verifyPassword($user, $password)) {
            $this->handleFailedLogin($identifier, $user, $clientIp);
            return [
                'success' => false,
                'errors' => ['general' => 'Invalid credentials']
            ];
        }

        // Check user status
        if ($user['status'] === 'blocked') {
            $this->logLoginAttempt($identifier, $clientIp, false, 'blocked_account');
            return [
                'success' => false,
                'errors' => ['general' => 'Your account has been blocked. Please contact support.']
            ];
        }

        if ($user['status'] === 'pending' && !$user['email_verified']) {
            $this->logLoginAttempt($identifier, $clientIp, false, 'unverified_email');
            return [
                'success' => false,
                'errors' => ['general' => 'Please verify your email address before logging in.']
            ];
        }

        // Successful login - reset failed attempts
        $this->resetFailedLoginAttempts($user['id']);

        // Login successful
        $this->userModel->updateLastLogin($user['id']);
        $this->session->setUser($user);

        // TODO: Handle remember me functionality
        
        return [
            'success' => true,
            'user' => $this->sanitizeUser($user),
            'message' => 'Login successful'
        ];
    }

    public function logout(): void
    {
        $this->session->logout();
    }

    public function getCurrentUser(): ?array
    {
        $user = $this->session->getUser();
        return $user ? $this->sanitizeUser($user) : null;
    }

    public function isLoggedIn(): bool
    {
        return $this->session->isLoggedIn();
    }

    public function isAdmin(): bool
    {
        return $this->session->isAdmin();
    }

    public function requireAuth(): bool
    {
        if (!$this->isLoggedIn()) {
            header('Location: /login');
            exit;
        }
        return true;
    }

    public function requireAdmin(): bool
    {
        $this->requireAuth();
        
        if (!$this->isAdmin()) {
            http_response_code(403);
            echo 'Access denied';
            exit;
        }
        return true;
    }

    public function verifyEmail(string $token): bool
    {
        return $this->userModel->verifyEmail($token);
    }

    public function requestPasswordReset(string $email): array
    {
        if (!v::email()->validate($email)) {
            return [
                'success' => false,
                'errors' => ['email' => 'Invalid email address']
            ];
        }

        $token = $this->userModel->generatePasswordResetToken($email);
        
        if (!$token) {
            // Don't reveal if email exists or not for security
            return [
                'success' => true,
                'message' => 'If the email exists, a password reset link has been sent.'
            ];
        }

        // TODO: Send password reset email
        
        return [
            'success' => true,
            'message' => 'Password reset link has been sent to your email.'
        ];
    }

    public function resetPassword(string $token, string $newPassword): array
    {
        $errors = $this->validatePassword($newPassword);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        if ($this->userModel->resetPassword($token, $newPassword)) {
            return [
                'success' => true,
                'message' => 'Password has been reset successfully.'
            ];
        }

        return [
            'success' => false,
            'errors' => ['general' => 'Invalid or expired reset token.']
        ];
    }

    public function changePassword(int $userId, string $currentPassword, string $newPassword): array
    {
        $user = $this->userModel->findById($userId);
        
        if (!$user || !$this->userModel->verifyPassword($user, $currentPassword)) {
            return [
                'success' => false,
                'errors' => ['current_password' => 'Current password is incorrect']
            ];
        }

        $errors = $this->validatePassword($newPassword);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }

        if ($this->userModel->update($userId, ['password' => $newPassword])) {
            return [
                'success' => true,
                'message' => 'Password changed successfully.'
            ];
        }

        return [
            'success' => false,
            'errors' => ['general' => 'Failed to change password.']
        ];
    }

    private function validateRegistration(array $data): array
    {
        $errors = [];

        // Username validation
        if (empty($data['username'])) {
            $errors['username'] = 'Username is required';
        } elseif (!v::alnum('_')->length(3, 50)->validate($data['username'])) {
            $errors['username'] = 'Username must be 3-50 characters and contain only letters, numbers, and underscores';
        } elseif ($this->userModel->isUsernameTaken($data['username'])) {
            $errors['username'] = 'Username is already taken';
        }

        // Email validation
        if (empty($data['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!v::email()->validate($data['email'])) {
            $errors['email'] = 'Invalid email address';
        } elseif ($this->userModel->isEmailTaken($data['email'])) {
            $errors['email'] = 'Email is already registered';
        }

        // Password validation
        $passwordErrors = $this->validatePassword($data['password'] ?? '');
        if (!empty($passwordErrors)) {
            $errors = array_merge($errors, $passwordErrors);
        }

        // Confirm password
        if (($data['password'] ?? '') !== ($data['confirm_password'] ?? '')) {
            $errors['confirm_password'] = 'Passwords do not match';
        }

        return $errors;
    }

    private function validatePassword(string $password): array
    {
        $errors = [];

        if (empty($password)) {
            $errors['password'] = 'Password is required';
        } elseif (strlen($password) < 8) {
            $errors['password'] = 'Password must be at least 8 characters long';
        } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            $errors['password'] = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
        }

        return $errors;
    }

    private function sanitizeUser(array $user): array
    {
        unset($user['password_hash'], $user['password_reset_token'], $user['email_verification_token']);
        return $user;
    }

    private function isAccountLocked(array $user): bool
    {
        if (empty($user['locked_until'])) {
            return false;
        }

        return strtotime($user['locked_until']) > time();
    }

    private function checkBruteForce(string $identifier, string $clientIp): array
    {
        // Check failed attempts from this IP in the last 15 minutes
        $result = $this->db->fetch(
            'SELECT COUNT(*) as count FROM login_attempts WHERE ip_address = ? AND success = ? AND created_at >= ?',
            [$clientIp, false, date('Y-m-d H:i:s', time() - 900)]
        );
        $recentAttempts = (int)($result['count'] ?? 0);

        $maxAttempts = 5;
        return [
            'allowed' => $recentAttempts < $maxAttempts,
            'remaining' => max(0, $maxAttempts - $recentAttempts),
            'reset_time' => time() + 900
        ];
    }

    private function handleFailedLogin(string $identifier, ?array $user, string $clientIp): void
    {
        // Log the failed attempt
        $this->logLoginAttempt($identifier, $clientIp, false, 'invalid_credentials');

        if ($user) {
            // Increment failed login attempts
            $failedAttempts = ($user['failed_login_attempts'] ?? 0) + 1;

            $updateData = ['failed_login_attempts' => $failedAttempts];

            // Lock account after 5 failed attempts
            if ($failedAttempts >= 5) {
                $updateData['locked_until'] = date('Y-m-d H:i:s', time() + 1800); // 30 minutes
            }

            $this->db->update('users', $updateData, ['id' => $user['id']]);
        }
    }

    private function resetFailedLoginAttempts(int $userId): void
    {
        $this->db->update('users', [
            'failed_login_attempts' => 0,
            'locked_until' => null
        ], ['id' => $userId]);
    }

    private function updateLastLogin(int $userId, string $clientIp): void
    {
        $this->db->update('users', [
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $clientIp
        ], ['id' => $userId]);
    }

    private function logLoginAttempt(string $identifier, string $clientIp, bool $success, string $reason = ''): void
    {
        $this->db->insert('login_attempts', [
            'identifier' => $identifier,
            'ip_address' => $clientIp,
            'success' => $success,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }

    private function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
