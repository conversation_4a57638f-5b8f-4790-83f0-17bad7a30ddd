<?php

declare(strict_types=1);

namespace App\Services;

use PDO;
use PDOException;
use Monolog\Logger;
use App\Utils\Config;

class DatabaseService
{
    private PDO $pdo;
    private Logger $logger;
    private Config $config;
    private ?PerformanceMonitoringService $performanceMonitor = null;
    private array $queryLog = [];
    private bool $enableQueryLogging;

    public function __construct(Config $config, Logger $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->enableQueryLogging = $config->get('DB_ENABLE_QUERY_LOGGING', false);
        $this->connect();
    }

    public function setPerformanceMonitor(PerformanceMonitoringService $monitor): void
    {
        $this->performanceMonitor = $monitor;
    }

    private function connect(): void
    {
        $host = $this->config->get('DB_HOST');
        $port = $this->config->get('DB_PORT');
        $dbname = $this->config->get('DB_NAME');
        $username = $this->config->get('DB_USER');
        $password = $this->config->get('DB_PASS');
        $socket = $this->config->get('DB_SOCKET', '');

        if (!empty($socket) && file_exists($socket)) {
            $dsn = "mysql:unix_socket={$socket};dbname={$dbname};charset=utf8mb4";
        } else {
            $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
        }

        try {
            $this->pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);

            $this->logger->info('Database connection established');
        } catch (PDOException $e) {
            $this->logger->error('Database connection failed', ['error' => $e->getMessage()]);
            throw new \RuntimeException('Database connection failed: ' . $e->getMessage());
        }
    }

    public function getPdo(): PDO
    {
        return $this->pdo;
    }

    public function query(string $sql, array $params = []): \PDOStatement
    {
        $startTime = microtime(true);

        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);

            $executionTime = microtime(true) - $startTime;
            $rowCount = $stmt->rowCount();

            // Log query performance
            if ($this->enableQueryLogging) {
                $this->logQuery($sql, $params, $executionTime, $rowCount);
            }

            // Record performance metrics
            if ($this->performanceMonitor) {
                $this->performanceMonitor->recordDatabaseQuery($sql, $executionTime, $rowCount);
            }

            $this->logger->debug('Database query executed', [
                'sql' => $sql,
                'params' => $params,
                'execution_time' => $executionTime,
                'row_count' => $rowCount
            ]);

            return $stmt;
        } catch (PDOException $e) {
            $executionTime = microtime(true) - $startTime;

            $this->logger->error('Database query failed', [
                'sql' => $sql,
                'params' => $params,
                'execution_time' => $executionTime,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function fetch(string $sql, array $params = []): ?array
    {
        $stmt = $this->query($sql, $params);
        $result = $stmt->fetch();
        return $result ?: null;
    }

    public function fetchAll(string $sql, array $params = []): array
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_map(fn($col) => ":{$col}", $columns);
        
        $sql = "INSERT INTO {$table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $this->query($sql, $data);
        return (int)$this->pdo->lastInsertId();
    }

    public function update(string $table, array $data, array $where): int
    {
        $setClause = array_map(fn($col) => "{$col} = :{$col}", array_keys($data));
        $whereClause = array_map(fn($col) => "{$col} = :where_{$col}", array_keys($where));
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE " . implode(' AND ', $whereClause);
        
        // Prefix where parameters to avoid conflicts
        $whereParams = [];
        foreach ($where as $key => $value) {
            $whereParams["where_{$key}"] = $value;
        }
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->query($sql, $params);
        
        return $stmt->rowCount();
    }

    public function delete(string $table, array $where): int
    {
        $whereClause = array_map(fn($col) => "{$col} = :{$col}", array_keys($where));
        $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
        
        $stmt = $this->query($sql, $where);
        return $stmt->rowCount();
    }

    public function exists(string $table, array $where): bool
    {
        $whereClause = array_map(fn($col) => "{$col} = :{$col}", array_keys($where));
        $sql = "SELECT 1 FROM {$table} WHERE " . implode(' AND ', $whereClause) . " LIMIT 1";
        
        $result = $this->fetch($sql, $where);
        return $result !== null;
    }

    public function count(string $table, array $where = []): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$table}";
        
        if (!empty($where)) {
            $whereClause = array_map(fn($col) => "{$col} = :{$col}", array_keys($where));
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $result = $this->fetch($sql, $where);
        return (int)($result['count'] ?? 0);
    }

    public function beginTransaction(): bool
    {
        return $this->pdo->beginTransaction();
    }

    public function commit(): bool
    {
        return $this->pdo->commit();
    }

    public function rollback(): bool
    {
        return $this->pdo->rollback();
    }

    public function inTransaction(): bool
    {
        return $this->pdo->inTransaction();
    }

    public function transaction(callable $callback): mixed
    {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    public function paginate(string $sql, array $params, int $page = 1, int $perPage = 20): array
    {
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM ({$sql}) as count_query";
        $totalResult = $this->fetch($countSql, $params);
        $total = (int)($totalResult['total'] ?? 0);
        
        // Get paginated results
        $paginatedSql = "{$sql} LIMIT {$perPage} OFFSET {$offset}";
        $data = $this->fetchAll($paginatedSql, $params);
        
        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => (int)ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }

    private function logQuery(string $sql, array $params, float $executionTime, int $rowCount): void
    {
        $queryHash = md5($sql);

        $this->queryLog[] = [
            'query_hash' => $queryHash,
            'query_text' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'row_count' => $rowCount,
            'timestamp' => microtime(true)
        ];

        // Log slow queries to database if performance monitor is available
        if ($this->performanceMonitor && $executionTime > 1.0) {
            try {
                $stmt = $this->pdo->prepare("
                    INSERT INTO slow_queries (query_hash, query_text, execution_time, rows_sent, database_name)
                    VALUES (?, ?, ?, ?, DATABASE())
                ");
                $stmt->execute([$queryHash, $sql, $executionTime, $rowCount]);
            } catch (\Exception $e) {
                // Ignore errors when logging slow queries to avoid recursion
            }
        }
    }

    public function getQueryLog(): array
    {
        return $this->queryLog;
    }

    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }

    public function getQueryStats(): array
    {
        if (empty($this->queryLog)) {
            return ['total_queries' => 0];
        }

        $totalTime = array_sum(array_column($this->queryLog, 'execution_time'));
        $executionTimes = array_column($this->queryLog, 'execution_time');

        return [
            'total_queries' => count($this->queryLog),
            'total_execution_time' => $totalTime,
            'average_execution_time' => $totalTime / count($this->queryLog),
            'slowest_query_time' => max($executionTimes),
            'fastest_query_time' => min($executionTimes),
            'slow_queries_count' => count(array_filter($executionTimes, fn($time) => $time > 1.0))
        ];
    }
}
