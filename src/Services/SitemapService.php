<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\GoogleSearchConsoleService;
use App\Services\BingWebmasterService;
use App\Services\UrlSubmissionService;
use App\Services\DatabaseService;
use App\Utils\Config;
use Monolog\Logger;
use GuzzleHttp\Client;

class SitemapService
{
    private GoogleSearchConsoleService $googleService;
    private BingWebmasterService $bingService;
    private UrlSubmissionService $urlSubmissionService;
    private DatabaseService $db;
    private Config $config;
    private Logger $logger;
    private Client $httpClient;

    public function __construct(
        GoogleSearchConsoleService $googleService,
        BingWebmasterService $bingService,
        UrlSubmissionService $urlSubmissionService,
        DatabaseService $db,
        Config $config,
        Logger $logger
    ) {
        $this->googleService = $googleService;
        $this->bingService = $bingService;
        $this->urlSubmissionService = $urlSubmissionService;
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        $this->httpClient = new Client(['timeout' => 30]);
    }

    public function submitSitemap(int $userId, int $domainId, string $sitemapUrl, array $providers = ['google', 'bing']): array
    {
        $results = [];
        $overallSuccess = false;

        // Get domain information
        $domain = $this->db->fetch('SELECT * FROM domains WHERE id = ? AND user_id = ?', [$domainId, $userId]);
        if (!$domain) {
            return [
                'success' => false,
                'error' => 'Domain not found or access denied'
            ];
        }

        $siteUrl = $domain['protocol'] . '://' . $domain['domain'];

        // Validate sitemap URL
        $validation = $this->validateSitemapUrl($sitemapUrl);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'error' => implode(', ', $validation['errors'])
            ];
        }

        // Record sitemap submission
        $sitemapId = $this->recordSitemapSubmission($userId, $domainId, $sitemapUrl);

        // Submit to Google if requested
        if (in_array('google', $providers)) {
            try {
                $googleResult = $this->googleService->submitSitemap($userId, $siteUrl, $sitemapUrl);
                $results['google'] = $googleResult;
                
                if ($googleResult['success']) {
                    $overallSuccess = true;
                    $this->updateSitemapStatus($sitemapId, 'google_submitted', true);
                }
            } catch (\Exception $e) {
                $results['google'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        // Submit to Bing if requested
        if (in_array('bing', $providers)) {
            try {
                $bingResult = $this->bingService->submitSitemap($userId, $siteUrl, $sitemapUrl);
                $results['bing'] = $bingResult;
                
                if ($bingResult['success']) {
                    $overallSuccess = true;
                    $this->updateSitemapStatus($sitemapId, 'bing_submitted', true);
                }
            } catch (\Exception $e) {
                $results['bing'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        // Update overall status
        $status = $overallSuccess ? 'completed' : 'failed';
        $this->updateSitemapStatus($sitemapId, 'status', $status);

        return [
            'success' => $overallSuccess,
            'results' => $results,
            'sitemap_id' => $sitemapId,
            'message' => $this->generateSitemapMessage($results)
        ];
    }

    public function processSitemapUrls(int $userId, int $sitemapId, bool $autoSubmit = false): array
    {
        // Get sitemap information
        $sitemap = $this->db->fetch('SELECT * FROM sitemaps WHERE id = ? AND user_id = ?', [$sitemapId, $userId]);
        if (!$sitemap) {
            return [
                'success' => false,
                'error' => 'Sitemap not found'
            ];
        }

        try {
            // Parse sitemap and extract URLs
            $urls = $this->parseSitemap($sitemap['sitemap_url']);
            
            if (empty($urls)) {
                return [
                    'success' => false,
                    'error' => 'No URLs found in sitemap'
                ];
            }

            // Update sitemap with URL count
            $this->db->update('sitemaps', [
                'total_urls' => count($urls),
                'status' => 'processing'
            ], ['id' => $sitemapId]);

            $results = [
                'total_urls' => count($urls),
                'processed' => 0,
                'successful' => 0,
                'failed' => 0,
                'urls' => []
            ];

            // Process URLs if auto-submit is enabled
            if ($autoSubmit) {
                $providers = [];
                if ($sitemap['google_submitted']) $providers[] = 'google';
                if ($sitemap['bing_submitted']) $providers[] = 'bing';

                foreach ($urls as $url) {
                    try {
                        $result = $this->urlSubmissionService->submitUrl(
                            $userId, 
                            $sitemap['domain_id'], 
                            $url, 
                            $providers
                        );
                        
                        $results['processed']++;
                        if ($result['success']) {
                            $results['successful']++;
                        } else {
                            $results['failed']++;
                        }
                        
                        $results['urls'][$url] = $result;
                        
                    } catch (\Exception $e) {
                        $results['processed']++;
                        $results['failed']++;
                        $results['urls'][$url] = [
                            'success' => false,
                            'error' => $e->getMessage()
                        ];
                    }
                }

                // Update sitemap statistics
                $this->db->update('sitemaps', [
                    'processed_urls' => $results['processed'],
                    'successful_urls' => $results['successful'],
                    'failed_urls' => $results['failed'],
                    'status' => 'completed'
                ], ['id' => $sitemapId]);
            }

            return [
                'success' => true,
                'results' => $results
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to process sitemap URLs', [
                'user_id' => $userId,
                'sitemap_id' => $sitemapId,
                'error' => $e->getMessage()
            ]);

            $this->db->update('sitemaps', [
                'status' => 'failed'
            ], ['id' => $sitemapId]);

            return [
                'success' => false,
                'error' => 'Failed to process sitemap: ' . $e->getMessage()
            ];
        }
    }

    public function getSitemaps(int $userId, int $page = 1, int $perPage = 20): array
    {
        $sql = 'SELECT s.*, d.domain, d.protocol 
                FROM sitemaps s 
                JOIN domains d ON s.domain_id = d.id 
                WHERE s.user_id = ? 
                ORDER BY s.created_at DESC';

        return $this->db->paginate($sql, [$userId], $page, $perPage);
    }

    private function validateSitemapUrl(string $sitemapUrl): array
    {
        $errors = [];

        if (empty($sitemapUrl)) {
            $errors[] = 'Sitemap URL is required';
        } elseif (!filter_var($sitemapUrl, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid sitemap URL format';
        } elseif (!preg_match('/\.xml$/i', $sitemapUrl)) {
            $errors[] = 'Sitemap URL must end with .xml';
        }

        // Try to fetch the sitemap to validate it exists
        if (empty($errors)) {
            try {
                $response = $this->httpClient->get($sitemapUrl, ['timeout' => 10]);
                $statusCode = $response->getStatusCode();
                
                if ($statusCode !== 200) {
                    $errors[] = "Sitemap URL returned HTTP {$statusCode}";
                }
                
                $contentType = $response->getHeaderLine('Content-Type');
                if (!empty($contentType) && strpos($contentType, 'xml') === false) {
                    $errors[] = 'Sitemap URL does not return XML content';
                }
                
            } catch (\Exception $e) {
                $errors[] = 'Cannot access sitemap URL: ' . $e->getMessage();
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    private function parseSitemap(string $sitemapUrl): array
    {
        $urls = [];

        try {
            $response = $this->httpClient->get($sitemapUrl);
            $xmlContent = $response->getBody()->getContents();
            
            // Parse XML
            $xml = simplexml_load_string($xmlContent);
            if ($xml === false) {
                throw new \Exception('Invalid XML format');
            }

            // Handle different sitemap formats
            if (isset($xml->url)) {
                // Standard sitemap
                foreach ($xml->url as $urlElement) {
                    if (isset($urlElement->loc)) {
                        $urls[] = (string)$urlElement->loc;
                    }
                }
            } elseif (isset($xml->sitemap)) {
                // Sitemap index - recursively parse sub-sitemaps
                foreach ($xml->sitemap as $sitemapElement) {
                    if (isset($sitemapElement->loc)) {
                        $subSitemapUrls = $this->parseSitemap((string)$sitemapElement->loc);
                        $urls = array_merge($urls, $subSitemapUrls);
                    }
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('Failed to parse sitemap', [
                'sitemap_url' => $sitemapUrl,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }

        return array_unique($urls);
    }

    private function recordSitemapSubmission(int $userId, int $domainId, string $sitemapUrl): int
    {
        return $this->db->insert('sitemaps', [
            'user_id' => $userId,
            'domain_id' => $domainId,
            'sitemap_url' => $sitemapUrl,
            'status' => 'pending'
        ]);
    }

    private function updateSitemapStatus(int $sitemapId, string $field, mixed $value): void
    {
        $this->db->update('sitemaps', [$field => $value], ['id' => $sitemapId]);
    }

    private function generateSitemapMessage(array $results): string
    {
        $messages = [];

        if (isset($results['google'])) {
            if ($results['google']['success']) {
                $messages[] = 'Google: Success';
            } else {
                $messages[] = 'Google: Failed - ' . $results['google']['error'];
            }
        }

        if (isset($results['bing'])) {
            if ($results['bing']['success']) {
                $messages[] = 'Bing: Success';
            } else {
                $messages[] = 'Bing: Failed - ' . $results['bing']['error'];
            }
        }

        return implode('; ', $messages);
    }
}
