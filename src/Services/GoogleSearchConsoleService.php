<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\GoogleOAuthService;
use App\Services\DatabaseService;
use App\Utils\Config;
use Monolog\Logger;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class GoogleSearchConsoleService
{
    private GoogleOAuthService $oauthService;
    private DatabaseService $db;
    private Config $config;
    private Logger $logger;
    private Client $httpClient;

    private const BASE_URL = 'https://www.googleapis.com/webmasters/v3';
    private const INDEXING_URL = 'https://indexing.googleapis.com/v3';

    public function __construct(
        GoogleOAuthService $oauthService,
        DatabaseService $db,
        Config $config,
        Logger $logger
    ) {
        $this->oauthService = $oauthService;
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        $this->httpClient = new Client(['timeout' => 30]);
    }

    public function getSites(int $userId): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::BASE_URL . '/sites'
            );

            if (!$response['success']) {
                return [
                    'success' => false,
                    'error' => $response['error']
                ];
            }

            $sites = $response['data']['siteEntry'] ?? [];
            
            $this->logger->info('Retrieved Google Search Console sites', [
                'user_id' => $userId,
                'site_count' => count($sites)
            ]);

            return [
                'success' => true,
                'sites' => $sites
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get Google Search Console sites', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to retrieve sites: ' . $e->getMessage()
            ];
        }
    }

    public function submitUrl(int $userId, string $url, string $type = 'URL_UPDATED'): array
    {
        try {
            // Check daily quota
            if (!$this->checkQuota($userId)) {
                return [
                    'success' => false,
                    'error' => 'Daily quota exceeded for Google Indexing API'
                ];
            }

            $response = $this->oauthService->makeApiRequest(
                $userId,
                'POST',
                self::INDEXING_URL . '/urlNotifications:publish',
                [
                    'json' => [
                        'url' => $url,
                        'type' => $type
                    ]
                ]
            );

            if ($response['success']) {
                // Track API usage
                $this->trackApiUsage($userId, 'urlNotifications:publish');
                
                // Log the submission
                $this->logUrlSubmission($userId, $url, 'google', $response['data']);

                $this->logger->info('URL submitted to Google Indexing API', [
                    'user_id' => $userId,
                    'url' => $url,
                    'type' => $type
                ]);

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'message' => 'URL submitted successfully to Google'
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to submit URL to Google', [
                'user_id' => $userId,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to submit URL: ' . $e->getMessage()
            ];
        }
    }

    public function getUrlStatus(int $userId, string $url): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::INDEXING_URL . '/urlNotifications/metadata',
                [
                    'query' => ['url' => $url]
                ]
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'urlNotifications/metadata');

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'status' => $this->parseIndexingStatus($response['data'])
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get URL status from Google', [
                'user_id' => $userId,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get URL status: ' . $e->getMessage()
            ];
        }
    }

    public function inspectUrl(int $userId, string $siteUrl, string $inspectionUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'POST',
                self::BASE_URL . '/sites/' . urlencode($siteUrl) . '/urlInspection/index:inspect',
                [
                    'json' => [
                        'inspectionUrl' => $inspectionUrl,
                        'siteUrl' => $siteUrl
                    ]
                ]
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'urlInspection/index:inspect');

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'inspection_result' => $this->parseInspectionResult($response['data'])
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to inspect URL with Google', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'inspection_url' => $inspectionUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to inspect URL: ' . $e->getMessage()
            ];
        }
    }

    public function submitSitemap(int $userId, string $siteUrl, string $sitemapUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'PUT',
                self::BASE_URL . '/sites/' . urlencode($siteUrl) . '/sitemaps/' . urlencode($sitemapUrl)
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'sitemaps:submit');

                $this->logger->info('Sitemap submitted to Google Search Console', [
                    'user_id' => $userId,
                    'site_url' => $siteUrl,
                    'sitemap_url' => $sitemapUrl
                ]);

                return [
                    'success' => true,
                    'message' => 'Sitemap submitted successfully to Google'
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to submit sitemap to Google', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'sitemap_url' => $sitemapUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to submit sitemap: ' . $e->getMessage()
            ];
        }
    }

    public function getSitemaps(int $userId, string $siteUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::BASE_URL . '/sites/' . urlencode($siteUrl) . '/sitemaps'
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'sitemaps:list');

                return [
                    'success' => true,
                    'sitemaps' => $response['data']['sitemap'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get sitemaps from Google', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get sitemaps: ' . $e->getMessage()
            ];
        }
    }

    private function checkQuota(int $userId): bool
    {
        $today = date('Y-m-d');
        $usage = $this->db->fetch(
            'SELECT SUM(requests_count) as total FROM api_usage 
             WHERE user_id = ? AND provider = "google" AND date = ?',
            [$userId, $today]
        );

        $dailyQuota = $this->config->get('GOOGLE_DAILY_QUOTA', 200);
        $currentUsage = (int)($usage['total'] ?? 0);

        return $currentUsage < $dailyQuota;
    }

    private function trackApiUsage(int $userId, string $endpoint): void
    {
        $today = date('Y-m-d');
        
        $existing = $this->db->fetch(
            'SELECT id, requests_count FROM api_usage 
             WHERE user_id = ? AND provider = "google" AND endpoint = ? AND date = ?',
            [$userId, $endpoint, $today]
        );

        if ($existing) {
            $this->db->update('api_usage', 
                ['requests_count' => $existing['requests_count'] + 1],
                ['id' => $existing['id']]
            );
        } else {
            $this->db->insert('api_usage', [
                'user_id' => $userId,
                'provider' => 'google',
                'endpoint' => $endpoint,
                'requests_count' => 1,
                'date' => $today
            ]);
        }
    }

    private function logUrlSubmission(int $userId, string $url, string $provider, array $responseData): void
    {
        // This would typically update the url_submissions table
        // Implementation depends on how you want to track submissions
        $this->logger->info('URL submission logged', [
            'user_id' => $userId,
            'url' => $url,
            'provider' => $provider,
            'response' => $responseData
        ]);
    }

    private function parseIndexingStatus(array $data): string
    {
        // Parse Google's response to determine indexing status
        if (isset($data['urlNotificationMetadata']['latestUpdate'])) {
            $update = $data['urlNotificationMetadata']['latestUpdate'];
            return $update['type'] ?? 'unknown';
        }

        return 'not_found';
    }

    private function parseInspectionResult(array $data): array
    {
        $result = [
            'index_status' => 'unknown',
            'coverage_state' => 'unknown',
            'crawl_time' => null,
            'user_canonical' => null,
            'google_canonical' => null
        ];

        if (isset($data['inspectionResult']['indexStatusResult'])) {
            $indexResult = $data['inspectionResult']['indexStatusResult'];
            $result['index_status'] = $indexResult['verdict'] ?? 'unknown';
            $result['coverage_state'] = $indexResult['coverageState'] ?? 'unknown';
            $result['crawl_time'] = $indexResult['lastCrawlTime'] ?? null;
            $result['user_canonical'] = $indexResult['userCanonical'] ?? null;
            $result['google_canonical'] = $indexResult['googleCanonical'] ?? null;
        }

        return $result;
    }
}
