<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\DatabaseService;
use App\Services\CacheService;
use App\Utils\Config;
use Monolog\Logger;

class PerformanceMonitoringService
{
    private DatabaseService $db;
    private CacheService $cache;
    private Config $config;
    private Logger $logger;
    private array $metrics = [];
    private float $requestStartTime;

    public function __construct(DatabaseService $db, CacheService $cache, Config $config, Logger $logger)
    {
        $this->db = $db;
        $this->cache = $cache;
        $this->config = $config;
        $this->logger = $logger;
        $this->requestStartTime = microtime(true);
    }

    public function startTimer(string $name): void
    {
        $this->metrics[$name] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true)
        ];
    }

    public function endTimer(string $name): array
    {
        if (!isset($this->metrics[$name])) {
            throw new \InvalidArgumentException("Timer '{$name}' was not started");
        }

        $metric = $this->metrics[$name];
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);

        $result = [
            'name' => $name,
            'execution_time' => $endTime - $metric['start_time'],
            'memory_used' => $endMemory - $metric['start_memory'],
            'peak_memory_used' => $endPeakMemory - $metric['start_peak_memory'],
            'start_time' => $metric['start_time'],
            'end_time' => $endTime
        ];

        $this->metrics[$name] = array_merge($metric, $result);
        
        return $result;
    }

    public function measureFunction(string $name, callable $function)
    {
        $this->startTimer($name);
        
        try {
            $result = $function();
            return $result;
        } finally {
            $this->endTimer($name);
        }
    }

    public function recordMetric(string $name, $value, string $type = 'gauge'): void
    {
        $metric = [
            'name' => $name,
            'value' => $value,
            'type' => $type,
            'timestamp' => time(),
            'date' => date('Y-m-d H:i:s')
        ];

        // Store in database for historical analysis
        $this->db->insert('performance_metrics', $metric);

        // Store in cache for quick access
        $cacheKey = "metric:{$name}:latest";
        $this->cache->set($cacheKey, $metric, 3600);

        // Log significant performance issues
        if ($this->isSignificantMetric($name, $value)) {
            $this->logger->warning('Performance metric threshold exceeded', $metric);
        }
    }

    public function getSystemMetrics(): array
    {
        return [
            'memory' => $this->getMemoryMetrics(),
            'cpu' => $this->getCpuMetrics(),
            'disk' => $this->getDiskMetrics(),
            'database' => $this->getDatabaseMetrics(),
            'cache' => $this->getCacheMetrics(),
            'request' => $this->getRequestMetrics()
        ];
    }

    public function getMemoryMetrics(): array
    {
        return [
            'current_usage' => memory_get_usage(true),
            'current_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_usage' => memory_get_peak_usage(true),
            'peak_usage_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'limit' => ini_get('memory_limit'),
            'limit_bytes' => $this->parseMemoryLimit(ini_get('memory_limit'))
        ];
    }

    public function getCpuMetrics(): array
    {
        $load = sys_getloadavg();
        
        return [
            'load_1min' => $load[0] ?? null,
            'load_5min' => $load[1] ?? null,
            'load_15min' => $load[2] ?? null,
            'cpu_count' => $this->getCpuCount()
        ];
    }

    public function getDiskMetrics(): array
    {
        $rootPath = '/';
        
        return [
            'free_space' => disk_free_space($rootPath),
            'free_space_gb' => round(disk_free_space($rootPath) / 1024 / 1024 / 1024, 2),
            'total_space' => disk_total_space($rootPath),
            'total_space_gb' => round(disk_total_space($rootPath) / 1024 / 1024 / 1024, 2),
            'used_percentage' => round((1 - disk_free_space($rootPath) / disk_total_space($rootPath)) * 100, 2)
        ];
    }

    public function getDatabaseMetrics(): array
    {
        try {
            // Connection status
            $status = $this->db->fetchAll("SHOW STATUS LIKE 'Threads_%'");
            $statusArray = [];
            foreach ($status as $row) {
                $statusArray[strtolower($row['Variable_name'])] = $row['Value'];
            }

            // Query cache stats
            $queryCache = $this->db->fetchAll("SHOW STATUS LIKE 'Qcache_%'");
            $queryCacheArray = [];
            foreach ($queryCache as $row) {
                $queryCacheArray[strtolower($row['Variable_name'])] = $row['Value'];
            }

            // Slow queries
            $slowQueries = $this->db->fetch("SHOW STATUS LIKE 'Slow_queries'");

            return [
                'threads_connected' => $statusArray['threads_connected'] ?? 0,
                'threads_running' => $statusArray['threads_running'] ?? 0,
                'slow_queries' => $slowQueries['Value'] ?? 0,
                'query_cache' => $queryCacheArray
            ];
        } catch (\Exception $e) {
            $this->logger->error('Failed to get database metrics', ['error' => $e->getMessage()]);
            return ['error' => 'Unable to fetch database metrics'];
        }
    }

    public function getCacheMetrics(): array
    {
        return $this->cache->getStats();
    }

    public function getRequestMetrics(): array
    {
        $currentTime = microtime(true);
        
        return [
            'request_time' => $currentTime - $this->requestStartTime,
            'request_time_ms' => round(($currentTime - $this->requestStartTime) * 1000, 2),
            'start_time' => $this->requestStartTime,
            'current_time' => $currentTime
        ];
    }

    public function getPerformanceReport(int $hours = 24): array
    {
        $since = date('Y-m-d H:i:s', time() - ($hours * 3600));
        
        // Get recent metrics
        $metrics = $this->db->fetchAll(
            'SELECT * FROM performance_metrics WHERE date >= ? ORDER BY date DESC',
            [$since]
        );

        // Group by metric name
        $grouped = [];
        foreach ($metrics as $metric) {
            $grouped[$metric['name']][] = $metric;
        }

        $report = [
            'period' => "{$hours} hours",
            'generated_at' => date('Y-m-d H:i:s'),
            'metrics' => []
        ];

        foreach ($grouped as $name => $values) {
            $numericValues = array_map(function($m) { return (float)$m['value']; }, $values);
            
            $report['metrics'][$name] = [
                'count' => count($values),
                'average' => round(array_sum($numericValues) / count($numericValues), 4),
                'min' => min($numericValues),
                'max' => max($numericValues),
                'latest' => $values[0]['value'],
                'trend' => $this->calculateTrend($numericValues)
            ];
        }

        return $report;
    }

    public function getSlowEndpoints(int $limit = 10): array
    {
        $sql = "
            SELECT 
                name,
                AVG(value) as avg_time,
                MAX(value) as max_time,
                MIN(value) as min_time,
                COUNT(*) as request_count
            FROM performance_metrics 
            WHERE name LIKE 'endpoint:%' 
            AND date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY name
            ORDER BY avg_time DESC
            LIMIT ?
        ";

        return $this->db->fetchAll($sql, [$limit]);
    }

    public function recordEndpointPerformance(string $endpoint, float $executionTime): void
    {
        $this->recordMetric("endpoint:{$endpoint}", $executionTime, 'timing');
        
        // Also record general endpoint metrics
        $this->recordMetric('endpoint_avg_time', $executionTime, 'timing');
        
        // Alert on slow endpoints
        $slowThreshold = (float)$this->config->get('SLOW_ENDPOINT_THRESHOLD', 2.0);
        if ($executionTime > $slowThreshold) {
            $this->logger->warning('Slow endpoint detected', [
                'endpoint' => $endpoint,
                'execution_time' => $executionTime,
                'threshold' => $slowThreshold
            ]);
        }
    }

    public function recordDatabaseQuery(string $query, float $executionTime, int $rowCount = 0): void
    {
        $this->recordMetric('db_query_time', $executionTime, 'timing');
        $this->recordMetric('db_query_rows', $rowCount, 'counter');
        
        // Log slow queries
        $slowQueryThreshold = (float)$this->config->get('SLOW_QUERY_THRESHOLD', 1.0);
        if ($executionTime > $slowQueryThreshold) {
            $this->logger->warning('Slow database query', [
                'query' => substr($query, 0, 200) . (strlen($query) > 200 ? '...' : ''),
                'execution_time' => $executionTime,
                'row_count' => $rowCount
            ]);
        }
    }

    public function getHealthCheck(): array
    {
        $health = [
            'status' => 'healthy',
            'checks' => [],
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // Database health
        try {
            $this->db->fetch('SELECT 1');
            $health['checks']['database'] = 'healthy';
        } catch (\Exception $e) {
            $health['checks']['database'] = 'unhealthy';
            $health['status'] = 'unhealthy';
        }

        // Cache health
        try {
            $this->cache->set('health_check', time(), 60);
            $health['checks']['cache'] = 'healthy';
        } catch (\Exception $e) {
            $health['checks']['cache'] = 'unhealthy';
            $health['status'] = 'degraded';
        }

        // Memory health
        $memory = $this->getMemoryMetrics();
        $memoryUsagePercent = ($memory['current_usage'] / $memory['limit_bytes']) * 100;
        if ($memoryUsagePercent > 90) {
            $health['checks']['memory'] = 'critical';
            $health['status'] = 'unhealthy';
        } elseif ($memoryUsagePercent > 75) {
            $health['checks']['memory'] = 'warning';
            if ($health['status'] === 'healthy') {
                $health['status'] = 'degraded';
            }
        } else {
            $health['checks']['memory'] = 'healthy';
        }

        // Disk health
        $disk = $this->getDiskMetrics();
        if ($disk['used_percentage'] > 95) {
            $health['checks']['disk'] = 'critical';
            $health['status'] = 'unhealthy';
        } elseif ($disk['used_percentage'] > 85) {
            $health['checks']['disk'] = 'warning';
            if ($health['status'] === 'healthy') {
                $health['status'] = 'degraded';
            }
        } else {
            $health['checks']['disk'] = 'healthy';
        }

        return $health;
    }

    private function isSignificantMetric(string $name, $value): bool
    {
        $thresholds = [
            'endpoint_avg_time' => 2.0,
            'db_query_time' => 1.0,
            'memory_usage_percent' => 80.0,
            'disk_usage_percent' => 85.0
        ];

        return isset($thresholds[$name]) && $value > $thresholds[$name];
    }

    private function calculateTrend(array $values): string
    {
        if (count($values) < 2) {
            return 'stable';
        }

        $first = array_slice($values, 0, count($values) / 2);
        $second = array_slice($values, count($values) / 2);

        $firstAvg = array_sum($first) / count($first);
        $secondAvg = array_sum($second) / count($second);

        $change = (($secondAvg - $firstAvg) / $firstAvg) * 100;

        if ($change > 10) {
            return 'increasing';
        } elseif ($change < -10) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }

    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int)$limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    private function getCpuCount(): int
    {
        if (is_file('/proc/cpuinfo')) {
            $cpuinfo = file_get_contents('/proc/cpuinfo');
            preg_match_all('/^processor/m', $cpuinfo, $matches);
            return count($matches[0]);
        }

        return 1; // Default fallback
    }
}
