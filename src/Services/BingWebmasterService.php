<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\BingOAuthService;
use App\Services\DatabaseService;
use App\Utils\Config;
use Monolog\Logger;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BingWebmasterService
{
    private BingOAuthService $oauthService;
    private DatabaseService $db;
    private Config $config;
    private Logger $logger;
    private Client $httpClient;

    private const BASE_URL = 'https://ssl.bing.com/webmaster/api.svc/json';

    public function __construct(
        BingOAuthService $oauthService,
        DatabaseService $db,
        Config $config,
        Logger $logger
    ) {
        $this->oauthService = $oauthService;
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        $this->httpClient = new Client(['timeout' => 30]);
    }

    public function getSites(int $userId): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::BASE_URL . '/GetUserSites'
            );

            if (!$response['success']) {
                return [
                    'success' => false,
                    'error' => $response['error']
                ];
            }

            $sites = $response['data']['d'] ?? [];
            
            $this->logger->info('Retrieved Bing Webmaster sites', [
                'user_id' => $userId,
                'site_count' => count($sites)
            ]);

            return [
                'success' => true,
                'sites' => $sites
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get Bing Webmaster sites', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to retrieve sites: ' . $e->getMessage()
            ];
        }
    }

    public function submitUrl(int $userId, string $siteUrl, string $url): array
    {
        try {
            // Check daily quota
            if (!$this->checkQuota($userId)) {
                return [
                    'success' => false,
                    'error' => 'Daily quota exceeded for Bing Webmaster API'
                ];
            }

            $response = $this->oauthService->makeApiRequest(
                $userId,
                'POST',
                self::BASE_URL . '/SubmitUrl',
                [
                    'json' => [
                        'siteUrl' => $siteUrl,
                        'url' => $url
                    ]
                ]
            );

            if ($response['success']) {
                // Track API usage
                $this->trackApiUsage($userId, 'SubmitUrl');
                
                // Log the submission
                $this->logUrlSubmission($userId, $url, 'bing', $response['data']);

                $this->logger->info('URL submitted to Bing Webmaster API', [
                    'user_id' => $userId,
                    'site_url' => $siteUrl,
                    'url' => $url
                ]);

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'message' => 'URL submitted successfully to Bing'
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to submit URL to Bing', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to submit URL: ' . $e->getMessage()
            ];
        }
    }

    public function submitUrlBatch(int $userId, string $siteUrl, array $urls): array
    {
        try {
            // Check if batch size is within limits
            if (count($urls) > 10) {
                return [
                    'success' => false,
                    'error' => 'Batch size cannot exceed 10 URLs'
                ];
            }

            // Check daily quota
            if (!$this->checkQuota($userId, count($urls))) {
                return [
                    'success' => false,
                    'error' => 'Daily quota exceeded for Bing Webmaster API'
                ];
            }

            $response = $this->oauthService->makeApiRequest(
                $userId,
                'POST',
                self::BASE_URL . '/SubmitUrlBatch',
                [
                    'json' => [
                        'siteUrl' => $siteUrl,
                        'urlList' => $urls
                    ]
                ]
            );

            if ($response['success']) {
                // Track API usage
                $this->trackApiUsage($userId, 'SubmitUrlBatch', count($urls));
                
                // Log each URL submission
                foreach ($urls as $url) {
                    $this->logUrlSubmission($userId, $url, 'bing', $response['data']);
                }

                $this->logger->info('URL batch submitted to Bing Webmaster API', [
                    'user_id' => $userId,
                    'site_url' => $siteUrl,
                    'url_count' => count($urls)
                ]);

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'message' => count($urls) . ' URLs submitted successfully to Bing'
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to submit URL batch to Bing', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'url_count' => count($urls),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to submit URL batch: ' . $e->getMessage()
            ];
        }
    }

    public function getUrlSubmissionQuota(int $userId, string $siteUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::BASE_URL . '/GetUrlSubmissionQuota',
                [
                    'query' => ['siteUrl' => $siteUrl]
                ]
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'GetUrlSubmissionQuota');

                return [
                    'success' => true,
                    'quota' => $response['data']['d'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get URL submission quota from Bing', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get quota: ' . $e->getMessage()
            ];
        }
    }

    public function submitSitemap(int $userId, string $siteUrl, string $sitemapUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'POST',
                self::BASE_URL . '/SubmitSitemap',
                [
                    'json' => [
                        'siteUrl' => $siteUrl,
                        'sitemapUrl' => $sitemapUrl
                    ]
                ]
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'SubmitSitemap');

                $this->logger->info('Sitemap submitted to Bing Webmaster', [
                    'user_id' => $userId,
                    'site_url' => $siteUrl,
                    'sitemap_url' => $sitemapUrl
                ]);

                return [
                    'success' => true,
                    'data' => $response['data'],
                    'message' => 'Sitemap submitted successfully to Bing'
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to submit sitemap to Bing', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'sitemap_url' => $sitemapUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to submit sitemap: ' . $e->getMessage()
            ];
        }
    }

    public function getSitemaps(int $userId, string $siteUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::BASE_URL . '/GetSitemaps',
                [
                    'query' => ['siteUrl' => $siteUrl]
                ]
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'GetSitemaps');

                return [
                    'success' => true,
                    'sitemaps' => $response['data']['d'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get sitemaps from Bing', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get sitemaps: ' . $e->getMessage()
            ];
        }
    }

    public function getCrawlStats(int $userId, string $siteUrl): array
    {
        try {
            $response = $this->oauthService->makeApiRequest(
                $userId,
                'GET',
                self::BASE_URL . '/GetCrawlStats',
                [
                    'query' => ['siteUrl' => $siteUrl]
                ]
            );

            if ($response['success']) {
                $this->trackApiUsage($userId, 'GetCrawlStats');

                return [
                    'success' => true,
                    'stats' => $response['data']['d'] ?? []
                ];
            }

            return [
                'success' => false,
                'error' => $response['error']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Failed to get crawl stats from Bing', [
                'user_id' => $userId,
                'site_url' => $siteUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to get crawl stats: ' . $e->getMessage()
            ];
        }
    }

    private function checkQuota(int $userId, int $requestCount = 1): bool
    {
        $today = date('Y-m-d');
        $usage = $this->db->fetch(
            'SELECT SUM(requests_count) as total FROM api_usage 
             WHERE user_id = ? AND provider = "bing" AND date = ?',
            [$userId, $today]
        );

        $dailyQuota = $this->config->get('BING_DAILY_QUOTA', 10000);
        $currentUsage = (int)($usage['total'] ?? 0);

        return ($currentUsage + $requestCount) <= $dailyQuota;
    }

    private function trackApiUsage(int $userId, string $endpoint, int $requestCount = 1): void
    {
        $today = date('Y-m-d');
        
        $existing = $this->db->fetch(
            'SELECT id, requests_count FROM api_usage 
             WHERE user_id = ? AND provider = "bing" AND endpoint = ? AND date = ?',
            [$userId, $endpoint, $today]
        );

        if ($existing) {
            $this->db->update('api_usage', 
                ['requests_count' => $existing['requests_count'] + $requestCount],
                ['id' => $existing['id']]
            );
        } else {
            $this->db->insert('api_usage', [
                'user_id' => $userId,
                'provider' => 'bing',
                'endpoint' => $endpoint,
                'requests_count' => $requestCount,
                'date' => $today
            ]);
        }
    }

    private function logUrlSubmission(int $userId, string $url, string $provider, array $responseData): void
    {
        // This would typically update the url_submissions table
        // Implementation depends on how you want to track submissions
        $this->logger->info('URL submission logged', [
            'user_id' => $userId,
            'url' => $url,
            'provider' => $provider,
            'response' => $responseData
        ]);
    }
}
