<?php

declare(strict_types=1);

namespace App\Services;

use App\Utils\Config;

class SessionService
{
    private Config $config;
    private bool $started = false;
    private static bool $configured = false;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->configureSession();
    }

    private function configureSession(): void
    {
        // Only configure session settings once and if no session is active
        if (!self::$configured) {
            if (session_status() === PHP_SESSION_NONE) {
                // Configure session settings
                ini_set('session.cookie_lifetime', (string)$this->config->get('SESSION_LIFETIME'));
                ini_set('session.cookie_secure', $this->config->get('SESSION_SECURE') ? '1' : '0');
                ini_set('session.cookie_httponly', $this->config->get('SESSION_HTTPONLY') ? '1' : '0');
                ini_set('session.cookie_samesite', 'Strict');
                ini_set('session.use_strict_mode', '1');
                ini_set('session.use_only_cookies', '1');

                // Generate secure session name
                session_name('SEOINDEXER_SESSION');
            }

            self::$configured = true;
        }
    }

    public function start(): void
    {
        if (!$this->started && session_status() === PHP_SESSION_NONE) {
            session_start();
            $this->started = true;
            
            // Regenerate session ID periodically for security
            if (!$this->has('_session_started')) {
                session_regenerate_id(true);
                $this->set('_session_started', time());
            } elseif (time() - $this->get('_session_started') > 300) { // 5 minutes
                session_regenerate_id(true);
                $this->set('_session_started', time());
            }
        }
    }

    public function destroy(): void
    {
        if ($this->started) {
            session_destroy();
            $this->started = false;
        }
    }

    public function regenerateId(): void
    {
        if ($this->started) {
            session_regenerate_id(true);
        }
    }

    public function set(string $key, mixed $value): void
    {
        $this->start();
        $_SESSION[$key] = $value;
    }

    public function get(string $key, mixed $default = null): mixed
    {
        $this->start();
        return $_SESSION[$key] ?? $default;
    }

    public function has(string $key): bool
    {
        $this->start();
        return isset($_SESSION[$key]);
    }

    public function remove(string $key): void
    {
        $this->start();
        unset($_SESSION[$key]);
    }

    public function clear(): void
    {
        $this->start();
        $_SESSION = [];
    }

    public function flash(string $key, mixed $value): void
    {
        $this->set("_flash_{$key}", $value);
    }

    public function getFlash(string $key, mixed $default = null): mixed
    {
        $flashKey = "_flash_{$key}";
        $value = $this->get($flashKey, $default);
        $this->remove($flashKey);
        return $value;
    }

    public function hasFlash(string $key): bool
    {
        return $this->has("_flash_{$key}");
    }

    public function getId(): string
    {
        $this->start();
        return session_id();
    }

    public function isStarted(): bool
    {
        return $this->started;
    }

    public function getCsrfToken(): string
    {
        if (!$this->has('_csrf_token')) {
            $this->set('_csrf_token', bin2hex(random_bytes(32)));
        }
        return $this->get('_csrf_token');
    }

    public function validateCsrfToken(string $token): bool
    {
        return hash_equals($this->getCsrfToken(), $token);
    }

    public function setUser(array $user): void
    {
        $this->set('user', $user);
        $this->set('user_id', $user['id']);
        $this->set('user_role', $user['role']);
        $this->regenerateId(); // Security: regenerate session ID on login
    }

    public function getUser(): ?array
    {
        return $this->get('user');
    }

    public function getUserId(): ?int
    {
        return $this->get('user_id');
    }

    public function getUserRole(): ?string
    {
        return $this->get('user_role');
    }

    public function isLoggedIn(): bool
    {
        return $this->has('user_id');
    }

    public function isAdmin(): bool
    {
        return $this->getUserRole() === 'admin';
    }

    public function logout(): void
    {
        $this->clear();
        $this->destroy();
    }

    public function all(): array
    {
        $this->start();
        return $_SESSION;
    }
}
