<?php

declare(strict_types=1);

namespace App\Services;

use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Token\AccessToken;
use App\Models\OAuthToken;
use App\Utils\Config;
use Monolog\Logger;
use GuzzleHttp\Client;

class BingOAuthService
{
    private GenericProvider $provider;
    private DatabaseService $db;
    private SessionService $session;
    private Config $config;
    private Logger $logger;
    private OAuthToken $tokenModel;
    private Client $httpClient;

    public function __construct(DatabaseService $db, SessionService $session, Config $config, Logger $logger)
    {
        $this->db = $db;
        $this->session = $session;
        $this->config = $config;
        $this->logger = $logger;
        $this->tokenModel = new OAuthToken($db);
        $this->httpClient = new Client();
        
        $this->initializeProvider();
    }

    private function initializeProvider(): void
    {
        // Microsoft OAuth2 endpoints
        $this->provider = new GenericProvider([
            'clientId' => $this->config->get('BING_CLIENT_ID'),
            'clientSecret' => $this->config->get('BING_CLIENT_SECRET'),
            'redirectUri' => $this->config->get('BING_REDIRECT_URI'),
            'urlAuthorize' => 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
            'urlAccessToken' => 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
            'urlResourceOwnerDetails' => 'https://graph.microsoft.com/v1.0/me',
            'scopes' => ['https://api.bing.microsoft.com/webmaster.read', 'https://api.bing.microsoft.com/webmaster.write']
        ]);
    }

    public function getAuthorizationUrl(): string
    {
        $authUrl = $this->provider->getAuthorizationUrl([
            'scope' => implode(' ', [
                'https://api.bing.microsoft.com/webmaster.read',
                'https://api.bing.microsoft.com/webmaster.write'
            ])
        ]);

        // Store state in session for security
        $this->session->set('bing_oauth2_state', $this->provider->getState());
        
        $this->logger->info('Bing OAuth authorization URL generated');
        
        return $authUrl;
    }

    public function handleCallback(string $code, string $state): array
    {
        // Verify state parameter
        if ($state !== $this->session->get('bing_oauth2_state')) {
            $this->logger->warning('Bing OAuth2 state mismatch', [
                'expected' => $this->session->get('bing_oauth2_state'), 
                'received' => $state
            ]);
            return [
                'success' => false,
                'error' => 'Invalid state parameter'
            ];
        }

        try {
            // Exchange authorization code for access token
            $token = $this->provider->getAccessToken('authorization_code', [
                'code' => $code
            ]);

            // Get user info
            $resourceOwner = $this->provider->getResourceOwner($token);
            $userInfo = $resourceOwner->toArray();

            // Store token in database
            $userId = $this->session->getUserId();
            if (!$userId) {
                return [
                    'success' => false,
                    'error' => 'User not logged in'
                ];
            }

            $this->tokenModel->saveToken($userId, 'bing', [
                'access_token' => $token->getToken(),
                'refresh_token' => $token->getRefreshToken(),
                'expires_at' => $token->getExpires() ? date('Y-m-d H:i:s', $token->getExpires()) : null,
                'scope' => 'webmaster.read webmaster.write',
                'user_info' => $userInfo
            ]);

            $this->logger->info('Bing OAuth token saved successfully', ['user_id' => $userId]);

            return [
                'success' => true,
                'user_info' => $userInfo,
                'message' => 'Microsoft/Bing account connected successfully'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Bing OAuth callback error', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'error' => 'Failed to connect Microsoft/Bing account: ' . $e->getMessage()
            ];
        }
    }

    public function getAccessToken(int $userId): ?AccessToken
    {
        $tokenData = $this->tokenModel->getToken($userId, 'bing');
        
        if (!$tokenData) {
            return null;
        }

        $token = new AccessToken([
            'access_token' => $tokenData['access_token'],
            'refresh_token' => $tokenData['refresh_token'],
            'expires' => $tokenData['expires_at'] ? strtotime($tokenData['expires_at']) : null
        ]);

        // Check if token needs refresh
        if ($token->hasExpired() && $token->getRefreshToken()) {
            return $this->refreshToken($userId, $token);
        }

        return $token;
    }

    public function refreshToken(int $userId, AccessToken $token): ?AccessToken
    {
        try {
            $newToken = $this->provider->getAccessToken('refresh_token', [
                'refresh_token' => $token->getRefreshToken()
            ]);

            // Update token in database
            $this->tokenModel->updateToken($userId, 'bing', [
                'access_token' => $newToken->getToken(),
                'refresh_token' => $newToken->getRefreshToken() ?: $token->getRefreshToken(),
                'expires_at' => $newToken->getExpires() ? date('Y-m-d H:i:s', $newToken->getExpires()) : null
            ]);

            $this->logger->info('Bing OAuth token refreshed', ['user_id' => $userId]);

            return $newToken;

        } catch (\Exception $e) {
            $this->logger->error('Failed to refresh Bing OAuth token', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function revokeToken(int $userId): bool
    {
        try {
            // Remove token from database (Microsoft doesn't have a simple revoke endpoint)
            $this->tokenModel->deleteToken($userId, 'bing');

            $this->logger->info('Bing OAuth token revoked', ['user_id' => $userId]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Failed to revoke Bing OAuth token', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function isConnected(int $userId): bool
    {
        $token = $this->getAccessToken($userId);
        return $token !== null && !$token->hasExpired();
    }

    public function makeApiRequest(int $userId, string $method, string $url, array $options = []): array
    {
        $token = $this->getAccessToken($userId);
        
        if (!$token) {
            return [
                'success' => false,
                'error' => 'Bing account not connected'
            ];
        }

        try {
            $defaultOptions = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token->getToken(),
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ]
            ];

            $options = array_merge_recursive($defaultOptions, $options);
            
            $response = $this->httpClient->request($method, $url, $options);
            $data = json_decode($response->getBody()->getContents(), true);
            
            return [
                'success' => true,
                'data' => $data,
                'status_code' => $response->getStatusCode()
            ];

        } catch (\Exception $e) {
            $this->logger->error('Bing API request failed', [
                'user_id' => $userId,
                'method' => $method,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function getUserInfo(int $userId): ?array
    {
        $tokenData = $this->tokenModel->getToken($userId, 'bing');
        
        if (!$tokenData || !isset($tokenData['user_info'])) {
            return null;
        }

        return json_decode($tokenData['user_info'], true);
    }

    // Bing Webmaster API specific methods
    public function getSites(int $userId): array
    {
        return $this->makeApiRequest(
            $userId,
            'GET',
            'https://ssl.bing.com/webmaster/api.svc/json/GetUserSites'
        );
    }

    public function submitUrl(int $userId, string $url): array
    {
        return $this->makeApiRequest(
            $userId,
            'POST',
            'https://ssl.bing.com/webmaster/api.svc/json/SubmitUrl',
            [
                'json' => [
                    'siteUrl' => $url,
                    'url' => $url
                ]
            ]
        );
    }

    public function submitUrlBatch(int $userId, array $urls): array
    {
        return $this->makeApiRequest(
            $userId,
            'POST',
            'https://ssl.bing.com/webmaster/api.svc/json/SubmitUrlBatch',
            [
                'json' => [
                    'urlList' => $urls
                ]
            ]
        );
    }
}
