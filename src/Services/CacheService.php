<?php

declare(strict_types=1);

namespace App\Services;

use App\Utils\Config;
use Monolog\Logger;

class CacheService
{
    private Config $config;
    private Logger $logger;
    private string $cacheDir;
    private array $memoryCache = [];
    private int $defaultTtl;

    public function __construct(Config $config, Logger $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
        $this->cacheDir = $config->get('CACHE_DIR', __DIR__ . '/../../cache');
        $this->defaultTtl = (int)$config->get('CACHE_DEFAULT_TTL', 3600); // 1 hour default
        
        $this->ensureCacheDirectory();
    }

    public function get(string $key, $default = null)
    {
        // Check memory cache first
        if (isset($this->memoryCache[$key])) {
            $item = $this->memoryCache[$key];
            if ($item['expires'] > time()) {
                return $item['data'];
            }
            unset($this->memoryCache[$key]);
        }

        // Check file cache
        $filePath = $this->getFilePath($key);
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath);
            if ($content !== false) {
                $data = unserialize($content);
                if ($data && $data['expires'] > time()) {
                    // Store in memory cache for faster subsequent access
                    $this->memoryCache[$key] = $data;
                    return $data['data'];
                }
                // Expired, remove file
                unlink($filePath);
            }
        }

        return $default;
    }

    public function set(string $key, $value, ?int $ttl = null): bool
    {
        $ttl = $ttl ?? $this->defaultTtl;
        $expires = time() + $ttl;
        
        $data = [
            'data' => $value,
            'expires' => $expires,
            'created' => time()
        ];

        // Store in memory cache
        $this->memoryCache[$key] = $data;

        // Store in file cache
        $filePath = $this->getFilePath($key);
        $result = file_put_contents($filePath, serialize($data), LOCK_EX);

        if ($result === false) {
            $this->logger->error('Failed to write cache file', ['key' => $key, 'file' => $filePath]);
            return false;
        }

        return true;
    }

    public function delete(string $key): bool
    {
        // Remove from memory cache
        unset($this->memoryCache[$key]);

        // Remove from file cache
        $filePath = $this->getFilePath($key);
        if (file_exists($filePath)) {
            return unlink($filePath);
        }

        return true;
    }

    public function clear(): bool
    {
        // Clear memory cache
        $this->memoryCache = [];

        // Clear file cache
        $files = glob($this->cacheDir . '/*.cache');
        $success = true;

        foreach ($files as $file) {
            if (!unlink($file)) {
                $success = false;
                $this->logger->error('Failed to delete cache file', ['file' => $file]);
            }
        }

        return $success;
    }

    public function has(string $key): bool
    {
        return $this->get($key) !== null;
    }

    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl);
        
        return $value;
    }

    public function rememberForever(string $key, callable $callback)
    {
        return $this->remember($key, $callback, 86400 * 365); // 1 year
    }

    public function increment(string $key, int $value = 1): int
    {
        $current = (int)$this->get($key, 0);
        $new = $current + $value;
        $this->set($key, $new);
        return $new;
    }

    public function decrement(string $key, int $value = 1): int
    {
        $current = (int)$this->get($key, 0);
        $new = max(0, $current - $value);
        $this->set($key, $new);
        return $new;
    }

    public function tags(array $tags): TaggedCache
    {
        return new TaggedCache($this, $tags);
    }

    public function invalidateTag(string $tag): bool
    {
        $tagFile = $this->cacheDir . '/tags/' . md5($tag) . '.tag';
        
        if (file_exists($tagFile)) {
            $keys = unserialize(file_get_contents($tagFile));
            if (is_array($keys)) {
                foreach ($keys as $key) {
                    $this->delete($key);
                }
            }
            return unlink($tagFile);
        }

        return true;
    }

    public function getStats(): array
    {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $expiredCount = 0;
        $validCount = 0;

        foreach ($files as $file) {
            $size = filesize($file);
            $totalSize += $size;

            $content = file_get_contents($file);
            if ($content !== false) {
                $data = unserialize($content);
                if ($data && $data['expires'] > time()) {
                    $validCount++;
                } else {
                    $expiredCount++;
                }
            }
        }

        return [
            'total_files' => count($files),
            'valid_entries' => $validCount,
            'expired_entries' => $expiredCount,
            'total_size_bytes' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'memory_cache_entries' => count($this->memoryCache),
            'cache_directory' => $this->cacheDir
        ];
    }

    public function cleanup(): int
    {
        $files = glob($this->cacheDir . '/*.cache');
        $cleaned = 0;

        foreach ($files as $file) {
            $content = file_get_contents($file);
            if ($content !== false) {
                $data = unserialize($content);
                if (!$data || $data['expires'] <= time()) {
                    if (unlink($file)) {
                        $cleaned++;
                    }
                }
            }
        }

        $this->logger->info('Cache cleanup completed', ['files_cleaned' => $cleaned]);
        return $cleaned;
    }

    private function getFilePath(string $key): string
    {
        $hash = md5($key);
        return $this->cacheDir . '/' . $hash . '.cache';
    }

    private function ensureCacheDirectory(): void
    {
        if (!is_dir($this->cacheDir)) {
            if (!mkdir($this->cacheDir, 0755, true)) {
                throw new \RuntimeException("Cannot create cache directory: {$this->cacheDir}");
            }
        }

        $tagsDir = $this->cacheDir . '/tags';
        if (!is_dir($tagsDir)) {
            mkdir($tagsDir, 0755, true);
        }

        // Create .htaccess to protect cache directory
        $htaccessPath = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessPath)) {
            file_put_contents($htaccessPath, "Deny from all\n");
        }
    }
}

class TaggedCache
{
    private CacheService $cache;
    private array $tags;

    public function __construct(CacheService $cache, array $tags)
    {
        $this->cache = $cache;
        $this->tags = $tags;
    }

    public function get(string $key, $default = null)
    {
        return $this->cache->get($this->taggedKey($key), $default);
    }

    public function set(string $key, $value, ?int $ttl = null): bool
    {
        $taggedKey = $this->taggedKey($key);
        
        // Store the key in tag files
        foreach ($this->tags as $tag) {
            $this->addKeyToTag($tag, $taggedKey);
        }

        return $this->cache->set($taggedKey, $value, $ttl);
    }

    public function delete(string $key): bool
    {
        return $this->cache->delete($this->taggedKey($key));
    }

    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        return $this->cache->remember($this->taggedKey($key), $callback, $ttl);
    }

    private function taggedKey(string $key): string
    {
        return 'tagged:' . implode(':', $this->tags) . ':' . $key;
    }

    private function addKeyToTag(string $tag, string $key): void
    {
        $tagFile = $this->cache->cacheDir . '/tags/' . md5($tag) . '.tag';
        $keys = [];

        if (file_exists($tagFile)) {
            $content = file_get_contents($tagFile);
            if ($content !== false) {
                $keys = unserialize($content) ?: [];
            }
        }

        if (!in_array($key, $keys)) {
            $keys[] = $key;
            file_put_contents($tagFile, serialize($keys), LOCK_EX);
        }
    }
}
