<?php

declare(strict_types=1);

namespace App\Services;

use App\Services\DatabaseService;
use App\Services\SecurityService;
use App\Utils\Config;
use Monolog\Logger;

class PasswordService
{
    private DatabaseService $db;
    private SecurityService $security;
    private Config $config;
    private Logger $logger;

    public function __construct(DatabaseService $db, SecurityService $security, Config $config, Logger $logger)
    {
        $this->db = $db;
        $this->security = $security;
        $this->config = $config;
        $this->logger = $logger;
    }

    public function validatePassword(string $password, ?int $userId = null): array
    {
        $errors = [];
        
        // Basic length check
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }
        
        if (strlen($password) > 128) {
            $errors[] = 'Password must be no more than 128 characters long';
        }
        
        // Character requirements
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        // Check against common passwords
        if ($this->isCommonPassword($password)) {
            $errors[] = 'Password is too common. Please choose a more unique password';
        }
        
        // Check against user's previous passwords if user ID provided
        if ($userId && $this->isReusedPassword($password, $userId)) {
            $errors[] = 'Password has been used recently. Please choose a different password';
        }
        
        // Check for personal information patterns
        if ($userId) {
            $personalInfo = $this->getUserPersonalInfo($userId);
            if ($this->containsPersonalInfo($password, $personalInfo)) {
                $errors[] = 'Password should not contain personal information';
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => $this->calculatePasswordStrength($password)
        ];
    }

    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3          // 3 threads
        ]);
    }

    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    public function generateSecurePassword(int $length = 16): string
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        
        $password = '';
        
        // Ensure at least one character from each category
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        
        // Fill the rest randomly
        $allChars = $lowercase . $uppercase . $numbers . $symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }
        
        // Shuffle the password
        return str_shuffle($password);
    }

    public function createPasswordResetToken(int $userId, string $email): array
    {
        $token = $this->security->generateSecureToken(32);
        $expiresAt = date('Y-m-d H:i:s', time() + 3600); // 1 hour
        $clientIp = $this->getClientIp();
        
        // Invalidate any existing tokens for this user
        $this->db->update('password_reset_tokens', 
            ['used' => true], 
            ['user_id' => $userId, 'used' => false]
        );
        
        // Create new token
        $this->db->insert('password_reset_tokens', [
            'user_id' => $userId,
            'token' => $token,
            'ip_address' => $clientIp,
            'expires_at' => $expiresAt
        ]);
        
        $this->security->logSecurityEvent('password_reset_requested', [
            'user_id' => $userId,
            'email' => $email,
            'ip_address' => $clientIp
        ]);
        
        return [
            'token' => $token,
            'expires_at' => $expiresAt
        ];
    }

    public function validatePasswordResetToken(string $token): ?array
    {
        $tokenData = $this->db->fetch(
            'SELECT * FROM password_reset_tokens WHERE token = ? AND used = FALSE AND expires_at > NOW()',
            [$token]
        );
        
        if (!$tokenData) {
            return null;
        }
        
        return $tokenData;
    }

    public function resetPassword(string $token, string $newPassword): array
    {
        $tokenData = $this->validatePasswordResetToken($token);
        
        if (!$tokenData) {
            return [
                'success' => false,
                'error' => 'Invalid or expired reset token'
            ];
        }
        
        // Validate new password
        $validation = $this->validatePassword($newPassword, $tokenData['user_id']);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'errors' => $validation['errors']
            ];
        }
        
        // Store old password hash for history
        $user = $this->db->fetch('SELECT password_hash FROM users WHERE id = ?', [$tokenData['user_id']]);
        if ($user) {
            $this->storePasswordHistory($tokenData['user_id'], $user['password_hash']);
        }
        
        // Update password
        $hashedPassword = $this->hashPassword($newPassword);
        $this->db->update('users', [
            'password_hash' => $hashedPassword,
            'password_changed_at' => date('Y-m-d H:i:s'),
            'failed_login_attempts' => 0,
            'locked_until' => null
        ], ['id' => $tokenData['user_id']]);
        
        // Mark token as used
        $this->db->update('password_reset_tokens', 
            ['used' => true], 
            ['id' => $tokenData['id']]
        );
        
        $this->security->logSecurityEvent('password_reset_completed', [
            'user_id' => $tokenData['user_id'],
            'ip_address' => $this->getClientIp()
        ]);
        
        return [
            'success' => true,
            'message' => 'Password reset successfully'
        ];
    }

    public function changePassword(int $userId, string $currentPassword, string $newPassword): array
    {
        // Verify current password
        $user = $this->db->fetch('SELECT password_hash FROM users WHERE id = ?', [$userId]);
        if (!$user || !$this->verifyPassword($currentPassword, $user['password_hash'])) {
            return [
                'success' => false,
                'error' => 'Current password is incorrect'
            ];
        }
        
        // Validate new password
        $validation = $this->validatePassword($newPassword, $userId);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'errors' => $validation['errors']
            ];
        }
        
        // Store old password hash for history
        $this->storePasswordHistory($userId, $user['password_hash']);
        
        // Update password
        $hashedPassword = $this->hashPassword($newPassword);
        $this->db->update('users', [
            'password_hash' => $hashedPassword,
            'password_changed_at' => date('Y-m-d H:i:s')
        ], ['id' => $userId]);
        
        $this->security->logSecurityEvent('password_changed', [
            'user_id' => $userId,
            'ip_address' => $this->getClientIp()
        ]);
        
        return [
            'success' => true,
            'message' => 'Password changed successfully'
        ];
    }

    private function calculatePasswordStrength(string $password): array
    {
        $score = 0;
        $feedback = [];
        
        // Length scoring
        if (strlen($password) >= 12) {
            $score += 2;
        } elseif (strlen($password) >= 8) {
            $score += 1;
        }
        
        // Character variety scoring
        if (preg_match('/[a-z]/', $password)) $score += 1;
        if (preg_match('/[A-Z]/', $password)) $score += 1;
        if (preg_match('/[0-9]/', $password)) $score += 1;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 1;
        
        // Complexity bonus
        if (preg_match('/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/', $password)) $score += 1;
        
        // Determine strength level
        if ($score >= 7) {
            $strength = 'very_strong';
        } elseif ($score >= 5) {
            $strength = 'strong';
        } elseif ($score >= 3) {
            $strength = 'medium';
        } else {
            $strength = 'weak';
        }
        
        return [
            'score' => $score,
            'level' => $strength,
            'percentage' => min(100, ($score / 7) * 100)
        ];
    }

    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
            'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',
            'qwerty123', 'admin123', 'root', 'toor', 'pass', 'test', 'guest',
            'login', 'passw0rd', '12345678', 'football', 'iloveyou', 'master',
            'dragon', 'princess', 'sunshine', 'superman', 'batman'
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }

    private function isReusedPassword(string $password, int $userId): bool
    {
        // Check against last 5 passwords
        $passwordHistory = $this->db->fetchAll(
            'SELECT password_hash FROM password_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 5',
            [$userId]
        );
        
        foreach ($passwordHistory as $oldPassword) {
            if ($this->verifyPassword($password, $oldPassword['password_hash'])) {
                return true;
            }
        }
        
        return false;
    }

    private function getUserPersonalInfo(int $userId): array
    {
        $user = $this->db->fetch(
            'SELECT username, email, first_name, last_name FROM users WHERE id = ?',
            [$userId]
        );
        
        return $user ? array_filter($user) : [];
    }

    private function containsPersonalInfo(string $password, array $personalInfo): bool
    {
        $password = strtolower($password);
        
        foreach ($personalInfo as $info) {
            if (strlen($info) >= 3 && strpos($password, strtolower($info)) !== false) {
                return true;
            }
        }
        
        return false;
    }

    private function storePasswordHistory(int $userId, string $passwordHash): void
    {
        $this->db->insert('password_history', [
            'user_id' => $userId,
            'password_hash' => $passwordHash
        ]);
        
        // Keep only last 10 passwords
        $this->db->query(
            'DELETE FROM password_history WHERE user_id = ? AND id NOT IN (
                SELECT id FROM (
                    SELECT id FROM password_history WHERE user_id = ? ORDER BY created_at DESC LIMIT 10
                ) AS recent
            )',
            [$userId, $userId]
        );
    }

    private function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
