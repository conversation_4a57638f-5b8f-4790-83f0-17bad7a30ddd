<?php

declare(strict_types=1);

namespace App\Services;

use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Token\AccessToken;
use App\Models\OAuthToken;
use App\Utils\Config;
use Monolog\Logger;

class GoogleOAuthService
{
    private Google $provider;
    private DatabaseService $db;
    private SessionService $session;
    private Config $config;
    private Logger $logger;
    private OAuthToken $tokenModel;

    public function __construct(DatabaseService $db, SessionService $session, Config $config, Logger $logger)
    {
        $this->db = $db;
        $this->session = $session;
        $this->config = $config;
        $this->logger = $logger;
        $this->tokenModel = new OAuthToken($db);
        
        $this->initializeProvider();
    }

    private function initializeProvider(): void
    {
        $clientId = $this->config->get('GOOGLE_CLIENT_ID');
        $clientSecret = $this->config->get('GOOGLE_CLIENT_SECRET');
        $redirectUri = $this->config->get('GOOGLE_REDIRECT_URI');

        if (empty($clientId) || empty($clientSecret)) {
            throw new \Exception('Google OAuth credentials not configured. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET in your .env file.');
        }

        $this->provider = new Google([
            'clientId' => $clientId,
            'clientSecret' => $clientSecret,
            'redirectUri' => $redirectUri,
            'scopes' => [
                'https://www.googleapis.com/auth/webmasters',
                'https://www.googleapis.com/auth/indexing'
            ]
        ]);
    }

    public function getAuthorizationUrl(): string
    {
        $authUrl = $this->provider->getAuthorizationUrl([
            'access_type' => 'offline',
            'prompt' => 'consent'
        ]);

        // Store state in session for security
        $this->session->set('oauth2_state', $this->provider->getState());
        
        $this->logger->info('Google OAuth authorization URL generated');
        
        return $authUrl;
    }

    public function handleCallback(string $code, string $state): array
    {
        // Verify state parameter
        if ($state !== $this->session->get('oauth2_state')) {
            $this->logger->warning('OAuth2 state mismatch', ['expected' => $this->session->get('oauth2_state'), 'received' => $state]);
            return [
                'success' => false,
                'error' => 'Invalid state parameter'
            ];
        }

        try {
            // Exchange authorization code for access token
            $token = $this->provider->getAccessToken('authorization_code', [
                'code' => $code
            ]);

            // Get user info
            $resourceOwner = $this->provider->getResourceOwner($token);
            $userInfo = $resourceOwner->toArray();

            // Store token in database
            $userId = $this->session->getUserId();
            if (!$userId) {
                return [
                    'success' => false,
                    'error' => 'User not logged in'
                ];
            }

            $this->tokenModel->saveToken($userId, 'google', [
                'access_token' => $token->getToken(),
                'refresh_token' => $token->getRefreshToken(),
                'expires_at' => $token->getExpires() ? date('Y-m-d H:i:s', $token->getExpires()) : null,
                'scope' => implode(' ', $this->provider->getDefaultScopes()),
                'user_info' => $userInfo
            ]);

            $this->logger->info('Google OAuth token saved successfully', ['user_id' => $userId]);

            return [
                'success' => true,
                'user_info' => $userInfo,
                'message' => 'Google account connected successfully'
            ];

        } catch (\Exception $e) {
            $this->logger->error('Google OAuth callback error', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'error' => 'Failed to connect Google account: ' . $e->getMessage()
            ];
        }
    }

    public function getAccessToken(int $userId): ?AccessToken
    {
        $tokenData = $this->tokenModel->getToken($userId, 'google');
        
        if (!$tokenData) {
            return null;
        }

        $token = new AccessToken([
            'access_token' => $tokenData['access_token'],
            'refresh_token' => $tokenData['refresh_token'],
            'expires' => $tokenData['expires_at'] ? strtotime($tokenData['expires_at']) : null
        ]);

        // Check if token needs refresh
        if ($token->hasExpired() && $token->getRefreshToken()) {
            return $this->refreshToken($userId, $token);
        }

        return $token;
    }

    public function refreshToken(int $userId, AccessToken $token): ?AccessToken
    {
        try {
            $newToken = $this->provider->getAccessToken('refresh_token', [
                'refresh_token' => $token->getRefreshToken()
            ]);

            // Update token in database
            $this->tokenModel->updateToken($userId, 'google', [
                'access_token' => $newToken->getToken(),
                'refresh_token' => $newToken->getRefreshToken() ?: $token->getRefreshToken(),
                'expires_at' => $newToken->getExpires() ? date('Y-m-d H:i:s', $newToken->getExpires()) : null
            ]);

            $this->logger->info('Google OAuth token refreshed', ['user_id' => $userId]);

            return $newToken;

        } catch (\Exception $e) {
            $this->logger->error('Failed to refresh Google OAuth token', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function revokeToken(int $userId): bool
    {
        $token = $this->getAccessToken($userId);
        
        if (!$token) {
            return false;
        }

        try {
            // Revoke token with Google
            $this->provider->getHttpClient()->post('https://oauth2.googleapis.com/revoke', [
                'form_params' => [
                    'token' => $token->getToken()
                ]
            ]);

            // Remove token from database
            $this->tokenModel->deleteToken($userId, 'google');

            $this->logger->info('Google OAuth token revoked', ['user_id' => $userId]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error('Failed to revoke Google OAuth token', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function isConnected(int $userId): bool
    {
        $token = $this->getAccessToken($userId);
        return $token !== null && !$token->hasExpired();
    }

    public function makeApiRequest(int $userId, string $method, string $url, array $options = []): array
    {
        $token = $this->getAccessToken($userId);
        
        if (!$token) {
            return [
                'success' => false,
                'error' => 'Google account not connected'
            ];
        }

        try {
            $request = $this->provider->getAuthenticatedRequest($method, $url, $token, $options);
            $response = $this->provider->getHttpClient()->send($request);
            
            $data = json_decode($response->getBody()->getContents(), true);
            
            return [
                'success' => true,
                'data' => $data,
                'status_code' => $response->getStatusCode()
            ];

        } catch (\Exception $e) {
            $this->logger->error('Google API request failed', [
                'user_id' => $userId,
                'method' => $method,
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    public function getUserInfo(int $userId): ?array
    {
        $tokenData = $this->tokenModel->getToken($userId, 'google');
        
        if (!$tokenData || !isset($tokenData['user_info'])) {
            return null;
        }

        return json_decode($tokenData['user_info'], true);
    }
}
