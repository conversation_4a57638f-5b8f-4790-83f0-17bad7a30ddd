<?php

declare(strict_types=1);

namespace App\Controllers;

class DashboardController extends BaseController
{
    public function index(): void
    {
        $this->requireAuth();

        $user = $this->auth->getCurrentUser();

        // Get dashboard statistics
        $stats = [
            'total_domains' => $this->db->count('domains', ['user_id' => $user['id']]),
            'total_urls' => $this->db->count('url_submissions', ['user_id' => $user['id']]),
            'indexed_urls' => $this->db->count('url_submissions', ['user_id' => $user['id'], 'status' => 'indexed']),
            'pending_urls' => $this->db->count('url_submissions', ['user_id' => $user['id'], 'status' => 'pending']),
        ];

        // Get OAuth connection status
        $tokenModel = new \App\Models\OAuthToken($this->db);
        $connectedProviders = $tokenModel->getConnectedProviders($user['id']);

        // Get recent URL submissions
        $recentUrls = $this->db->fetchAll(
            'SELECT us.*, d.domain FROM url_submissions us
             JOIN domains d ON us.domain_id = d.id
             WHERE us.user_id = ?
             ORDER BY us.created_at DESC
             LIMIT 10',
            [$user['id']]
        );

        $this->render('dashboard/index.html.twig', [
            'title' => 'Dashboard',
            'stats' => $stats,
            'recent_urls' => $recentUrls,
            'connected_providers' => $connectedProviders
        ]);
    }

    public function domains(): void
    {
        $this->requireAuth();
        
        $user = $this->auth->getCurrentUser();
        
        // Get user's domains
        $domains = $this->db->fetchAll(
            'SELECT * FROM domains WHERE user_id = ? ORDER BY created_at DESC',
            [$user['id']]
        );

        $this->render('dashboard/domains.html.twig', [
            'title' => 'My Domains',
            'domains' => $domains
        ]);
    }

    public function urls(): void
    {
        $this->requireAuth();

        $user = $this->auth->getCurrentUser();
        $page = (int)($_GET['page'] ?? 1);

        // Get user's domains for the form
        $domains = $this->db->fetchAll(
            'SELECT * FROM domains WHERE user_id = ? AND verified = TRUE ORDER BY domain',
            [$user['id']]
        );

        // Get connected providers
        $tokenModel = new \App\Models\OAuthToken($this->db);
        $connectedProviders = $tokenModel->getConnectedProviders($user['id']);

        // Get paginated URL submissions
        $result = $this->db->paginate(
            'SELECT us.*, d.domain, d.protocol FROM url_submissions us
             JOIN domains d ON us.domain_id = d.id
             WHERE us.user_id = ?
             ORDER BY us.created_at DESC',
            [$user['id']],
            $page,
            20
        );

        // Get submission statistics
        $urlSubmissionService = new \App\Services\UrlSubmissionService($this->db, $this->config, $this->logger);
        $stats = $urlSubmissionService->getSubmissionStats($user['id']);

        $this->render('dashboard/urls.html.twig', [
            'title' => 'URL Submissions',
            'urls' => $result['data'],
            'pagination' => $result['pagination'],
            'domains' => $domains,
            'connected_providers' => $connectedProviders,
            'stats' => $stats
        ]);
    }

    public function sitemaps(): void
    {
        $this->requireAuth();

        $user = $this->auth->getCurrentUser();
        $page = (int)($_GET['page'] ?? 1);

        // Get user's domains for the form
        $domains = $this->db->fetchAll(
            'SELECT * FROM domains WHERE user_id = ? AND verified = TRUE ORDER BY domain',
            [$user['id']]
        );

        // Get connected providers
        $tokenModel = new \App\Models\OAuthToken($this->db);
        $connectedProviders = $tokenModel->getConnectedProviders($user['id']);

        // Get paginated sitemaps
        $result = $this->db->paginate(
            'SELECT s.*, d.domain, d.protocol FROM sitemaps s
             JOIN domains d ON s.domain_id = d.id
             WHERE s.user_id = ?
             ORDER BY s.created_at DESC',
            [$user['id']],
            $page,
            20
        );

        // Get sitemap statistics
        $stats = [
            'total' => $this->db->count('sitemaps', ['user_id' => $user['id']]),
            'completed' => $this->db->count('sitemaps', ['user_id' => $user['id'], 'status' => 'completed']),
            'processing' => $this->db->count('sitemaps', ['user_id' => $user['id'], 'status' => 'processing']),
            'failed' => $this->db->count('sitemaps', ['user_id' => $user['id'], 'status' => 'failed'])
        ];

        $this->render('dashboard/sitemaps.html.twig', [
            'title' => 'Sitemaps',
            'sitemaps' => $result['data'],
            'pagination' => $result['pagination'],
            'domains' => $domains,
            'connected_providers' => $connectedProviders,
            'stats' => $stats
        ]);
    }

    public function apiKeys(): void
    {
        $this->requireAuth();
        
        $user = $this->auth->getCurrentUser();
        
        // Get user's API configurations
        $apiConfigs = $this->db->fetchAll(
            'SELECT * FROM api_configurations WHERE user_id = ? ORDER BY created_at DESC',
            [$user['id']]
        );

        $this->render('dashboard/api-keys.html.twig', [
            'title' => 'API Keys & Configurations',
            'api_configs' => $apiConfigs
        ]);
    }

    public function reports(): void
    {
        $this->requireAuth();

        $user = $this->auth->getCurrentUser();

        // Get comprehensive analytics data
        $analytics = $this->getAnalyticsData($user['id']);

        $this->render('dashboard/reports.html.twig', [
            'title' => 'Reports & Analytics',
            'analytics' => $analytics
        ]);
    }

    private function getAnalyticsData(int $userId): array
    {
        $analytics = [];

        // URL submission statistics
        $analytics['url_stats'] = [
            'total' => $this->db->count('url_submissions', ['user_id' => $userId]),
            'google' => $this->db->count('url_submissions', ['user_id' => $userId, 'provider' => 'google']),
            'bing' => $this->db->count('url_submissions', ['user_id' => $userId, 'provider' => 'bing']),
            'successful' => $this->db->count('url_submissions', ['user_id' => $userId, 'status' => 'submitted']),
            'failed' => $this->db->count('url_submissions', ['user_id' => $userId, 'status' => 'failed'])
        ];

        // Daily submission trends (last 30 days)
        $analytics['daily_submissions'] = $this->db->fetchAll(
            'SELECT DATE(submitted_at) as date, COUNT(*) as count, provider
             FROM url_submissions
             WHERE user_id = ? AND submitted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
             GROUP BY DATE(submitted_at), provider
             ORDER BY date DESC',
            [$userId]
        );

        // API usage statistics
        $analytics['api_usage'] = [
            'daily' => $this->db->fetchAll(
                'SELECT date, provider, SUM(requests_count) as total_requests
                 FROM api_usage
                 WHERE user_id = ? AND date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                 GROUP BY date, provider
                 ORDER BY date DESC',
                [$userId]
            ),
            'by_endpoint' => $this->db->fetchAll(
                'SELECT provider, endpoint, SUM(requests_count) as total_requests
                 FROM api_usage
                 WHERE user_id = ? AND date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                 GROUP BY provider, endpoint
                 ORDER BY total_requests DESC',
                [$userId]
            )
        ];

        // Domain statistics
        $analytics['domain_stats'] = [
            'total' => $this->db->count('domains', ['user_id' => $userId]),
            'verified' => $this->db->count('domains', ['user_id' => $userId, 'verified' => true]),
            'by_domain' => $this->db->fetchAll(
                'SELECT d.domain, d.protocol, COUNT(us.id) as url_count
                 FROM domains d
                 LEFT JOIN url_submissions us ON d.id = us.domain_id
                 WHERE d.user_id = ?
                 GROUP BY d.id, d.domain, d.protocol
                 ORDER BY url_count DESC',
                [$userId]
            )
        ];

        // Sitemap statistics
        $analytics['sitemap_stats'] = [
            'total' => $this->db->count('sitemaps', ['user_id' => $userId]),
            'completed' => $this->db->count('sitemaps', ['user_id' => $userId, 'status' => 'completed']),
            'total_urls_processed' => $this->db->fetch(
                'SELECT SUM(processed_urls) as total FROM sitemaps WHERE user_id = ?',
                [$userId]
            )['total'] ?? 0
        ];

        // Success rates
        if ($analytics['url_stats']['total'] > 0) {
            $analytics['success_rate'] = [
                'overall' => round(($analytics['url_stats']['successful'] / $analytics['url_stats']['total']) * 100, 1),
                'google' => $analytics['url_stats']['google'] > 0 ? round((
                    $this->db->count('url_submissions', ['user_id' => $userId, 'provider' => 'google', 'status' => 'submitted']) /
                    $analytics['url_stats']['google']
                ) * 100, 1) : 0,
                'bing' => $analytics['url_stats']['bing'] > 0 ? round((
                    $this->db->count('url_submissions', ['user_id' => $userId, 'provider' => 'bing', 'status' => 'submitted']) /
                    $analytics['url_stats']['bing']
                ) * 100, 1) : 0
            ];
        } else {
            $analytics['success_rate'] = ['overall' => 0, 'google' => 0, 'bing' => 0];
        }

        // Recent activity
        $analytics['recent_activity'] = $this->db->fetchAll(
            'SELECT us.url, us.provider, us.status, us.submitted_at, d.domain
             FROM url_submissions us
             JOIN domains d ON us.domain_id = d.id
             WHERE us.user_id = ?
             ORDER BY us.submitted_at DESC
             LIMIT 10',
            [$userId]
        );

        return $analytics;
    }

    public function connections(): void
    {
        $this->requireAuth();

        $user = $this->auth->getCurrentUser();
        $tokenModel = new \App\Models\OAuthToken($this->db);

        // Get detailed connection information
        $tokens = $tokenModel->getUserTokens($user['id']);
        $connectedProviders = $tokenModel->getConnectedProviders($user['id']);

        // Get user info for connected accounts
        $googleService = new \App\Services\GoogleOAuthService($this->db, $this->session, $this->config, $this->logger);
        $bingService = new \App\Services\BingOAuthService($this->db, $this->session, $this->config, $this->logger);

        $connectionDetails = [];

        if ($connectedProviders['google'] ?? false) {
            $connectionDetails['google'] = [
                'connected' => true,
                'user_info' => $googleService->getUserInfo($user['id']),
                'token_info' => array_filter($tokens, fn($t) => $t['provider'] === 'google')[0] ?? null
            ];
        } else {
            $connectionDetails['google'] = ['connected' => false];
        }

        if ($connectedProviders['bing'] ?? false) {
            $connectionDetails['bing'] = [
                'connected' => true,
                'user_info' => $bingService->getUserInfo($user['id']),
                'token_info' => array_filter($tokens, fn($t) => $t['provider'] === 'bing')[0] ?? null
            ];
        } else {
            $connectionDetails['bing'] = ['connected' => false];
        }

        $this->render('dashboard/connections.html.twig', [
            'title' => 'API Connections',
            'connections' => $connectionDetails
        ]);
    }

    // Action methods (POST requests)
    
    public function addDomain(): void
    {
        $this->requireAuth();
        $this->requireCsrf();

        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();

        // Validate domain input
        $domain = trim($input['domain'] ?? '');
        $protocol = $input['protocol'] ?? 'https';

        // Remove protocol if included in domain
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = rtrim($domain, '/');

        $errors = [];

        if (empty($domain)) {
            $errors['domain'] = 'Domain is required';
        } elseif (!filter_var('http://' . $domain, FILTER_VALIDATE_URL)) {
            $errors['domain'] = 'Invalid domain format';
        } elseif (strpos($domain, '/') !== false) {
            $errors['domain'] = 'Domain should not contain paths';
        }

        // Check if domain already exists for this user
        if (empty($errors)) {
            $existing = $this->db->fetch(
                'SELECT id FROM domains WHERE user_id = ? AND domain = ?',
                [$user['id'], $domain]
            );

            if ($existing) {
                $errors['domain'] = 'Domain already exists in your account';
            }
        }

        if (empty($errors)) {
            try {
                $domainId = $this->db->insert('domains', [
                    'user_id' => $user['id'],
                    'domain' => $domain,
                    'protocol' => $protocol,
                    'verification_token' => bin2hex(random_bytes(16)),
                    'verification_method' => 'html_file'
                ]);

                $this->logActivity('Domain added', [
                    'domain_id' => $domainId,
                    'domain' => $domain,
                    'protocol' => $protocol
                ]);

                $this->setFlash('success', 'Domain added successfully. Please verify ownership using the instructions below.');
            } catch (\Exception $e) {
                $this->logger->error('Failed to add domain', [
                    'user_id' => $user['id'],
                    'domain' => $domain,
                    'error' => $e->getMessage()
                ]);
                $this->setFlash('error', 'Failed to add domain. Please try again.');
            }
        } else {
            foreach ($errors as $error) {
                $this->setFlash('error', $error);
            }
        }

        $this->redirect('/dashboard/domains');
    }

    public function verifyDomain(): void
    {
        $this->requireAuth();
        $this->requireCsrf();

        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();
        $domainId = (int)($input['domain_id'] ?? 0);

        // Get domain
        $domain = $this->db->fetch(
            'SELECT * FROM domains WHERE id = ? AND user_id = ?',
            [$domainId, $user['id']]
        );

        if (!$domain) {
            $this->setFlash('error', 'Domain not found.');
            $this->redirect('/dashboard/domains');
            return;
        }

        if ($domain['verified']) {
            $this->setFlash('info', 'Domain is already verified.');
            $this->redirect('/dashboard/domains');
            return;
        }

        try {
            $verified = $this->performDomainVerification($domain);

            if ($verified) {
                $this->db->update('domains', [
                    'verified' => true
                ], ['id' => $domainId]);

                $this->logActivity('Domain verified', [
                    'domain_id' => $domainId,
                    'domain' => $domain['domain']
                ]);

                $this->setFlash('success', 'Domain verified successfully!');
            } else {
                $this->setFlash('error', 'Domain verification failed. Please check your verification file and try again.');
            }

        } catch (\Exception $e) {
            $this->logger->error('Domain verification failed', [
                'user_id' => $user['id'],
                'domain_id' => $domainId,
                'error' => $e->getMessage()
            ]);
            $this->setFlash('error', 'Verification failed: ' . $e->getMessage());
        }

        $this->redirect('/dashboard/domains');
    }

    private function performDomainVerification(array $domain): bool
    {
        $verificationUrl = $domain['protocol'] . '://' . $domain['domain'] . '/seo-indexer-verification-' . $domain['verification_token'] . '.html';

        try {
            $client = new \GuzzleHttp\Client(['timeout' => 10]);
            $response = $client->get($verificationUrl);

            if ($response->getStatusCode() === 200) {
                $content = $response->getBody()->getContents();
                $expectedContent = 'seo-indexer-verification-' . $domain['verification_token'];

                return strpos($content, $expectedContent) !== false;
            }

        } catch (\Exception $e) {
            $this->logger->info('Domain verification check failed', [
                'domain' => $domain['domain'],
                'verification_url' => $verificationUrl,
                'error' => $e->getMessage()
            ]);
        }

        return false;
    }

    public function submitUrls(): void
    {
        $this->requireAuth();
        $this->requireCsrf();

        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();

        // Validate input
        $errors = $this->validateInput([
            'domain_id' => ['required' => true, 'type' => 'int'],
            'urls' => ['required' => true],
            'providers' => ['required' => true]
        ], $input);

        if (!empty($errors)) {
            $this->setFlash('error', implode(', ', $errors));
            $this->redirect('/dashboard/urls');
            return;
        }

        try {
            $urlSubmissionService = new \App\Services\UrlSubmissionService($this->db, $this->config, $this->logger);

            // Parse URLs (can be single URL or multiple URLs separated by newlines)
            $urlsText = trim($input['urls']);
            $urls = array_filter(array_map('trim', explode("\n", $urlsText)));

            // Validate URLs
            $validation = $urlSubmissionService->validateUrlBatch($urls);

            if (empty($validation['valid_urls'])) {
                $this->setFlash('error', 'No valid URLs found. Please check your URL format.');
                $this->redirect('/dashboard/urls');
                return;
            }

            // Get selected providers
            $providers = is_array($input['providers']) ? $input['providers'] : [$input['providers']];

            // Submit URLs
            if (count($validation['valid_urls']) === 1) {
                $result = $urlSubmissionService->submitUrl(
                    $user['id'],
                    (int)$input['domain_id'],
                    $validation['valid_urls'][0],
                    $providers
                );
            } else {
                $result = $urlSubmissionService->submitUrlBatch(
                    $user['id'],
                    (int)$input['domain_id'],
                    $validation['valid_urls'],
                    $providers
                );
            }

            if ($result['success']) {
                $this->logActivity('URLs submitted', [
                    'domain_id' => $input['domain_id'],
                    'url_count' => count($validation['valid_urls']),
                    'providers' => $providers
                ]);
                $this->setFlash('success', $result['message']);
            } else {
                $this->setFlash('error', $result['message'] ?? 'Failed to submit URLs');
            }

            // Show warnings for invalid URLs
            if (!empty($validation['invalid_urls'])) {
                $this->setFlash('warning', 'Some URLs were invalid and skipped: ' . implode(', ', array_slice($validation['invalid_urls'], 0, 3)) . (count($validation['invalid_urls']) > 3 ? '...' : ''));
            }

        } catch (\Exception $e) {
            $this->logger->error('URL submission failed', [
                'user_id' => $user['id'],
                'error' => $e->getMessage()
            ]);
            $this->setFlash('error', 'Failed to submit URLs. Please try again.');
        }

        $this->redirect('/dashboard/urls');
    }

    public function submitSitemap(): void
    {
        $this->requireAuth();
        $this->requireCsrf();

        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();

        // Validate input
        $errors = $this->validateInput([
            'domain_id' => ['required' => true, 'type' => 'int'],
            'sitemap_url' => ['required' => true, 'type' => 'url'],
            'providers' => ['required' => true]
        ], $input);

        if (!empty($errors)) {
            $this->setFlash('error', implode(', ', $errors));
            $this->redirect('/dashboard/sitemaps');
            return;
        }

        try {
            // Initialize services
            $googleOAuth = new \App\Services\GoogleOAuthService($this->db, $this->session, $this->config, $this->logger);
            $bingOAuth = new \App\Services\BingOAuthService($this->db, $this->session, $this->config, $this->logger);
            $googleService = new \App\Services\GoogleSearchConsoleService($googleOAuth, $this->db, $this->config, $this->logger);
            $bingService = new \App\Services\BingWebmasterService($bingOAuth, $this->db, $this->config, $this->logger);
            $urlSubmissionService = new \App\Services\UrlSubmissionService($this->db, $this->config, $this->logger);

            $sitemapService = new \App\Services\SitemapService(
                $googleService,
                $bingService,
                $urlSubmissionService,
                $this->db,
                $this->config,
                $this->logger
            );

            // Get selected providers
            $providers = is_array($input['providers']) ? $input['providers'] : [$input['providers']];

            // Submit sitemap
            $result = $sitemapService->submitSitemap(
                $user['id'],
                (int)$input['domain_id'],
                $input['sitemap_url'],
                $providers
            );

            if ($result['success']) {
                $this->logActivity('Sitemap submitted', [
                    'domain_id' => $input['domain_id'],
                    'sitemap_url' => $input['sitemap_url'],
                    'providers' => $providers
                ]);

                $this->setFlash('success', $result['message']);

                // Process URLs if requested
                if (!empty($input['process_urls'])) {
                    $processResult = $sitemapService->processSitemapUrls($user['id'], $result['sitemap_id'], true);
                    if ($processResult['success']) {
                        $this->setFlash('info', 'Sitemap URLs are being processed in the background.');
                    }
                }
            } else {
                $this->setFlash('error', $result['message'] ?? 'Failed to submit sitemap');
            }

        } catch (\Exception $e) {
            $this->logger->error('Sitemap submission failed', [
                'user_id' => $user['id'],
                'error' => $e->getMessage()
            ]);
            $this->setFlash('error', 'Failed to submit sitemap. Please try again.');
        }

        $this->redirect('/dashboard/sitemaps');
    }

    public function saveApiKeys(): void
    {
        $this->requireAuth();
        $this->requireCsrf();
        
        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();
        
        // TODO: Implement API key saving logic
        $this->setFlash('info', 'API key management feature is not yet implemented.');
        $this->redirect('/dashboard/api-keys');
    }

    public function deleteDomain(string $id): void
    {
        $this->requireAuth();
        $this->requireCsrf();
        
        $user = $this->auth->getCurrentUser();
        
        // Verify domain belongs to user
        $domain = $this->db->fetch('SELECT id FROM domains WHERE id = ? AND user_id = ?', [$id, $user['id']]);
        
        if ($domain) {
            $this->db->delete('domains', ['id' => $id, 'user_id' => $user['id']]);
            $this->json(['success' => true, 'message' => 'Domain deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Domain not found'], 404);
        }
    }
}
