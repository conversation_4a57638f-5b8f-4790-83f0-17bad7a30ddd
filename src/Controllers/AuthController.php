<?php

declare(strict_types=1);

namespace App\Controllers;

class AuthController extends BaseController
{
    public function showLogin(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $this->render('auth/login.html.twig', [
            'title' => 'Login'
        ]);
    }

    public function login(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $input = $this->getInput();
        
        if (!$this->validateCsrf()) {
            $this->setFlash('error', 'Invalid security token. Please try again.');
            $this->redirect('/login');
            return;
        }

        $result = $this->auth->login(
            $input['identifier'] ?? '',
            $input['password'] ?? '',
            !empty($input['remember'])
        );

        if ($result['success']) {
            $this->logActivity('User logged in', ['user_id' => $result['user']['id']]);
            $this->setFlash('success', $result['message']);
            
            // Redirect to intended page or dashboard
            $redirectTo = $this->session->get('intended_url', '/dashboard');
            $this->session->remove('intended_url');
            $this->redirect($redirectTo);
        } else {
            $this->setFlash('error', $result['errors']['general'] ?? 'Login failed');
            $this->render('auth/login.html.twig', [
                'title' => 'Login',
                'errors' => $result['errors'],
                'old_input' => $input
            ]);
        }
    }

    public function showRegister(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $this->render('auth/register.html.twig', [
            'title' => 'Register'
        ]);
    }

    public function register(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $input = $this->getInput();
        
        if (!$this->validateCsrf()) {
            $this->setFlash('error', 'Invalid security token. Please try again.');
            $this->redirect('/register');
            return;
        }

        $result = $this->auth->register($input);

        if ($result['success']) {
            $this->logActivity('User registered', ['user_id' => $result['user_id']]);
            $this->setFlash('success', $result['message']);
            $this->redirect('/login');
        } else {
            $this->render('auth/register.html.twig', [
                'title' => 'Register',
                'errors' => $result['errors'],
                'old_input' => $input
            ]);
        }
    }

    public function logout(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->logActivity('User logged out');
            $this->auth->logout();
            $this->setFlash('success', 'You have been logged out successfully.');
        }
        
        $this->redirect('/');
    }

    public function googleAuth(): void
    {
        $this->requireAuth();

        try {
            $googleService = new \App\Services\GoogleOAuthService(
                $this->db,
                $this->session,
                $this->config,
                $this->logger
            );

            $authUrl = $googleService->getAuthorizationUrl();
            $this->redirect($authUrl);

        } catch (\Exception $e) {
            $this->logger->error('Google OAuth initialization failed', ['error' => $e->getMessage()]);
            $this->setFlash('error', 'Failed to initialize Google authentication. Please check your configuration.');
            $this->redirect('/dashboard');
        }
    }

    public function googleCallback(): void
    {
        $this->requireAuth();

        $code = $_GET['code'] ?? '';
        $state = $_GET['state'] ?? '';
        $error = $_GET['error'] ?? '';

        if ($error) {
            $this->setFlash('error', 'Google authentication was cancelled or failed.');
            $this->redirect('/dashboard');
            return;
        }

        if (empty($code) || empty($state)) {
            $this->setFlash('error', 'Invalid Google authentication response.');
            $this->redirect('/dashboard');
            return;
        }

        try {
            $googleService = new \App\Services\GoogleOAuthService(
                $this->db,
                $this->session,
                $this->config,
                $this->logger
            );

            $result = $googleService->handleCallback($code, $state);

            if ($result['success']) {
                $this->logActivity('Google account connected', ['user_info' => $result['user_info']]);
                $this->setFlash('success', $result['message']);
            } else {
                $this->setFlash('error', $result['error']);
            }

        } catch (\Exception $e) {
            $this->logger->error('Google OAuth callback failed', ['error' => $e->getMessage()]);
            $this->setFlash('error', 'Failed to connect Google account.');
        }

        $this->redirect('/dashboard');
    }

    public function bingAuth(): void
    {
        $this->requireAuth();

        try {
            $bingService = new \App\Services\BingOAuthService(
                $this->db,
                $this->session,
                $this->config,
                $this->logger
            );

            $authUrl = $bingService->getAuthorizationUrl();
            $this->redirect($authUrl);

        } catch (\Exception $e) {
            $this->logger->error('Bing OAuth initialization failed', ['error' => $e->getMessage()]);
            $this->setFlash('error', 'Failed to initialize Bing authentication. Please check your configuration.');
            $this->redirect('/dashboard');
        }
    }

    public function bingCallback(): void
    {
        $this->requireAuth();

        $code = $_GET['code'] ?? '';
        $state = $_GET['state'] ?? '';
        $error = $_GET['error'] ?? '';

        if ($error) {
            $this->setFlash('error', 'Bing authentication was cancelled or failed.');
            $this->redirect('/dashboard');
            return;
        }

        if (empty($code) || empty($state)) {
            $this->setFlash('error', 'Invalid Bing authentication response.');
            $this->redirect('/dashboard');
            return;
        }

        try {
            $bingService = new \App\Services\BingOAuthService(
                $this->db,
                $this->session,
                $this->config,
                $this->logger
            );

            $result = $bingService->handleCallback($code, $state);

            if ($result['success']) {
                $this->logActivity('Bing account connected', ['user_info' => $result['user_info']]);
                $this->setFlash('success', $result['message']);
            } else {
                $this->setFlash('error', $result['error']);
            }

        } catch (\Exception $e) {
            $this->logger->error('Bing OAuth callback failed', ['error' => $e->getMessage()]);
            $this->setFlash('error', 'Failed to connect Bing account.');
        }

        $this->redirect('/dashboard');
    }

    public function disconnectGoogle(): void
    {
        $this->requireAuth();

        if (!$this->validateCsrf()) {
            $this->setFlash('error', 'Invalid security token.');
            $this->redirect('/dashboard');
            return;
        }

        try {
            $googleService = new \App\Services\GoogleOAuthService(
                $this->db,
                $this->session,
                $this->config,
                $this->logger
            );

            $userId = $this->auth->getCurrentUser()['id'];

            if ($googleService->revokeToken($userId)) {
                $this->logActivity('Google account disconnected');
                $this->setFlash('success', 'Google account disconnected successfully.');
            } else {
                $this->setFlash('error', 'Failed to disconnect Google account.');
            }

        } catch (\Exception $e) {
            $this->logger->error('Failed to disconnect Google account', ['error' => $e->getMessage()]);
            $this->setFlash('error', 'Failed to disconnect Google account.');
        }

        $this->redirect('/dashboard');
    }

    public function disconnectBing(): void
    {
        $this->requireAuth();

        if (!$this->validateCsrf()) {
            $this->setFlash('error', 'Invalid security token.');
            $this->redirect('/dashboard');
            return;
        }

        try {
            $bingService = new \App\Services\BingOAuthService(
                $this->db,
                $this->session,
                $this->config,
                $this->logger
            );

            $userId = $this->auth->getCurrentUser()['id'];

            if ($bingService->revokeToken($userId)) {
                $this->logActivity('Bing account disconnected');
                $this->setFlash('success', 'Bing account disconnected successfully.');
            } else {
                $this->setFlash('error', 'Failed to disconnect Bing account.');
            }

        } catch (\Exception $e) {
            $this->logger->error('Failed to disconnect Bing account', ['error' => $e->getMessage()]);
            $this->setFlash('error', 'Failed to disconnect Bing account.');
        }

        $this->redirect('/dashboard');
    }

    public function verifyEmail(): void
    {
        $token = $_GET['token'] ?? '';
        
        if (empty($token)) {
            $this->setFlash('error', 'Invalid verification link.');
            $this->redirect('/login');
            return;
        }

        if ($this->auth->verifyEmail($token)) {
            $this->setFlash('success', 'Email verified successfully. You can now log in.');
        } else {
            $this->setFlash('error', 'Invalid or expired verification link.');
        }
        
        $this->redirect('/login');
    }

    public function showForgotPassword(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $this->render('auth/forgot-password.html.twig', [
            'title' => 'Forgot Password'
        ]);
    }

    public function forgotPassword(): void
    {
        if ($this->auth->isLoggedIn()) {
            $this->redirect('/dashboard');
            return;
        }

        $input = $this->getInput();
        
        if (!$this->validateCsrf()) {
            $this->setFlash('error', 'Invalid security token. Please try again.');
            $this->redirect('/forgot-password');
            return;
        }

        $result = $this->auth->requestPasswordReset($input['email'] ?? '');
        
        $this->setFlash($result['success'] ? 'success' : 'error', $result['message']);
        
        if ($result['success']) {
            $this->redirect('/login');
        } else {
            $this->render('auth/forgot-password.html.twig', [
                'title' => 'Forgot Password',
                'errors' => $result['errors'] ?? [],
                'old_input' => $input
            ]);
        }
    }

    public function showResetPassword(): void
    {
        $token = $_GET['token'] ?? '';
        
        if (empty($token)) {
            $this->setFlash('error', 'Invalid reset link.');
            $this->redirect('/login');
            return;
        }

        $this->render('auth/reset-password.html.twig', [
            'title' => 'Reset Password',
            'token' => $token
        ]);
    }

    public function resetPassword(): void
    {
        $input = $this->getInput();
        $token = $input['token'] ?? '';
        
        if (!$this->validateCsrf()) {
            $this->setFlash('error', 'Invalid security token. Please try again.');
            $this->redirect('/reset-password?token=' . urlencode($token));
            return;
        }

        $result = $this->auth->resetPassword($token, $input['password'] ?? '');
        
        if ($result['success']) {
            $this->setFlash('success', $result['message']);
            $this->redirect('/login');
        } else {
            $this->render('auth/reset-password.html.twig', [
                'title' => 'Reset Password',
                'token' => $token,
                'errors' => $result['errors'],
                'old_input' => $input
            ]);
        }
    }
}
