<?php

declare(strict_types=1);

namespace App\Controllers;

use App\App;
use App\Services\DatabaseService;
use App\Services\AuthService;
use App\Services\SessionService;
use App\Utils\Config;
use Twig\Environment;
use Monolog\Logger;

abstract class BaseController
{
    protected App $app;
    protected DatabaseService $db;
    protected AuthService $auth;
    protected SessionService $session;
    protected Config $config;
    protected Environment $twig;
    protected Logger $logger;

    public function __construct(App $app)
    {
        $this->app = $app;
        $this->db = $app->getDatabase();
        $this->auth = $app->getAuth();
        $this->session = $app->getSession();
        $this->config = $app->getConfig();
        $this->twig = $app->getTwig();
        $this->logger = $app->getLogger();
    }

    protected function render(string $template, array $data = []): void
    {
        // Add common template variables
        $data = array_merge($data, [
            'current_user' => $this->auth->getCurrentUser(),
            'is_logged_in' => $this->auth->isLoggedIn(),
            'is_admin' => $this->auth->isAdmin(),
            'csrf_token' => $this->session->getCsrfToken(),
            'flash_messages' => $this->getFlashMessages(),
            'current_url' => $_SERVER['REQUEST_URI'] ?? '/',
        ]);

        // Update app global with current user
        $this->twig->addGlobal('app', [
            'user' => $this->auth->getCurrentUser(),
            'request' => [
                'uri' => $_SERVER['REQUEST_URI'] ?? '/',
                'method' => $_SERVER['REQUEST_METHOD'] ?? 'GET'
            ]
        ]);

        echo $this->twig->render($template, $data);
    }

    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
    }

    protected function redirect(string $url, int $statusCode = 302): void
    {
        http_response_code($statusCode);
        header("Location: {$url}");
        exit;
    }

    protected function getInput(): array
    {
        $input = [];
        
        // Get POST data
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $input = array_merge($input, $_POST);
        }
        
        // Get JSON data
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            $json = json_decode(file_get_contents('php://input'), true);
            if ($json) {
                $input = array_merge($input, $json);
            }
        }
        
        // Get query parameters
        $input = array_merge($input, $_GET);
        
        return $input;
    }

    protected function validateCsrf(): bool
    {
        $token = $this->getInput()['csrf_token'] ?? '';
        return $this->session->validateCsrfToken($token);
    }

    protected function requireCsrf(): void
    {
        if (!$this->validateCsrf()) {
            $this->json(['error' => 'Invalid CSRF token'], 403);
            exit;
        }
    }

    protected function requireAuth(): void
    {
        $this->auth->requireAuth();
    }

    protected function requireAdmin(): void
    {
        $this->auth->requireAdmin();
    }

    protected function setFlash(string $type, string $message): void
    {
        $this->session->flash($type, $message);
    }

    protected function getFlashMessages(): array
    {
        $messages = [];
        $types = ['success', 'error', 'warning', 'info'];
        
        foreach ($types as $type) {
            if ($this->session->hasFlash($type)) {
                $messages[$type] = $this->session->getFlash($type);
            }
        }
        
        return $messages;
    }

    protected function isAjax(): bool
    {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    protected function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    protected function logActivity(string $action, array $data = []): void
    {
        $this->logger->info($action, array_merge($data, [
            'user_id' => $this->auth->getCurrentUser()['id'] ?? null,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'method' => $_SERVER['REQUEST_METHOD'] ?? ''
        ]));
    }

    protected function validateInput(array $rules, array $input): array
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $input[$field] ?? null;
            
            if (isset($rule['required']) && $rule['required'] && empty($value)) {
                $errors[$field] = $rule['message'] ?? "{$field} is required";
                continue;
            }
            
            if (!empty($value) && isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $errors[$field] = $rule['message'] ?? "Invalid {$field}";
                        }
                        break;
                    case 'url':
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $errors[$field] = $rule['message'] ?? "Invalid {$field}";
                        }
                        break;
                    case 'int':
                        if (!filter_var($value, FILTER_VALIDATE_INT)) {
                            $errors[$field] = $rule['message'] ?? "{$field} must be a number";
                        }
                        break;
                }
            }
            
            if (!empty($value) && isset($rule['min_length'])) {
                if (strlen($value) < $rule['min_length']) {
                    $errors[$field] = $rule['message'] ?? "{$field} must be at least {$rule['min_length']} characters";
                }
            }
            
            if (!empty($value) && isset($rule['max_length'])) {
                if (strlen($value) > $rule['max_length']) {
                    $errors[$field] = $rule['message'] ?? "{$field} must be no more than {$rule['max_length']} characters";
                }
            }
        }
        
        return $errors;
    }
}
