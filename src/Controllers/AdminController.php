<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Models\User;

class AdminController extends BaseController
{
    private User $userModel;

    public function __construct($app)
    {
        parent::__construct($app);
        $this->userModel = new User($this->db);
    }

    public function index(): void
    {
        $this->requireAdmin();
        
        // Get system statistics
        $stats = [
            'users' => $this->userModel->getUserStats(),
            'domains' => [
                'total' => $this->db->count('domains'),
                'verified' => $this->db->count('domains', ['verified' => true])
            ],
            'urls' => [
                'total' => $this->db->count('url_submissions'),
                'indexed' => $this->db->count('url_submissions', ['status' => 'indexed']),
                'pending' => $this->db->count('url_submissions', ['status' => 'pending']),
                'failed' => $this->db->count('url_submissions', ['status' => 'failed'])
            ]
        ];

        $this->render('admin/index.html.twig', [
            'title' => 'Admin Dashboard',
            'stats' => $stats
        ]);
    }

    public function users(): void
    {
        $this->requireAdmin();
        
        $page = (int)($_GET['page'] ?? 1);
        $result = $this->userModel->getAllUsers($page, 20);

        $this->render('admin/users.html.twig', [
            'title' => 'User Management',
            'users' => $result['data'],
            'pagination' => $result['pagination']
        ]);
    }

    public function domains(): void
    {
        $this->requireAdmin();
        
        $page = (int)($_GET['page'] ?? 1);
        
        $result = $this->db->paginate(
            'SELECT d.*, u.username, u.email FROM domains d 
             JOIN users u ON d.user_id = u.id 
             ORDER BY d.created_at DESC',
            [],
            $page,
            20
        );

        $this->render('admin/domains.html.twig', [
            'title' => 'Domain Management',
            'domains' => $result['data'],
            'pagination' => $result['pagination']
        ]);
    }

    public function logs(): void
    {
        $this->requireAdmin();
        
        $page = (int)($_GET['page'] ?? 1);
        
        $result = $this->db->paginate(
            'SELECT sl.*, u.username FROM system_logs sl 
             LEFT JOIN users u ON sl.user_id = u.id 
             ORDER BY sl.created_at DESC',
            [],
            $page,
            50
        );

        $this->render('admin/logs.html.twig', [
            'title' => 'System Logs',
            'logs' => $result['data'],
            'pagination' => $result['pagination']
        ]);
    }

    public function settings(): void
    {
        $this->requireAdmin();
        
        // Get system settings
        $settings = $this->db->fetchAll('SELECT * FROM system_settings ORDER BY setting_key');

        $this->render('admin/settings.html.twig', [
            'title' => 'System Settings',
            'settings' => $settings
        ]);
    }

    // Action methods
    
    public function blockUser(): void
    {
        $this->requireAdmin();
        $this->requireCsrf();
        
        $input = $this->getInput();
        $userId = (int)($input['user_id'] ?? 0);
        
        if ($this->userModel->blockUser($userId)) {
            $this->logActivity('Admin blocked user', ['blocked_user_id' => $userId]);
            $this->json(['success' => true, 'message' => 'User blocked successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to block user'], 400);
        }
    }

    public function unblockUser(): void
    {
        $this->requireAdmin();
        $this->requireCsrf();
        
        $input = $this->getInput();
        $userId = (int)($input['user_id'] ?? 0);
        
        if ($this->userModel->unblockUser($userId)) {
            $this->logActivity('Admin unblocked user', ['unblocked_user_id' => $userId]);
            $this->json(['success' => true, 'message' => 'User unblocked successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to unblock user'], 400);
        }
    }

    public function deleteUser(string $id): void
    {
        $this->requireAdmin();
        $this->requireCsrf();
        
        $userId = (int)$id;
        $currentUser = $this->auth->getCurrentUser();
        
        // Prevent admin from deleting themselves
        if ($userId === $currentUser['id']) {
            $this->json(['success' => false, 'message' => 'Cannot delete your own account'], 400);
            return;
        }
        
        if ($this->userModel->delete($userId)) {
            $this->logActivity('Admin deleted user', ['deleted_user_id' => $userId]);
            $this->json(['success' => true, 'message' => 'User deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to delete user'], 400);
        }
    }

    public function saveSettings(): void
    {
        $this->requireAdmin();
        $this->requireCsrf();
        
        $input = $this->getInput();
        
        try {
            foreach ($input['settings'] ?? [] as $key => $value) {
                $this->db->update('system_settings', 
                    ['setting_value' => $value], 
                    ['setting_key' => $key]
                );
            }
            
            $this->logActivity('Admin updated system settings');
            $this->setFlash('success', 'Settings saved successfully');
        } catch (\Exception $e) {
            $this->setFlash('error', 'Failed to save settings');
        }
        
        $this->redirect('/admin/settings');
    }

    public function security(): void
    {
        $this->requireAuth();
        $this->requireAdmin();

        // Get security statistics
        $securityStats = $this->getSecurityStatistics();

        $this->render('admin/security.html.twig', [
            'title' => 'Security Monitoring',
            'stats' => $securityStats
        ]);
    }

    private function getSecurityStatistics(): array
    {
        $stats = [];

        // Recent security events (last 24 hours)
        $stats['recent_events'] = $this->db->fetchAll(
            'SELECT event_type, COUNT(*) as count, severity
             FROM security_logs
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
             GROUP BY event_type, severity
             ORDER BY count DESC
             LIMIT 10'
        );

        // Blocked IPs
        $stats['blocked_ips'] = $this->db->fetchAll(
            'SELECT ip_address, reason, created_at, expires_at,
                    CASE
                        WHEN expires_at IS NULL THEN "Permanent"
                        WHEN expires_at > NOW() THEN "Active"
                        ELSE "Expired"
                    END as status
             FROM blocked_ips
             ORDER BY created_at DESC
             LIMIT 20'
        );

        // Failed login attempts (last 24 hours)
        $stats['failed_logins'] = $this->db->fetchAll(
            'SELECT identifier, ip_address, COUNT(*) as attempts, MAX(created_at) as last_attempt
             FROM login_attempts
             WHERE success = FALSE AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
             GROUP BY identifier, ip_address
             HAVING attempts >= 3
             ORDER BY attempts DESC
             LIMIT 20'
        );

        // Rate limit violations
        $stats['rate_violations'] = $this->db->fetchAll(
            'SELECT identifier, action, ip_address, COUNT(*) as violations
             FROM rate_limits
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
             GROUP BY identifier, action, ip_address
             HAVING violations > 50
             ORDER BY violations DESC
             LIMIT 20'
        );

        // Security metrics
        $stats['metrics'] = [
            'total_events_24h' => (int)($this->db->fetch(
                'SELECT COUNT(*) as count FROM security_logs WHERE created_at >= ?',
                [date('Y-m-d H:i:s', time() - 86400)]
            )['count'] ?? 0),
            'high_severity_events_24h' => (int)($this->db->fetch(
                'SELECT COUNT(*) as count FROM security_logs WHERE created_at >= ? AND severity = ?',
                [date('Y-m-d H:i:s', time() - 86400), 'high']
            )['count'] ?? 0),
            'active_blocked_ips' => (int)($this->db->fetch(
                'SELECT COUNT(*) as count FROM blocked_ips WHERE expires_at > ? OR expires_at IS NULL',
                [date('Y-m-d H:i:s')]
            )['count'] ?? 0),
            'failed_logins_24h' => (int)($this->db->fetch(
                'SELECT COUNT(*) as count FROM login_attempts WHERE success = ? AND created_at >= ?',
                [false, date('Y-m-d H:i:s', time() - 86400)]
            )['count'] ?? 0),
            'locked_accounts' => (int)($this->db->fetch(
                'SELECT COUNT(*) as count FROM users WHERE locked_until > ?',
                [date('Y-m-d H:i:s')]
            )['count'] ?? 0)
        ];

        return $stats;
    }
}
