<?php

declare(strict_types=1);

namespace App\Controllers;

class ApiController extends BaseController
{
    public function getUrlStatus(): void
    {
        $this->requireAuth();

        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();

        $url = $input['url'] ?? '';

        if (empty($url)) {
            $this->json(['error' => 'URL parameter is required'], 400);
            return;
        }

        try {
            // Use the URL submission service to check status
            $urlSubmissionService = new \App\Services\UrlSubmissionService($this->db, $this->config, $this->logger);
            $result = $urlSubmissionService->checkUrlStatus($user['id'], $url);

            if ($result['success']) {
                $this->json([
                    'success' => true,
                    'url' => $url,
                    'results' => $result['results']
                ]);
            } else {
                $this->json(['error' => 'URL status not found'], 404);
            }

        } catch (\Exception $e) {
            $this->logger->error('Failed to get URL status', [
                'user_id' => $user['id'],
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            $this->json(['error' => 'Failed to check URL status'], 500);
        }
    }

    public function checkUrls(): void
    {
        $this->requireAuth();
        $this->requireCsrf();
        
        $input = $this->getInput();
        $user = $this->auth->getCurrentUser();
        
        $urls = $input['urls'] ?? [];
        
        if (empty($urls) || !is_array($urls)) {
            $this->json(['error' => 'URLs array is required'], 400);
            return;
        }
        
        $results = [];
        
        foreach ($urls as $url) {
            $status = $this->db->fetch(
                'SELECT status, google_status, bing_status, last_checked FROM url_submissions 
                 WHERE url = ? AND user_id = ?',
                [$url, $user['id']]
            );
            
            $results[] = [
                'url' => $url,
                'status' => $status['status'] ?? 'not_found',
                'google_status' => $status['google_status'] ?? null,
                'bing_status' => $status['bing_status'] ?? null,
                'last_checked' => $status['last_checked'] ?? null
            ];
        }
        
        $this->json(['results' => $results]);
    }

    public function getQuotas(): void
    {
        $this->requireAuth();
        
        $user = $this->auth->getCurrentUser();
        $today = date('Y-m-d');
        
        // Get today's API usage
        $googleUsage = $this->db->fetch(
            'SELECT SUM(requests_count) as total FROM api_usage 
             WHERE user_id = ? AND provider = "google" AND date = ?',
            [$user['id'], $today]
        )['total'] ?? 0;
        
        $bingUsage = $this->db->fetch(
            'SELECT SUM(requests_count) as total FROM api_usage 
             WHERE user_id = ? AND provider = "bing" AND date = ?',
            [$user['id'], $today]
        )['total'] ?? 0;
        
        // Get quota limits from settings
        $googleQuota = $this->db->fetch(
            'SELECT setting_value FROM system_settings WHERE setting_key = "google_daily_quota"'
        )['setting_value'] ?? 200;
        
        $bingQuota = $this->db->fetch(
            'SELECT setting_value FROM system_settings WHERE setting_key = "bing_daily_quota"'
        )['setting_value'] ?? 10000;
        
        $this->json([
            'google' => [
                'used' => (int)$googleUsage,
                'limit' => (int)$googleQuota,
                'remaining' => max(0, (int)$googleQuota - (int)$googleUsage)
            ],
            'bing' => [
                'used' => (int)$bingUsage,
                'limit' => (int)$bingQuota,
                'remaining' => max(0, (int)$bingQuota - (int)$bingUsage)
            ]
        ]);
    }
}
