<?php

try {
    $dsn = "mysql:host=127.0.0.1;port=8889;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', 'root', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "Connection successful!\n";
    
    // Show all databases
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll();
    echo "Available databases:\n";
    foreach ($databases as $db) {
        echo "- " . $db['Database'] . "\n";
    }

    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'indexation_tool'");
    $result = $stmt->fetch();

    if ($result) {
        echo "Database 'indexation_tool' exists.\n";
    } else {
        echo "Database 'indexation_tool' does not exist. Creating it...\n";
        $pdo->exec("CREATE DATABASE indexation_tool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "Database created successfully.\n";
    }

    // Now connect to the specific database
    $dsn = "mysql:host=127.0.0.1;port=8889;dbname=indexation_tool;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', 'root', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    echo "Connected to indexation_tool database successfully!\n";
    
} catch (PDOException $e) {
    echo "Connection failed: " . $e->getMessage() . "\n";
}
