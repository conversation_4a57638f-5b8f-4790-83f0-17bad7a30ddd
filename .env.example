# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=seo_indexer
DB_USER=root
DB_PASS=

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost
APP_NAME="SEO Indexer Platform"
APP_SECRET_KEY=your-secret-key-here

# Session Configuration
SESSION_LIFETIME=7200
SESSION_SECURE=false
SESSION_HTTPONLY=true

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost/auth/google/callback

# Bing/Microsoft OAuth2 Configuration
BING_CLIENT_ID=your-bing-client-id
BING_CLIENT_SECRET=your-bing-client-secret
BING_REDIRECT_URI=http://localhost/auth/bing/callback
BING_API_KEY=your-bing-api-key

# Email Configuration (Optional)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="SEO Indexer Platform"

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# API Quotas
GOOGLE_DAILY_QUOTA=200
BING_DAILY_QUOTA=10000
