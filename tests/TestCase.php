<?php

declare(strict_types=1);

namespace Tests;

use PHPUnit\Framework\TestCase as BaseTestCase;
use App\Services\DatabaseService;
use App\Services\AuthService;
use App\Services\SecurityService;
use App\Services\SessionService;
use App\Utils\Config;
use Monolog\Logger;
use Monolog\Handler\NullHandler;

abstract class TestCase extends BaseTestCase
{
    protected DatabaseService $db;
    protected Config $config;
    protected Logger $logger;
    protected AuthService $auth;
    protected SecurityService $security;
    protected SessionService $session;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize core services
        $this->config = new Config();
        $this->logger = new Logger('test');
        $this->logger->pushHandler(new NullHandler()); // Suppress logs during testing
        
        $this->db = new DatabaseService($this->config);
        $this->session = new SessionService($this->config);
        $this->auth = new AuthService($this->db, $this->session, $this->logger);
        $this->security = new SecurityService($this->db, $this->config, $this->logger);
        
        // Clean database before each test
        $this->cleanDatabase();
    }

    protected function tearDown(): void
    {
        // Clean database after each test
        $this->cleanDatabase();
        
        // Clear any mock responses
        MockHttpClient::clearResponses();
        
        parent::tearDown();
    }

    protected function cleanDatabase(): void
    {
        cleanupTestDatabase();
    }

    protected function createUser(array $overrides = []): array
    {
        $userData = createTestUser($overrides);
        $userId = $this->db->insert('users', $userData);
        $userData['id'] = $userId;
        return $userData;
    }

    protected function createDomain(int $userId, array $overrides = []): array
    {
        $domainData = createTestDomain($userId, $overrides);
        $domainId = $this->db->insert('domains', $domainData);
        $domainData['id'] = $domainId;
        return $domainData;
    }

    protected function createUrlSubmission(int $userId, int $domainId, array $overrides = []): array
    {
        $submissionData = createTestUrlSubmission($userId, $domainId, $overrides);
        $submissionId = $this->db->insert('url_submissions', $submissionData);
        $submissionData['id'] = $submissionId;
        return $submissionData;
    }

    protected function loginUser(array $user): void
    {
        $this->session->createUserSession($user['id']);
    }

    protected function logoutUser(): void
    {
        $this->session->destroySession();
    }

    protected function assertDatabaseHas(string $table, array $conditions): void
    {
        assertDatabaseHas($table, $conditions);
    }

    protected function assertDatabaseMissing(string $table, array $conditions): void
    {
        assertDatabaseMissing($table, $conditions);
    }

    protected function assertDatabaseCount(string $table, int $expectedCount, array $conditions = []): void
    {
        assertDatabaseCount($table, $expectedCount, $conditions);
    }

    protected function mockHttpResponse(string $url, array $response): void
    {
        MockHttpClient::addResponse($url, $response);
    }

    protected function simulateRequest(string $method, string $path, array $data = [], array $headers = []): array
    {
        // Backup original server variables
        $originalServer = $_SERVER;
        $originalPost = $_POST;
        $originalGet = $_GET;

        try {
            // Set up request environment
            $_SERVER['REQUEST_METHOD'] = $method;
            $_SERVER['REQUEST_URI'] = $path;
            $_SERVER['HTTP_HOST'] = 'localhost';
            $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
            $_SERVER['HTTP_USER_AGENT'] = 'PHPUnit Test';

            // Set headers
            foreach ($headers as $key => $value) {
                $_SERVER['HTTP_' . strtoupper(str_replace('-', '_', $key))] = $value;
            }

            // Set request data
            if ($method === 'GET') {
                $_GET = $data;
            } else {
                $_POST = $data;
            }

            // Capture output
            ob_start();
            
            // Here you would normally dispatch the request through your router
            // For now, we'll return a mock response structure
            $response = [
                'status' => 200,
                'headers' => [],
                'body' => ob_get_contents()
            ];
            
            ob_end_clean();
            
            return $response;

        } finally {
            // Restore original server variables
            $_SERVER = $originalServer;
            $_POST = $originalPost;
            $_GET = $originalGet;
        }
    }

    protected function assertResponseStatus(array $response, int $expectedStatus): void
    {
        $this->assertEquals($expectedStatus, $response['status'], 
            "Expected response status {$expectedStatus}, got {$response['status']}");
    }

    protected function assertResponseContains(array $response, string $needle): void
    {
        $this->assertStringContainsString($needle, $response['body'],
            "Response body does not contain expected string: {$needle}");
    }

    protected function assertResponseJson(array $response): array
    {
        $decoded = json_decode($response['body'], true);
        $this->assertNotNull($decoded, "Response body is not valid JSON");
        return $decoded;
    }

    protected function assertValidationError(array $response, string $field): void
    {
        $json = $this->assertResponseJson($response);
        $this->assertArrayHasKey('errors', $json);
        $this->assertArrayHasKey($field, $json['errors']);
    }

    protected function assertSecurityBlocked(array $response): void
    {
        $this->assertContains($response['status'], [403, 429], 
            "Expected security block response (403 or 429)");
    }

    protected function generateCsrfToken(): string
    {
        return $this->session->generateCsrfToken();
    }

    protected function withCsrfToken(array $data): array
    {
        $data['csrf_token'] = $this->generateCsrfToken();
        return $data;
    }

    protected function actingAs(array $user): self
    {
        $this->loginUser($user);
        return $this;
    }

    protected function assertExecutionTimeUnder(callable $callback, float $maxSeconds): void
    {
        assertExecutionTimeUnder($callback, $maxSeconds);
    }

    protected function measureExecutionTime(callable $callback): float
    {
        return measureExecutionTime($callback);
    }

    protected function createOAuthToken(int $userId, string $provider, array $overrides = []): array
    {
        $tokenData = array_merge([
            'user_id' => $userId,
            'provider' => $provider,
            'access_token' => 'encrypted_access_token_' . uniqid(),
            'refresh_token' => 'encrypted_refresh_token_' . uniqid(),
            'token_type' => 'Bearer',
            'expires_at' => date('Y-m-d H:i:s', time() + 3600),
            'scope' => 'https://www.googleapis.com/auth/webmasters',
            'created_at' => date('Y-m-d H:i:s')
        ], $overrides);

        $tokenId = $this->db->insert('oauth_tokens', $tokenData);
        $tokenData['id'] = $tokenId;
        return $tokenData;
    }

    protected function createApiUsage(int $userId, string $provider, array $overrides = []): array
    {
        $usageData = array_merge([
            'user_id' => $userId,
            'provider' => $provider,
            'endpoint' => 'test_endpoint',
            'requests_count' => 1,
            'date' => date('Y-m-d'),
            'created_at' => date('Y-m-d H:i:s')
        ], $overrides);

        $usageId = $this->db->insert('api_usage', $usageData);
        $usageData['id'] = $usageId;
        return $usageData;
    }

    protected function createSecurityLog(array $overrides = []): array
    {
        $logData = array_merge([
            'event_type' => 'test_event',
            'ip_address' => '127.0.0.1',
            'user_agent' => 'PHPUnit Test',
            'user_id' => null,
            'event_data' => json_encode(['test' => true]),
            'severity' => 'info',
            'created_at' => date('Y-m-d H:i:s')
        ], $overrides);

        $logId = $this->db->insert('security_logs', $logData);
        $logData['id'] = $logId;
        return $logData;
    }

    protected function assertSecurityEventLogged(string $eventType, ?int $userId = null): void
    {
        $conditions = ['event_type' => $eventType];
        if ($userId !== null) {
            $conditions['user_id'] = $userId;
        }

        $this->assertDatabaseHas('security_logs', $conditions);
    }

    protected function assertRateLimitRecorded(string $identifier, string $action): void
    {
        $this->assertDatabaseHas('rate_limits', [
            'identifier' => $identifier,
            'action' => $action
        ]);
    }
}
