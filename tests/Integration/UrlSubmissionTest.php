<?php

declare(strict_types=1);

namespace Tests\Integration;

use Tests\TestCase;
use App\Services\UrlSubmissionService;
use App\Services\GoogleSearchConsoleService;
use App\Services\BingWebmasterService;
use App\Services\GoogleOAuthService;
use App\Services\BingOAuthService;

class UrlSubmissionTest extends TestCase
{
    private UrlSubmissionService $urlSubmissionService;

    protected function setUp(): void
    {
        parent::setUp();

        // Initialize OAuth services
        $googleOAuth = new GoogleOAuthService($this->db, $this->session, $this->config, $this->logger);
        $bingOAuth = new BingOAuthService($this->db, $this->session, $this->config, $this->logger);

        // Initialize API services
        $googleService = new GoogleSearchConsoleService($googleOAuth, $this->db, $this->config, $this->logger);
        $bingService = new BingWebmasterService($bingOAuth, $this->db, $this->config, $this->logger);

        // Initialize URL submission service
        $this->urlSubmissionService = new UrlSubmissionService($this->db, $this->config, $this->logger);
    }

    public function testSubmitSingleUrlToGoogle(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'google');

        // Mock successful Google API response
        $this->mockHttpResponse('https://indexing.googleapis.com/v3/urlNotifications:publish', [
            'status' => 200,
            'body' => json_encode(['urlNotificationMetadata' => ['url' => 'https://example.com/test']])
        ]);

        $url = 'https://example.com/test-page';
        $result = $this->urlSubmissionService->submitUrl($user['id'], $domain['id'], $url, ['google']);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('results', $result);
        $this->assertArrayHasKey('google', $result['results']);

        // Assert submission was recorded in database
        $this->assertDatabaseHas('url_submissions', [
            'user_id' => $user['id'],
            'domain_id' => $domain['id'],
            'url' => $url,
            'provider' => 'google',
            'status' => 'submitted'
        ]);
    }

    public function testSubmitSingleUrlToBing(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'bing');

        // Mock successful Bing API response
        $this->mockHttpResponse('https://ssl.bing.com/webmaster/api.svc/json/SubmitUrl', [
            'status' => 200,
            'body' => json_encode(['d' => 'Success'])
        ]);

        $url = 'https://example.com/test-page';
        $result = $this->urlSubmissionService->submitUrl($user['id'], $domain['id'], $url, ['bing']);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('results', $result);
        $this->assertArrayHasKey('bing', $result['results']);

        // Assert submission was recorded in database
        $this->assertDatabaseHas('url_submissions', [
            'user_id' => $user['id'],
            'domain_id' => $domain['id'],
            'url' => $url,
            'provider' => 'bing',
            'status' => 'submitted'
        ]);
    }

    public function testSubmitUrlToBothProviders(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'google');
        $this->createOAuthToken($user['id'], 'bing');

        // Mock successful responses for both providers
        $this->mockHttpResponse('https://indexing.googleapis.com/v3/urlNotifications:publish', [
            'status' => 200,
            'body' => json_encode(['urlNotificationMetadata' => ['url' => 'https://example.com/test']])
        ]);
        $this->mockHttpResponse('https://ssl.bing.com/webmaster/api.svc/json/SubmitUrl', [
            'status' => 200,
            'body' => json_encode(['d' => 'Success'])
        ]);

        $url = 'https://example.com/test-page';
        $result = $this->urlSubmissionService->submitUrl($user['id'], $domain['id'], $url, ['google', 'bing']);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('google', $result['results']);
        $this->assertArrayHasKey('bing', $result['results']);

        // Assert submissions were recorded for both providers
        $this->assertDatabaseHas('url_submissions', [
            'user_id' => $user['id'],
            'url' => $url,
            'provider' => 'google'
        ]);
        $this->assertDatabaseHas('url_submissions', [
            'user_id' => $user['id'],
            'url' => $url,
            'provider' => 'bing'
        ]);
    }

    public function testSubmitUrlWithoutOAuthToken(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        // No OAuth token created

        $url = 'https://example.com/test-page';
        $result = $this->urlSubmissionService->submitUrl($user['id'], $domain['id'], $url, ['google']);

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('google', $result['results']);
        $this->assertStringContainsString('not connected', $result['results']['google']['error']);
    }

    public function testSubmitUrlWithInvalidDomain(): void
    {
        $user = $this->createUser();
        $invalidDomainId = 99999;

        $url = 'https://example.com/test-page';
        $result = $this->urlSubmissionService->submitUrl($user['id'], $invalidDomainId, $url, ['google']);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Domain not found', $result['error']);
    }

    public function testBatchUrlSubmission(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'google');

        // Mock successful Google API responses
        for ($i = 1; $i <= 3; $i++) {
            $this->mockHttpResponse('https://indexing.googleapis.com/v3/urlNotifications:publish', [
                'status' => 200,
                'body' => json_encode(['urlNotificationMetadata' => ['url' => "https://example.com/page{$i}"]])
            ]);
        }

        $urls = [
            'https://example.com/page1',
            'https://example.com/page2',
            'https://example.com/page3'
        ];

        $result = $this->urlSubmissionService->submitUrlBatch($user['id'], $domain['id'], $urls, ['google']);

        $this->assertTrue($result['success']);
        $this->assertEquals(3, $result['summary']['total']);
        $this->assertEquals(3, $result['summary']['successful']);
        $this->assertEquals(0, $result['summary']['failed']);

        // Assert all URLs were recorded
        foreach ($urls as $url) {
            $this->assertDatabaseHas('url_submissions', [
                'user_id' => $user['id'],
                'url' => $url,
                'provider' => 'google'
            ]);
        }
    }

    public function testUrlValidation(): void
    {
        $validUrl = 'https://example.com/valid-page';
        $invalidUrl = 'not-a-url';

        $validResult = $this->urlSubmissionService->validateUrl($validUrl);
        $this->assertTrue($validResult['valid']);
        $this->assertEmpty($validResult['errors']);

        $invalidResult = $this->urlSubmissionService->validateUrl($invalidUrl);
        $this->assertFalse($invalidResult['valid']);
        $this->assertNotEmpty($invalidResult['errors']);
    }

    public function testBatchUrlValidation(): void
    {
        $urls = [
            'https://example.com/valid1',
            'https://example.com/valid2',
            'not-a-url',
            'ftp://example.com/invalid-protocol'
        ];

        $result = $this->urlSubmissionService->validateUrlBatch($urls);

        $this->assertCount(2, $result['valid_urls']);
        $this->assertCount(2, $result['invalid_urls']);
        $this->assertEquals(4, $result['summary']['total']);
        $this->assertEquals(2, $result['summary']['valid']);
        $this->assertEquals(2, $result['summary']['invalid']);
    }

    public function testGetSubmissionHistory(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);

        // Create some test submissions
        for ($i = 1; $i <= 5; $i++) {
            $this->createUrlSubmission($user['id'], $domain['id'], [
                'url' => "https://example.com/page{$i}",
                'provider' => $i % 2 === 0 ? 'bing' : 'google'
            ]);
        }

        $result = $this->urlSubmissionService->getSubmissionHistory($user['id'], 1, 10);

        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('pagination', $result);
        $this->assertCount(5, $result['data']);

        // Check that domain information is included
        foreach ($result['data'] as $submission) {
            $this->assertArrayHasKey('domain', $submission);
            $this->assertArrayHasKey('protocol', $submission);
        }
    }

    public function testGetSubmissionStats(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);

        // Create test submissions with different statuses and providers
        $this->createUrlSubmission($user['id'], $domain['id'], ['provider' => 'google', 'status' => 'submitted']);
        $this->createUrlSubmission($user['id'], $domain['id'], ['provider' => 'google', 'status' => 'failed']);
        $this->createUrlSubmission($user['id'], $domain['id'], ['provider' => 'bing', 'status' => 'submitted']);

        $stats = $this->urlSubmissionService->getSubmissionStats($user['id']);

        $this->assertEquals(3, $stats['total']);
        $this->assertEquals(2, $stats['google']);
        $this->assertEquals(1, $stats['bing']);
        $this->assertEquals(2, $stats['by_status']['submitted']);
        $this->assertEquals(1, $stats['by_status']['failed']);
    }

    public function testCheckUrlStatus(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'google');

        $url = 'https://example.com/test-page';
        $this->createUrlSubmission($user['id'], $domain['id'], [
            'url' => $url,
            'provider' => 'google',
            'status' => 'submitted'
        ]);

        // Mock Google API response for status check
        $this->mockHttpResponse('https://indexing.googleapis.com/v3/urlNotifications/metadata', [
            'status' => 200,
            'body' => json_encode([
                'urlNotificationMetadata' => [
                    'latestUpdate' => ['type' => 'URL_UPDATED']
                ]
            ])
        ]);

        $result = $this->urlSubmissionService->checkUrlStatus($user['id'], $url);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('results', $result);
    }

    public function testGetConnectedProviders(): void
    {
        $user = $this->createUser();
        $this->createOAuthToken($user['id'], 'google');
        $this->createOAuthToken($user['id'], 'bing');

        $providers = $this->urlSubmissionService->getConnectedProviders($user['id']);

        $this->assertArrayHasKey('google', $providers);
        $this->assertArrayHasKey('bing', $providers);
        $this->assertTrue($providers['google']);
        $this->assertTrue($providers['bing']);
    }

    public function testApiUsageTracking(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'google');

        // Mock successful Google API response
        $this->mockHttpResponse('https://indexing.googleapis.com/v3/urlNotifications:publish', [
            'status' => 200,
            'body' => json_encode(['urlNotificationMetadata' => ['url' => 'https://example.com/test']])
        ]);

        $url = 'https://example.com/test-page';
        $this->urlSubmissionService->submitUrl($user['id'], $domain['id'], $url, ['google']);

        // Assert API usage was tracked
        $this->assertDatabaseHas('api_usage', [
            'user_id' => $user['id'],
            'provider' => 'google',
            'date' => date('Y-m-d')
        ]);
    }

    public function testPerformanceOfBatchSubmission(): void
    {
        $user = $this->createUser();
        $domain = $this->createDomain($user['id']);
        $this->createOAuthToken($user['id'], 'google');

        // Generate 10 URLs for batch submission
        $urls = [];
        for ($i = 1; $i <= 10; $i++) {
            $urls[] = "https://example.com/page{$i}";
            // Mock response for each URL
            $this->mockHttpResponse('https://indexing.googleapis.com/v3/urlNotifications:publish', [
                'status' => 200,
                'body' => json_encode(['urlNotificationMetadata' => ['url' => "https://example.com/page{$i}"]])
            ]);
        }

        // Measure execution time
        $executionTime = $this->measureExecutionTime(function() use ($user, $domain, $urls) {
            $this->urlSubmissionService->submitUrlBatch($user['id'], $domain['id'], $urls, ['google']);
        });

        // Assert batch submission completes within reasonable time (5 seconds)
        $this->assertLessThan(5.0, $executionTime, 'Batch submission took too long');
    }
}
