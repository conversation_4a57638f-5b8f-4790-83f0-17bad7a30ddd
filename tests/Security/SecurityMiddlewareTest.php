<?php

declare(strict_types=1);

namespace Tests\Security;

use Tests\TestCase;
use App\Middleware\SecurityMiddleware;

class SecurityMiddlewareTest extends TestCase
{
    private SecurityMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->middleware = new SecurityMiddleware($this->security, $this->session, $this->config, $this->logger);
    }

    public function testAllowsNormalRequest(): void
    {
        $result = $this->middleware->handle('GET', '/dashboard');

        $this->assertNull($result); // Null means request is allowed to continue
    }

    public function testBlocksRequestFromBlockedIp(): void
    {
        $ipAddress = '*************';
        $this->security->blockIp($ipAddress, 'Test block');

        // Simulate request from blocked IP
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        $result = $this->middleware->handle('GET', '/dashboard');

        $this->assertNotNull($result);
        $this->assertEquals(403, $result['status']);
        $this->assertStringContainsString('blocked', $result['error']);
    }

    public function testRateLimitingForApiEndpoints(): void
    {
        $ipAddress = '127.0.0.1';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        // Make requests up to the API limit (100 per hour)
        for ($i = 0; $i < 100; $i++) {
            $this->security->recordAttempt("ip:{$ipAddress}", 'get:/api/test');
        }

        $result = $this->middleware->handle('GET', '/api/test');

        $this->assertNotNull($result);
        $this->assertEquals(429, $result['status']);
        $this->assertStringContainsString('Too many requests', $result['error']);
        $this->assertArrayHasKey('X-RateLimit-Limit', $result['headers']);
    }

    public function testRateLimitingForAuthEndpoints(): void
    {
        $ipAddress = '127.0.0.1';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        // Make requests up to the auth limit (10 per 15 minutes)
        for ($i = 0; $i < 10; $i++) {
            $this->security->recordAttempt("ip:{$ipAddress}", 'post:/auth/login');
        }

        $result = $this->middleware->handle('POST', '/auth/login');

        $this->assertNotNull($result);
        $this->assertEquals(429, $result['status']);
    }

    public function testCsrfProtectionForPostRequests(): void
    {
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_POST = ['data' => 'test'];
        // No CSRF token provided

        $result = $this->middleware->handle('POST', '/dashboard/test');

        $this->assertNotNull($result);
        $this->assertEquals(403, $result['status']);
        $this->assertStringContainsString('security token', $result['error']);
    }

    public function testAllowsPostRequestWithValidCsrfToken(): void
    {
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $token = $this->session->generateCsrfToken();
        $_POST = ['csrf_token' => $token, 'data' => 'test'];

        $result = $this->middleware->handle('POST', '/dashboard/test');

        $this->assertNull($result); // Should be allowed
    }

    public function testDetectsSqlInjectionAttempt(): void
    {
        $maliciousPath = "/dashboard/test?id=1'; DROP TABLE users; --";

        $result = $this->middleware->handle('GET', $maliciousPath);

        // Should log suspicious activity
        $this->assertSecurityEventLogged('suspicious_activity');
    }

    public function testDetectsXssAttempt(): void
    {
        $_GET['search'] = '<script>alert("XSS")</script>';

        $result = $this->middleware->handle('GET', '/dashboard/search');

        // Should log suspicious activity
        $this->assertSecurityEventLogged('suspicious_activity');
    }

    public function testDetectsSuspiciousUserAgent(): void
    {
        $_SERVER['HTTP_USER_AGENT'] = 'sqlmap/1.0';

        $result = $this->middleware->handle('GET', '/dashboard');

        // Should log suspicious activity
        $this->assertSecurityEventLogged('suspicious_activity');
    }

    public function testAutoBlocksAfterMultipleSuspiciousActivities(): void
    {
        $ipAddress = '*************';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;
        $_SERVER['HTTP_USER_AGENT'] = 'sqlmap/1.0'; // Suspicious user agent

        // Make multiple suspicious requests
        for ($i = 0; $i < 3; $i++) {
            $this->middleware->handle('GET', "/dashboard/test?id='; DROP TABLE users; --");
        }

        // IP should now be blocked
        $this->assertTrue($this->security->isIpBlocked($ipAddress));
    }

    public function testSanitizesInputData(): void
    {
        $_GET['search'] = '<script>alert("XSS")</script>';
        $_POST['comment'] = '<img src=x onerror=alert(1)>';

        $this->middleware->handle('POST', '/dashboard/test');

        // Input should be sanitized
        $this->assertStringNotContainsString('<script>', $_GET['search']);
        $this->assertStringNotContainsString('<img', $_POST['comment']);
    }

    public function testSetsSecurityHeaders(): void
    {
        // Capture headers
        $headers = [];
        $originalHeaderFunction = 'header';
        
        // Mock header function to capture headers
        if (!function_exists('test_header')) {
            function test_header($string) {
                global $testHeaders;
                $testHeaders[] = $string;
            }
        }

        $this->middleware->handle('GET', '/dashboard');

        // In a real test, you would check that security headers are set
        // For now, we'll just verify the method doesn't throw errors
        $this->assertTrue(true);
    }

    public function testHandlesRapidRequestsFromSameIp(): void
    {
        $ipAddress = '127.0.0.1';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        // Make 60 rapid requests (should trigger rapid request detection)
        for ($i = 0; $i < 60; $i++) {
            $this->security->recordAttempt("ip:{$ipAddress}", 'rapid_requests');
        }

        $result = $this->middleware->handle('GET', '/dashboard');

        // Should log suspicious activity for rapid requests
        $this->assertSecurityEventLogged('suspicious_activity');
    }

    public function testDifferentRateLimitsForDifferentEndpoints(): void
    {
        $ipAddress = '127.0.0.1';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        // Test API endpoint limit (100/hour)
        for ($i = 0; $i < 100; $i++) {
            $this->security->recordAttempt("ip:{$ipAddress}", 'get:/api/test');
        }
        $apiResult = $this->middleware->handle('GET', '/api/test');
        $this->assertEquals(429, $apiResult['status']);

        // Test auth endpoint limit (10/15min) - should still be allowed
        $authResult = $this->middleware->handle('POST', '/auth/login');
        $this->assertNull($authResult); // Should be allowed (different limit)
    }

    public function testLogsSecurityViolations(): void
    {
        $ipAddress = '*************';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        // Trigger a security violation
        $this->middleware->handleSecurityViolation('sql_injection', [
            'payload' => "'; DROP TABLE users; --"
        ]);

        // Should log the violation
        $this->assertSecurityEventLogged('sql_injection');

        // Should auto-block for severe violations
        $this->assertTrue($this->security->isIpBlocked($ipAddress));
    }

    public function testHandlesEmptyUserAgent(): void
    {
        $_SERVER['HTTP_USER_AGENT'] = '';

        $result = $this->middleware->handle('GET', '/dashboard');

        // Should log suspicious activity for missing user agent
        $this->assertSecurityEventLogged('suspicious_activity');
    }

    public function testHandlesVeryShortUserAgent(): void
    {
        $_SERVER['HTTP_USER_AGENT'] = 'Bot';

        $result = $this->middleware->handle('GET', '/dashboard');

        // Should log suspicious activity for very short user agent
        $this->assertSecurityEventLogged('suspicious_activity');
    }

    public function testPreservesValidInputData(): void
    {
        $_GET['page'] = '1';
        $_POST['title'] = 'Valid Title';
        $_POST['csrf_token'] = $this->session->generateCsrfToken();

        $this->middleware->handle('POST', '/dashboard/test');

        // Valid data should be preserved
        $this->assertEquals('1', $_GET['page']);
        $this->assertEquals('Valid Title', $_POST['title']);
    }

    public function testRateLimitHeadersIncludeCorrectValues(): void
    {
        $ipAddress = '127.0.0.1';
        $_SERVER['REMOTE_ADDR'] = $ipAddress;

        // Make requests up to limit
        for ($i = 0; $i < 200; $i++) {
            $this->security->recordAttempt("ip:{$ipAddress}", 'get:/dashboard');
        }

        $result = $this->middleware->handle('GET', '/dashboard');

        $this->assertNotNull($result);
        $this->assertEquals(429, $result['status']);
        $this->assertArrayHasKey('X-RateLimit-Limit', $result['headers']);
        $this->assertArrayHasKey('X-RateLimit-Remaining', $result['headers']);
        $this->assertArrayHasKey('X-RateLimit-Reset', $result['headers']);
        $this->assertArrayHasKey('Retry-After', $result['headers']);
    }

    public function testSecurityMiddlewarePerformance(): void
    {
        // Test that security middleware doesn't significantly impact performance
        $executionTime = $this->measureExecutionTime(function() {
            for ($i = 0; $i < 100; $i++) {
                $this->middleware->handle('GET', '/dashboard');
            }
        });

        // Should complete 100 requests in under 1 second
        $this->assertLessThan(1.0, $executionTime, 'Security middleware is too slow');
    }
}
