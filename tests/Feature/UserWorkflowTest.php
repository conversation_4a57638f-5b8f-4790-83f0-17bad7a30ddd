<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;

class UserWorkflowTest extends TestCase
{
    public function testCompleteUserRegistrationAndLoginWorkflow(): void
    {
        // Step 1: User registration
        $registrationData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => 'SecurePassword123!',
            'confirm_password' => 'SecurePassword123!',
            'first_name' => 'Test',
            'last_name' => 'User'
        ];

        $registrationResult = $this->auth->register($registrationData);
        $this->assertTrue($registrationResult['success']);

        // Step 2: User login
        $loginResult = $this->auth->login('<EMAIL>', 'SecurePassword123!');
        $this->assertTrue($loginResult['success']);

        // Step 3: Verify user session
        $this->assertTrue($this->auth->isLoggedIn());
        $currentUser = $this->auth->getCurrentUser();
        $this->assertEquals('<EMAIL>', $currentUser['email']);

        // Step 4: User logout
        $this->auth->logout();
        $this->assertFalse($this->auth->isLoggedIn());
    }

    public function testCompleteDomainManagementWorkflow(): void
    {
        // Setup: Create and login user
        $user = $this->createUser();
        $this->loginUser($user);

        // Step 1: Add domain
        $domainData = [
            'domain' => 'example.com',
            'protocol' => 'https'
        ];

        // Simulate domain addition (would normally go through controller)
        $domain = $this->createDomain($user['id'], [
            'domain' => $domainData['domain'],
            'protocol' => $domainData['protocol'],
            'verified' => false
        ]);

        $this->assertDatabaseHas('domains', [
            'user_id' => $user['id'],
            'domain' => 'example.com',
            'verified' => false
        ]);

        // Step 2: Verify domain
        $this->db->update('domains', ['verified' => true], ['id' => $domain['id']]);

        $verifiedDomain = $this->db->fetch('SELECT * FROM domains WHERE id = ?', [$domain['id']]);
        $this->assertTrue((bool)$verifiedDomain['verified']);

        // Step 3: Use domain for URL submission
        $this->assertTrue(true); // Domain is ready for use
    }

    public function testCompleteUrlSubmissionWorkflow(): void
    {
        // Setup: Create user, domain, and OAuth tokens
        $user = $this->createUser();
        $domain = $this->createDomain($user['id'], ['verified' => true]);
        $this->createOAuthToken($user['id'], 'google');
        $this->createOAuthToken($user['id'], 'bing');

        // Step 1: Submit single URL
        $url = 'https://example.com/test-page';
        $submission = $this->createUrlSubmission($user['id'], $domain['id'], [
            'url' => $url,
            'provider' => 'google',
            'status' => 'submitted'
        ]);

        $this->assertDatabaseHas('url_submissions', [
            'user_id' => $user['id'],
            'url' => $url,
            'status' => 'submitted'
        ]);

        // Step 2: Check submission status
        $submissionRecord = $this->db->fetch(
            'SELECT * FROM url_submissions WHERE id = ?',
            [$submission['id']]
        );
        $this->assertEquals('submitted', $submissionRecord['status']);

        // Step 3: View submission history
        $history = $this->db->fetchAll(
            'SELECT * FROM url_submissions WHERE user_id = ? ORDER BY created_at DESC',
            [$user['id']]
        );
        $this->assertCount(1, $history);
        $this->assertEquals($url, $history[0]['url']);
    }

    public function testOAuthConnectionWorkflow(): void
    {
        // Setup: Create user
        $user = $this->createUser();
        $this->loginUser($user);

        // Step 1: Connect Google OAuth
        $googleToken = $this->createOAuthToken($user['id'], 'google');
        
        $this->assertDatabaseHas('oauth_tokens', [
            'user_id' => $user['id'],
            'provider' => 'google'
        ]);

        // Step 2: Connect Bing OAuth
        $bingToken = $this->createOAuthToken($user['id'], 'bing');
        
        $this->assertDatabaseHas('oauth_tokens', [
            'user_id' => $user['id'],
            'provider' => 'bing'
        ]);

        // Step 3: Verify both connections are active
        $tokens = $this->db->fetchAll(
            'SELECT * FROM oauth_tokens WHERE user_id = ?',
            [$user['id']]
        );
        $this->assertCount(2, $tokens);

        $providers = array_column($tokens, 'provider');
        $this->assertContains('google', $providers);
        $this->assertContains('bing', $providers);
    }

    public function testSitemapSubmissionWorkflow(): void
    {
        // Setup: Create user and domain
        $user = $this->createUser();
        $domain = $this->createDomain($user['id'], ['verified' => true]);
        $this->createOAuthToken($user['id'], 'google');

        // Step 1: Submit sitemap
        $sitemapData = [
            'user_id' => $user['id'],
            'domain_id' => $domain['id'],
            'sitemap_url' => 'https://example.com/sitemap.xml',
            'status' => 'pending',
            'google_submitted' => true,
            'bing_submitted' => false
        ];

        $sitemapId = $this->db->insert('sitemaps', $sitemapData);

        $this->assertDatabaseHas('sitemaps', [
            'user_id' => $user['id'],
            'sitemap_url' => 'https://example.com/sitemap.xml'
        ]);

        // Step 2: Process sitemap (simulate URL extraction)
        $this->db->update('sitemaps', [
            'status' => 'processing',
            'total_urls' => 10
        ], ['id' => $sitemapId]);

        // Step 3: Complete sitemap processing
        $this->db->update('sitemaps', [
            'status' => 'completed',
            'processed_urls' => 10,
            'successful_urls' => 8,
            'failed_urls' => 2
        ], ['id' => $sitemapId]);

        $completedSitemap = $this->db->fetch('SELECT * FROM sitemaps WHERE id = ?', [$sitemapId]);
        $this->assertEquals('completed', $completedSitemap['status']);
        $this->assertEquals(10, $completedSitemap['total_urls']);
    }

    public function testSecurityWorkflow(): void
    {
        // Step 1: Normal user activity
        $user = $this->createUser();
        $this->loginUser($user);

        // Should not trigger any security measures
        $this->assertDatabaseCount('security_logs', 0);

        // Step 2: Suspicious activity
        $this->security->logSecurityEvent('suspicious_activity', [
            'user_id' => $user['id'],
            'reason' => 'multiple_failed_attempts'
        ]);

        $this->assertDatabaseHas('security_logs', [
            'user_id' => $user['id'],
            'event_type' => 'suspicious_activity'
        ]);

        // Step 3: Rate limiting
        for ($i = 0; $i < 5; $i++) {
            $this->security->recordAttempt('test-user', 'test-action');
        }

        $rateLimitResult = $this->security->checkRateLimit('test-user', 'test-action', 3, 60);
        $this->assertFalse($rateLimitResult['allowed']);
    }

    public function testPasswordResetWorkflow(): void
    {
        // Setup: Create user
        $user = $this->createUser(['email' => '<EMAIL>']);

        // Step 1: Request password reset
        $resetResult = $this->auth->generatePasswordResetToken('<EMAIL>');
        $this->assertTrue($resetResult['success']);

        $token = $resetResult['token'];
        $this->assertDatabaseHas('password_reset_tokens', [
            'user_id' => $user['id'],
            'used' => false
        ]);

        // Step 2: Reset password with token
        $newPassword = 'NewSecurePassword123!';
        $passwordResetResult = $this->auth->resetPassword($token, $newPassword);
        $this->assertTrue($passwordResetResult['success']);

        // Step 3: Verify new password works
        $loginResult = $this->auth->login('<EMAIL>', $newPassword);
        $this->assertTrue($loginResult['success']);

        // Step 4: Verify token is marked as used
        $this->assertDatabaseHas('password_reset_tokens', [
            'token' => $token,
            'used' => true
        ]);
    }

    public function testApiUsageTrackingWorkflow(): void
    {
        // Setup: Create user
        $user = $this->createUser();

        // Step 1: Record API usage
        $this->createApiUsage($user['id'], 'google', [
            'endpoint' => 'urlNotifications:publish',
            'requests_count' => 5
        ]);

        $this->createApiUsage($user['id'], 'bing', [
            'endpoint' => 'SubmitUrl',
            'requests_count' => 3
        ]);

        // Step 2: Check usage statistics
        $totalUsage = $this->db->fetch(
            'SELECT SUM(requests_count) as total FROM api_usage WHERE user_id = ?',
            [$user['id']]
        );
        $this->assertEquals(8, $totalUsage['total']);

        // Step 3: Check provider-specific usage
        $googleUsage = $this->db->fetch(
            'SELECT SUM(requests_count) as total FROM api_usage WHERE user_id = ? AND provider = "google"',
            [$user['id']]
        );
        $this->assertEquals(5, $googleUsage['total']);
    }

    public function testCompleteAdminWorkflow(): void
    {
        // Setup: Create admin user
        $admin = $this->createUser(['role' => 'admin']);
        $this->loginUser($admin);

        // Step 1: View system statistics
        $userCount = $this->db->count('users');
        $domainCount = $this->db->count('domains');
        $submissionCount = $this->db->count('url_submissions');

        $this->assertGreaterThanOrEqual(1, $userCount); // At least the admin user

        // Step 2: View security logs
        $this->createSecurityLog(['event_type' => 'admin_test']);
        
        $securityLogs = $this->db->fetchAll(
            'SELECT * FROM security_logs ORDER BY created_at DESC LIMIT 10'
        );
        $this->assertNotEmpty($securityLogs);

        // Step 3: Manage blocked IPs
        $this->security->blockIp('*************', 'Admin test block');
        
        $this->assertDatabaseHas('blocked_ips', [
            'ip_address' => '*************',
            'reason' => 'Admin test block'
        ]);
    }

    public function testErrorHandlingWorkflow(): void
    {
        // Test various error scenarios

        // Step 1: Invalid login
        $loginResult = $this->auth->login('<EMAIL>', 'wrongpassword');
        $this->assertFalse($loginResult['success']);
        $this->assertArrayHasKey('errors', $loginResult);

        // Step 2: Invalid domain submission
        $user = $this->createUser();
        $invalidDomainResult = $this->createDomain($user['id'], ['domain' => '']);
        // This would normally fail validation in the controller

        // Step 3: Rate limit exceeded
        for ($i = 0; $i < 10; $i++) {
            $this->security->recordAttempt('test-user', 'test-action');
        }
        
        $rateLimitResult = $this->security->checkRateLimit('test-user', 'test-action', 5, 60);
        $this->assertFalse($rateLimitResult['allowed']);
    }

    public function testPerformanceOfCompleteWorkflow(): void
    {
        // Test that a complete user workflow completes in reasonable time
        $executionTime = $this->measureExecutionTime(function() {
            // Complete workflow: register, login, add domain, submit URL
            $user = $this->createUser();
            $this->loginUser($user);
            $domain = $this->createDomain($user['id']);
            $this->createOAuthToken($user['id'], 'google');
            $this->createUrlSubmission($user['id'], $domain['id']);
        });

        // Should complete in under 1 second
        $this->assertLessThan(1.0, $executionTime, 'Complete workflow is too slow');
    }
}
