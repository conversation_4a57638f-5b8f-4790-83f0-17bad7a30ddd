<?php

declare(strict_types=1);

// Load Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables for testing
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Override environment variables for testing
$_ENV['APP_ENV'] = 'testing';
$_ENV['DB_NAME'] = 'seo_indexer_test';
$_ENV['LOG_LEVEL'] = 'error'; // Reduce log noise during testing

// Set up test database
setupTestDatabase();

// Helper functions for testing
function setupTestDatabase(): void
{
    $config = new \App\Utils\Config();
    
    try {
        // Connect to MySQL server (without database)
        $pdo = new PDO(
            "mysql:host={$config->get('DB_HOST')};charset=utf8mb4",
            $config->get('DB_USER'),
            $config->get('DB_PASS'),
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]
        );
        
        // Create test database if it doesn't exist
        $dbName = $config->get('DB_NAME');
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$dbName}`");
        
        // Run database migrations
        $migrationFiles = [
            __DIR__ . '/../database/schema.sql',
            __DIR__ . '/../database/security_tables.sql'
        ];
        
        foreach ($migrationFiles as $file) {
            if (file_exists($file)) {
                $sql = file_get_contents($file);
                // Split by semicolon and execute each statement
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                foreach ($statements as $statement) {
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
            }
        }
        
    } catch (PDOException $e) {
        echo "Database setup failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}

function cleanupTestDatabase(): void
{
    $config = new \App\Utils\Config();
    
    try {
        $pdo = new PDO(
            "mysql:host={$config->get('DB_HOST')};dbname={$config->get('DB_NAME')};charset=utf8mb4",
            $config->get('DB_USER'),
            $config->get('DB_PASS'),
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        
        // Get all table names
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        // Truncate all tables
        foreach ($tables as $table) {
            $pdo->exec("TRUNCATE TABLE `{$table}`");
        }
        
        // Re-enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
    } catch (PDOException $e) {
        echo "Database cleanup failed: " . $e->getMessage() . "\n";
    }
}

// Test data factory functions
function createTestUser(array $overrides = []): array
{
    $defaults = [
        'username' => 'testuser_' . uniqid(),
        'email' => 'test_' . uniqid() . '@example.com',
        'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
        'first_name' => 'Test',
        'last_name' => 'User',
        'role' => 'user',
        'status' => 'active',
        'email_verified' => true,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    return array_merge($defaults, $overrides);
}

function createTestDomain(int $userId, array $overrides = []): array
{
    $defaults = [
        'user_id' => $userId,
        'domain' => 'example-' . uniqid() . '.com',
        'protocol' => 'https',
        'verified' => true,
        'verification_token' => bin2hex(random_bytes(16)),
        'verification_method' => 'html_file',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    return array_merge($defaults, $overrides);
}

function createTestUrlSubmission(int $userId, int $domainId, array $overrides = []): array
{
    $defaults = [
        'user_id' => $userId,
        'domain_id' => $domainId,
        'url' => 'https://example.com/page-' . uniqid(),
        'provider' => 'google',
        'submission_type' => 'url',
        'status' => 'submitted',
        'submitted_at' => date('Y-m-d H:i:s'),
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    return array_merge($defaults, $overrides);
}

// Mock HTTP responses for testing
class MockHttpClient
{
    private static array $responses = [];
    
    public static function addResponse(string $url, array $response): void
    {
        self::$responses[$url] = $response;
    }
    
    public static function getResponse(string $url): ?array
    {
        return self::$responses[$url] ?? null;
    }
    
    public static function clearResponses(): void
    {
        self::$responses = [];
    }
}

// Test assertion helpers
function assertDatabaseHas(string $table, array $conditions): void
{
    $config = new \App\Utils\Config();
    $db = new \App\Services\DatabaseService($config);
    
    $record = $db->fetch(
        "SELECT * FROM {$table} WHERE " . implode(' AND ', array_map(fn($k) => "{$k} = ?", array_keys($conditions))),
        array_values($conditions)
    );
    
    if (!$record) {
        throw new \PHPUnit\Framework\AssertionFailedError(
            "Failed asserting that table '{$table}' contains record with conditions: " . json_encode($conditions)
        );
    }
}

function assertDatabaseMissing(string $table, array $conditions): void
{
    $config = new \App\Utils\Config();
    $db = new \App\Services\DatabaseService($config);
    
    $record = $db->fetch(
        "SELECT * FROM {$table} WHERE " . implode(' AND ', array_map(fn($k) => "{$k} = ?", array_keys($conditions))),
        array_values($conditions)
    );
    
    if ($record) {
        throw new \PHPUnit\Framework\AssertionFailedError(
            "Failed asserting that table '{$table}' does not contain record with conditions: " . json_encode($conditions)
        );
    }
}

function assertDatabaseCount(string $table, int $expectedCount, array $conditions = []): void
{
    $config = new \App\Utils\Config();
    $db = new \App\Services\DatabaseService($config);
    
    $actualCount = $db->count($table, $conditions);
    
    if ($actualCount !== $expectedCount) {
        throw new \PHPUnit\Framework\AssertionFailedError(
            "Failed asserting that table '{$table}' contains {$expectedCount} records. Actual count: {$actualCount}"
        );
    }
}

// Security testing helpers
function simulateAttack(string $type, array $payload = []): array
{
    switch ($type) {
        case 'sql_injection':
            return [
                'input' => "'; DROP TABLE users; --",
                'expected_blocked' => true
            ];
        case 'xss':
            return [
                'input' => '<script>alert("XSS")</script>',
                'expected_blocked' => true
            ];
        case 'csrf':
            return [
                'missing_token' => true,
                'expected_blocked' => true
            ];
        case 'brute_force':
            return [
                'attempts' => 10,
                'expected_blocked' => true
            ];
        default:
            return [];
    }
}

// Performance testing helpers
function measureExecutionTime(callable $callback): float
{
    $start = microtime(true);
    $callback();
    return microtime(true) - $start;
}

function assertExecutionTimeUnder(callable $callback, float $maxSeconds): void
{
    $executionTime = measureExecutionTime($callback);
    
    if ($executionTime > $maxSeconds) {
        throw new \PHPUnit\Framework\AssertionFailedError(
            "Execution time {$executionTime}s exceeded maximum allowed time {$maxSeconds}s"
        );
    }
}

// Register shutdown function to cleanup
register_shutdown_function(function() {
    if ($_ENV['APP_ENV'] === 'testing') {
        cleanupTestDatabase();
    }
});
