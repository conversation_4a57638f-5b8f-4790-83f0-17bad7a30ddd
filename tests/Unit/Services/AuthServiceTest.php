<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\AuthService;

class AuthServiceTest extends TestCase
{
    public function testSuccessfulLogin(): void
    {
        // Create a test user
        $user = $this->createUser([
            'email' => '<EMAIL>',
            'password_hash' => password_hash('password123', PASSWORD_DEFAULT)
        ]);

        // Attempt login
        $result = $this->auth->login('<EMAIL>', 'password123');

        // Assert successful login
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('user', $result);
        $this->assertEquals($user['email'], $result['user']['email']);

        // Assert session was created
        $this->assertTrue($this->session->isLoggedIn());
        $this->assertEquals($user['id'], $this->session->getUserId());

        // Assert last login was updated
        $updatedUser = $this->db->fetch('SELECT * FROM users WHERE id = ?', [$user['id']]);
        $this->assertNotNull($updatedUser['last_login_at']);
        $this->assertNotNull($updatedUser['last_login_ip']);
    }

    public function testFailedLoginWithInvalidCredentials(): void
    {
        $user = $this->createUser([
            'email' => '<EMAIL>',
            'password_hash' => password_hash('password123', PASSWORD_DEFAULT)
        ]);

        $result = $this->auth->login('<EMAIL>', 'wrongpassword');

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertFalse($this->session->isLoggedIn());

        // Assert failed login attempt was recorded
        $this->assertDatabaseHas('login_attempts', [
            'identifier' => '<EMAIL>',
            'success' => false
        ]);

        // Assert failed login count was incremented
        $updatedUser = $this->db->fetch('SELECT * FROM users WHERE id = ?', [$user['id']]);
        $this->assertEquals(1, $updatedUser['failed_login_attempts']);
    }

    public function testAccountLockoutAfterMultipleFailedAttempts(): void
    {
        $user = $this->createUser([
            'email' => '<EMAIL>',
            'password_hash' => password_hash('password123', PASSWORD_DEFAULT)
        ]);

        // Attempt 5 failed logins
        for ($i = 0; $i < 5; $i++) {
            $this->auth->login('<EMAIL>', 'wrongpassword');
        }

        // Check that account is locked
        $lockedUser = $this->db->fetch('SELECT * FROM users WHERE id = ?', [$user['id']]);
        $this->assertEquals(5, $lockedUser['failed_login_attempts']);
        $this->assertNotNull($lockedUser['locked_until']);

        // Attempt login with correct password should fail due to lockout
        $result = $this->auth->login('<EMAIL>', 'password123');
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('locked', $result['errors']['general']);
    }

    public function testSuccessfulRegistration(): void
    {
        $userData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'confirm_password' => 'Password123!',
            'first_name' => 'Test',
            'last_name' => 'User'
        ];

        $result = $this->auth->register($userData);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('user', $result);

        // Assert user was created in database
        $this->assertDatabaseHas('users', [
            'username' => 'testuser',
            'email' => '<EMAIL>'
        ]);

        // Assert password was hashed
        $user = $this->db->fetch('SELECT * FROM users WHERE email = ?', ['<EMAIL>']);
        $this->assertTrue(password_verify('Password123!', $user['password_hash']));
    }

    public function testRegistrationWithWeakPassword(): void
    {
        $userData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => '123',
            'confirm_password' => '123',
            'first_name' => 'Test',
            'last_name' => 'User'
        ];

        $result = $this->auth->register($userData);

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertArrayHasKey('password', $result['errors']);

        // Assert user was not created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>'
        ]);
    }

    public function testRegistrationWithMismatchedPasswords(): void
    {
        $userData = [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'confirm_password' => 'DifferentPassword123!',
            'first_name' => 'Test',
            'last_name' => 'User'
        ];

        $result = $this->auth->register($userData);

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertArrayHasKey('confirm_password', $result['errors']);
    }

    public function testRegistrationWithDuplicateEmail(): void
    {
        // Create existing user
        $this->createUser(['email' => '<EMAIL>']);

        $userData = [
            'username' => 'testuser2',
            'email' => '<EMAIL>',
            'password' => 'Password123!',
            'confirm_password' => 'Password123!',
            'first_name' => 'Test',
            'last_name' => 'User'
        ];

        $result = $this->auth->register($userData);

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertArrayHasKey('email', $result['errors']);
    }

    public function testLogout(): void
    {
        $user = $this->createUser();
        $this->loginUser($user);

        $this->assertTrue($this->session->isLoggedIn());

        $this->auth->logout();

        $this->assertFalse($this->session->isLoggedIn());
        $this->assertNull($this->session->getUserId());
    }

    public function testGetCurrentUser(): void
    {
        $user = $this->createUser();
        $this->loginUser($user);

        $currentUser = $this->auth->getCurrentUser();

        $this->assertNotNull($currentUser);
        $this->assertEquals($user['id'], $currentUser['id']);
        $this->assertEquals($user['email'], $currentUser['email']);

        // Assert sensitive data is not included
        $this->assertArrayNotHasKey('password_hash', $currentUser);
    }

    public function testGetCurrentUserWhenNotLoggedIn(): void
    {
        $currentUser = $this->auth->getCurrentUser();
        $this->assertNull($currentUser);
    }

    public function testIsLoggedIn(): void
    {
        $this->assertFalse($this->auth->isLoggedIn());

        $user = $this->createUser();
        $this->loginUser($user);

        $this->assertTrue($this->auth->isLoggedIn());
    }

    public function testRequireAuth(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Authentication required');

        $this->auth->requireAuth();
    }

    public function testRequireAuthWhenLoggedIn(): void
    {
        $user = $this->createUser();
        $this->loginUser($user);

        // Should not throw exception
        $this->auth->requireAuth();
        $this->assertTrue(true); // Assert we got here without exception
    }

    public function testRequireAdmin(): void
    {
        $user = $this->createUser(['role' => 'user']);
        $this->loginUser($user);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Admin access required');

        $this->auth->requireAdmin();
    }

    public function testRequireAdminWhenAdmin(): void
    {
        $admin = $this->createUser(['role' => 'admin']);
        $this->loginUser($admin);

        // Should not throw exception
        $this->auth->requireAdmin();
        $this->assertTrue(true); // Assert we got here without exception
    }

    public function testBruteForceProtection(): void
    {
        $user = $this->createUser([
            'email' => '<EMAIL>',
            'password_hash' => password_hash('password123', PASSWORD_DEFAULT)
        ]);

        // Simulate multiple failed attempts from same IP
        for ($i = 0; $i < 6; $i++) {
            $this->auth->login('<EMAIL>', 'wrongpassword');
        }

        // Next attempt should be blocked due to brute force protection
        $result = $this->auth->login('<EMAIL>', 'wrongpassword');
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Too many failed', $result['errors']['general']);
    }

    public function testPasswordResetTokenGeneration(): void
    {
        $user = $this->createUser(['email' => '<EMAIL>']);

        $result = $this->auth->generatePasswordResetToken('<EMAIL>');

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('token', $result);

        // Assert token was stored in database
        $this->assertDatabaseHas('password_reset_tokens', [
            'user_id' => $user['id'],
            'used' => false
        ]);
    }

    public function testPasswordResetWithValidToken(): void
    {
        $user = $this->createUser(['email' => '<EMAIL>']);
        $tokenResult = $this->auth->generatePasswordResetToken('<EMAIL>');
        $token = $tokenResult['token'];

        $result = $this->auth->resetPassword($token, 'NewPassword123!');

        $this->assertTrue($result['success']);

        // Assert password was changed
        $updatedUser = $this->db->fetch('SELECT * FROM users WHERE id = ?', [$user['id']]);
        $this->assertTrue(password_verify('NewPassword123!', $updatedUser['password_hash']));

        // Assert token was marked as used
        $this->assertDatabaseHas('password_reset_tokens', [
            'token' => $token,
            'used' => true
        ]);
    }

    public function testPasswordResetWithInvalidToken(): void
    {
        $result = $this->auth->resetPassword('invalid-token', 'NewPassword123!');

        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error', $result);
    }
}
