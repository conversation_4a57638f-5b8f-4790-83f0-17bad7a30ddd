<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use Tests\TestCase;

class SecurityServiceTest extends TestCase
{
    public function testRateLimitingAllowsRequestsWithinLimit(): void
    {
        $identifier = 'test-user';
        $action = 'test-action';
        $maxAttempts = 5;
        $windowMinutes = 60;

        // First request should be allowed
        $result = $this->security->checkRateLimit($identifier, $action, $maxAttempts, $windowMinutes);

        $this->assertTrue($result['allowed']);
        $this->assertEquals($maxAttempts, $result['max_attempts']);
        $this->assertEquals($maxAttempts - 1, $result['remaining']); // -1 because we haven't recorded this attempt yet
    }

    public function testRateLimitingBlocksRequestsOverLimit(): void
    {
        $identifier = 'test-user';
        $action = 'test-action';
        $maxAttempts = 3;
        $windowMinutes = 60;

        // Record attempts up to the limit
        for ($i = 0; $i < $maxAttempts; $i++) {
            $this->security->recordAttempt($identifier, $action);
        }

        // Next check should be blocked
        $result = $this->security->checkRateLimit($identifier, $action, $maxAttempts, $windowMinutes);

        $this->assertFalse($result['allowed']);
        $this->assertEquals(0, $result['remaining']);
        $this->assertEquals($maxAttempts, $result['current_attempts']);
    }

    public function testRateLimitingResetsAfterWindow(): void
    {
        $identifier = 'test-user';
        $action = 'test-action';
        $maxAttempts = 2;
        $windowMinutes = 1; // 1 minute window

        // Record attempts up to the limit
        for ($i = 0; $i < $maxAttempts; $i++) {
            $this->security->recordAttempt($identifier, $action);
        }

        // Should be blocked
        $result = $this->security->checkRateLimit($identifier, $action, $maxAttempts, $windowMinutes);
        $this->assertFalse($result['allowed']);

        // Simulate time passing by manually cleaning old entries
        $cutoffTime = date('Y-m-d H:i:s', time() + 120); // 2 minutes in future
        $this->db->query('DELETE FROM rate_limits WHERE created_at < ?', [$cutoffTime]);

        // Should be allowed again
        $result = $this->security->checkRateLimit($identifier, $action, $maxAttempts, $windowMinutes);
        $this->assertTrue($result['allowed']);
    }

    public function testBruteForceProtection(): void
    {
        $identifier = '<EMAIL>';
        $action = 'login';

        // Should allow initial attempts
        $result = $this->security->checkBruteForce($identifier, $action, 3, 15);
        $this->assertTrue($result['allowed']);

        // Record failed attempts
        for ($i = 0; $i < 3; $i++) {
            $this->security->recordAttempt($identifier, $action);
        }

        // Should now be blocked
        $result = $this->security->checkBruteForce($identifier, $action, 3, 15);
        $this->assertFalse($result['allowed']);
    }

    public function testIpBlocking(): void
    {
        $ipAddress = '*************';
        $reason = 'Suspicious activity';

        // IP should not be blocked initially
        $this->assertFalse($this->security->isIpBlocked($ipAddress));

        // Block the IP
        $this->security->blockIp($ipAddress, $reason, 60);

        // IP should now be blocked
        $this->assertTrue($this->security->isIpBlocked($ipAddress));

        // Assert record was created in database
        $this->assertDatabaseHas('blocked_ips', [
            'ip_address' => $ipAddress,
            'reason' => $reason
        ]);
    }

    public function testInputValidation(): void
    {
        $data = [
            'email' => '<EMAIL>',
            'age' => '25',
            'website' => 'https://example.com',
            'name' => 'John Doe'
        ];

        $rules = [
            'email' => ['required' => true, 'type' => 'email'],
            'age' => ['required' => true, 'type' => 'int'],
            'website' => ['required' => false, 'type' => 'url'],
            'name' => ['required' => true, 'min_length' => 2, 'max_length' => 50]
        ];

        $errors = $this->security->validateInput($data, $rules);

        $this->assertEmpty($errors);
    }

    public function testInputValidationWithErrors(): void
    {
        $data = [
            'email' => 'invalid-email',
            'age' => 'not-a-number',
            'website' => 'not-a-url',
            'name' => 'A' // Too short
        ];

        $rules = [
            'email' => ['required' => true, 'type' => 'email'],
            'age' => ['required' => true, 'type' => 'int'],
            'website' => ['required' => true, 'type' => 'url'],
            'name' => ['required' => true, 'min_length' => 2]
        ];

        $errors = $this->security->validateInput($data, $rules);

        $this->assertNotEmpty($errors);
        $this->assertArrayHasKey('email', $errors);
        $this->assertArrayHasKey('age', $errors);
        $this->assertArrayHasKey('website', $errors);
        $this->assertArrayHasKey('name', $errors);
    }

    public function testInputSanitization(): void
    {
        $maliciousInput = '<script>alert("XSS")</script>';
        $sanitized = $this->security->sanitizeInput($maliciousInput, 'html');

        $this->assertStringNotContainsString('<script>', $sanitized);
        $this->assertStringContainsString('&lt;script&gt;', $sanitized);
    }

    public function testPasswordHashing(): void
    {
        $password = 'TestPassword123!';
        $hash = $this->security->hashPassword($password);

        $this->assertNotEquals($password, $hash);
        $this->assertTrue($this->security->verifyPassword($password, $hash));
        $this->assertFalse($this->security->verifyPassword('WrongPassword', $hash));
    }

    public function testPasswordStrengthChecking(): void
    {
        // Test weak password
        $weakResult = $this->security->checkPasswordStrength('123');
        $this->assertEquals('weak', $weakResult['strength']);
        $this->assertNotEmpty($weakResult['feedback']);

        // Test strong password
        $strongResult = $this->security->checkPasswordStrength('StrongPassword123!');
        $this->assertEquals('strong', $strongResult['strength']);
        $this->assertGreaterThan(3, $strongResult['score']);
    }

    public function testSecureTokenGeneration(): void
    {
        $token1 = $this->security->generateSecureToken(32);
        $token2 = $this->security->generateSecureToken(32);

        $this->assertEquals(64, strlen($token1)); // 32 bytes = 64 hex chars
        $this->assertEquals(64, strlen($token2));
        $this->assertNotEquals($token1, $token2); // Should be unique
        $this->assertMatchesRegularExpression('/^[a-f0-9]+$/', $token1); // Should be hex
    }

    public function testSecurityEventLogging(): void
    {
        $eventType = 'test_event';
        $eventData = ['test' => 'data'];

        $this->security->logSecurityEvent($eventType, $eventData);

        $this->assertDatabaseHas('security_logs', [
            'event_type' => $eventType
        ]);

        // Check that event data was stored as JSON
        $log = $this->db->fetch('SELECT * FROM security_logs WHERE event_type = ?', [$eventType]);
        $decodedData = json_decode($log['event_data'], true);
        $this->assertEquals($eventData['test'], $decodedData['test']);
    }

    public function testCustomValidationRules(): void
    {
        $data = ['custom_field' => 'test_value'];
        
        $rules = [
            'custom_field' => [
                'required' => true,
                'custom' => function($value) {
                    return $value === 'test_value' ? true : 'Custom validation failed';
                }
            ]
        ];

        $errors = $this->security->validateInput($data, $rules);
        $this->assertEmpty($errors);

        // Test failing custom validation
        $data['custom_field'] = 'wrong_value';
        $errors = $this->security->validateInput($data, $rules);
        $this->assertNotEmpty($errors);
        $this->assertArrayHasKey('custom_field', $errors);
    }

    public function testPatternValidation(): void
    {
        $data = ['phone' => '******-123-4567'];
        
        $rules = [
            'phone' => [
                'required' => true,
                'pattern' => '/^\+\d{1}-\d{3}-\d{3}-\d{4}$/'
            ]
        ];

        $errors = $this->security->validateInput($data, $rules);
        $this->assertEmpty($errors);

        // Test invalid pattern
        $data['phone'] = 'invalid-phone';
        $errors = $this->security->validateInput($data, $rules);
        $this->assertNotEmpty($errors);
        $this->assertArrayHasKey('phone', $errors);
    }

    public function testRecordAttemptStoresMetadata(): void
    {
        $identifier = 'test-user';
        $action = 'test-action';
        $metadata = ['additional' => 'data'];

        $this->security->recordAttempt($identifier, $action, $metadata);

        $this->assertDatabaseHas('rate_limits', [
            'identifier' => $identifier,
            'action' => $action
        ]);

        // Check metadata was stored
        $record = $this->db->fetch(
            'SELECT * FROM rate_limits WHERE identifier = ? AND action = ?',
            [$identifier, $action]
        );
        
        $storedMetadata = json_decode($record['metadata'], true);
        $this->assertEquals($metadata['additional'], $storedMetadata['additional']);
    }

    public function testCleanupOldRateLimits(): void
    {
        $identifier = 'test-user';
        $action = 'test-action';

        // Create an old rate limit entry
        $this->db->insert('rate_limits', [
            'identifier' => $identifier,
            'action' => $action,
            'ip_address' => '127.0.0.1',
            'created_at' => date('Y-m-d H:i:s', time() - 7200) // 2 hours ago
        ]);

        // Check rate limit (this should trigger cleanup)
        $this->security->checkRateLimit($identifier, $action, 5, 60);

        // Old entry should still exist (cleanup only removes entries older than 24 hours)
        $this->assertDatabaseHas('rate_limits', [
            'identifier' => $identifier,
            'action' => $action
        ]);
    }
}
